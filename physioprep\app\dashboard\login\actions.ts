"use server";

import axios, { AxiosError } from "axios";
import { cookies } from "next/headers";
import { handleServerActionError } from "@/services/api";

export type LoginState = {
  error: string | null;
  success: boolean;
  message?: string;
};

export async function adminLogin(
  previousState: { error: string | null },
  formData: FormData
): Promise<LoginState> {
  const email = formData.get("email");
  const password = formData.get("password");

  if (!email || !password) {
    return { error: "Email and password are required", success: false };
  }

  try {
    const apiUrl = "http://localhost:5000/api";
    const response = await axios.post(`${apiUrl}/auth/admin/login`, {
      email: email,
      password: password,
    });
    console.log(response.data);

    if (response.data.success) {
      // Set admin cookies
      const cookieStore = await cookies();
      cookieStore.set("adminToken", response.data.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60, // 24 hours
      });

      if (response.data.data.refreshToken) {
        cookieStore.set("adminRefreshToken", response.data.data.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "strict",
          maxAge: 7 * 24 * 60 * 60, // 7 days
        });
      }

      cookieStore.set("isLoggedIn", "true", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60,
      });

      return { error: null, success: true, message: "Login successful" };
    }
    // If not successful, return error
    return { error: "Login failed", success: false };
  } catch (error) {
    const err = await handleServerActionError(
      error as AxiosError,
      "Failed to login"
    );
    return {
      success: false,
      error: err?.errors?.[0]?.msg || "Login failed",
    };
  }
}
