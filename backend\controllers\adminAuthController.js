const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const User = require('../models/User');
const { asyncHandler } = require('../middleware/errorHandler');
const bcrypt = require('bcryptjs');
const { validationResult } = require('express-validator');

// Generate JWT token with admin-specific claims
const generateAdminToken = (userId, role) => {
  return jwt.sign(
    { 
      userId, 
      role,
      isAdmin: true,
      tokenType: 'admin'
    }, 
    process.env.JWT_SECRET, 
    {
      expiresIn: '24h' // Shorter expiry for admin tokens for security
    }
  );
};

// Generate refresh token
const generateRefreshToken = (userId) => {
  return jwt.sign(
    { 
      userId,
      tokenType: 'refresh'
    }, 
    process.env.JWT_SECRET, 
    {
      expiresIn: '7d'
    }
  );
};

// @desc    Admin login
// @route   POST /api/auth/admin/login
// @access  Public
const adminLogin = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }

  try {
    const { email, password } = req.body;

    // Check if user exists and include password for comparison
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Invalid admin credentials' }]
      });
    }

    // Check if user has admin role
    if (user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        errors: [{ msg: 'Access denied. Admin privileges required.' }]
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Invalid admin credentials' }]
      });
    }

    // Update last login
    user.lastLogin = new Date();
    user.loginCount += 1;
    await user.save();

    // Generate tokens
    const token = generateAdminToken(user._id, user.role);
    const refreshToken = generateRefreshToken(user._id);

    // Get user object without password
    const userObj = await User.findById(user._id).select('-password').lean();
    
    res.json({
      success: true,
      data: {
        token,
        refreshToken,
        user: {
          id: userObj._id,
          name: userObj.name,
          email: userObj.email,
          role: userObj.role,
          isPremium: userObj.isPremium,
          isPremiumActive: user.isPremiumActive,
          isEmailVerified: userObj.isEmailVerified,
          preferences: userObj.preferences,
          stats: userObj.stats,
          avatar: userObj.avatar,
          premiumExpiry: userObj.premiumExpiry,
          lastLogin: userObj.lastLogin,
          loginCount: userObj.loginCount,
          createdAt: userObj.createdAt,
          updatedAt: userObj.updatedAt
        }
      }
    });
  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error during admin login' }]
    });
  }
});

// @desc    Admin logout
// @route   POST /api/auth/admin/logout
// @access  Private (Admin)
const adminLogout = asyncHandler(async (req, res) => {
  try {
    // In a more sophisticated implementation, you might want to:
    // 1. Blacklist the token
    // 2. Store logout timestamp
    // 3. Clear any session data
    
    res.json({
      success: true,
      data: {
        message: 'Admin logged out successfully'
      }
    });
  } catch (error) {
    console.error('Admin logout error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error during admin logout' }]
    });
  }
});

// @desc    Get current admin user
// @route   GET /api/auth/admin/me
// @access  Private (Admin)
const getCurrentAdmin = asyncHandler(async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password').lean();
    
    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'Admin user not found' }]
      });
    }

    // Double-check admin role
    if (user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        errors: [{ msg: 'Access denied. Admin privileges required.' }]
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          isPremium: user.isPremium,
          isPremiumActive: req.user.isPremiumActive,
          isEmailVerified: user.isEmailVerified,
          preferences: user.preferences,
          stats: user.stats,
          avatar: user.avatar,
          premiumExpiry: user.premiumExpiry,
          lastLogin: user.lastLogin,
          loginCount: user.loginCount,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    });
  } catch (error) {
    console.error('Get current admin error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error retrieving admin user' }]
    });
  }
});

// @desc    Refresh admin token
// @route   POST /api/auth/admin/refresh
// @access  Public (with valid refresh token)
const refreshAdminToken = asyncHandler(async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Refresh token is required' }]
      });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);
    
    if (decoded.tokenType !== 'refresh') {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Invalid refresh token' }]
      });
    }

    // Get user and verify admin role
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    if (user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        errors: [{ msg: 'Access denied. Admin privileges required.' }]
      });
    }

    // Generate new tokens
    const newToken = generateAdminToken(user._id, user.role);
    const newRefreshToken = generateRefreshToken(user._id);

    res.json({
      success: true,
      data: {
        token: newToken,
        refreshToken: newRefreshToken
      }
    });
  } catch (error) {
    console.error('Refresh admin token error:', error);
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Invalid or expired refresh token' }]
      });
    }
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error during token refresh' }]
    });
  }
});

module.exports = {
  adminLogin,
  adminLogout,
  getCurrentAdmin,
  refreshAdminToken
};
