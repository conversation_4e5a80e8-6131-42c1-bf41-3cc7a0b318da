"use client";

import { useState, useTransition, useMemo, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { deleteTopic } from "../actions";
import { Topic, Subject } from "@/types/types";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { EnhancedDataTable } from "../../../../components/datatable/EnhancedDataTable";
import { TopicsFilter } from "./TopicFilter";
import { createTopicColumns } from "@/components/datatable/columns/TopicsColumns";
import { topicsAtom } from "@/store/topics";
import { useAtom } from "jotai";

interface TopicsTableProps {
  topics: Topic[];
  subjects: Subject[];
}

export function TopicsTable({
  topics: initialTopics,
  subjects,
}: TopicsTableProps) {
  const router = useRouter();
  const [topics, setTopics] = useAtom(topicsAtom);
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setTopics(initialTopics);
    setHydrated(true);
  }, [initialTopics, setTopics]);

  // ...rest of your code...

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [topicToDelete, setTopicToDelete] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();
  const [filters, setFilters] = useState({
    subject: "",
    status: "",
    tier: "",
  });

  // Filter topics based on current filters
  const filteredTopics = useMemo(() => {
    return topics.filter((topic) => {
      // Subject filter
      if (filters.subject && topic.subject) {
        const subjectId =
          typeof topic.subject === "object" ? topic.subject._id : topic.subject;
        if (subjectId !== filters.subject) return false;
      }

      // Status filter
      if (filters.status !== "") {
        const isActive = filters.status === "true";
        if (topic.isActive !== isActive) return false;
      }

      // Tier filter
      if (filters.tier !== "") {
        const isPremium = filters.tier === "true";
        if (topic.isPremium !== isPremium) return false;
      }

      return true;
    });
  }, [topics, filters]);


  const handleDeleteClick = (id: string) => {
    setTopicToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (!topicToDelete) return;

    startTransition(async () => {
      try {
        const result = await deleteTopic(topicToDelete);
        if (result.success) {
          setTopics((prev) =>
            prev.filter((topic) => topic._id !== topicToDelete)
          );
          toast.success("Topic deleted successfully");
          router.refresh();
        } else {
          toast.error(result.errors[0]?.msg || "Failed to delete topic");
        }
      } catch {
        toast.error("An unexpected error occurred");
      } finally {
        setDeleteDialogOpen(false);
        setTopicToDelete(null);
      }
    });
  };

  const handleFilterChange = (newFilters: {
    subject?: string;
    status?: string;
    tier?: string;
  }) => {
    setFilters({
      subject: newFilters.subject || "",
      status: newFilters.status || "",
      tier: newFilters.tier || "",
    });
  };

  const columns = createTopicColumns(handleDeleteClick);

  if (!hydrated) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <EnhancedDataTable
        columns={columns}
        data={filteredTopics}
        searchPlaceholder="Search topics by name, description..."
        enablePagination={true}
        enableRowSelection={true}
        additionalFilters={
          <TopicsFilter
            subjects={subjects}
            onFilterChange={handleFilterChange}
          />
        }
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              topic and all associated questions.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
