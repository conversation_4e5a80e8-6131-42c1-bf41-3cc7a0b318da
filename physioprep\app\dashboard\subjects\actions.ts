"use server";

import axios from "axios";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import {
  getApiUrl,
  checkAuth,
  handleServerActionError,
  handleRedirectError,
} from "@/services/api";
import type { Subject, ApiResponse } from "@/types/types";

// Define the state types for form actions
export type SubjectFormState = {
  error: string | null;
  success: boolean;
  message?: string;
};

export type SubjectCreateData = {
  name: string;
  description: string;
  icon?: string;
  color?: string;
  order?: number;
  isActive?: boolean;
};

export type SubjectUpdateData = SubjectCreateData;

// Define admin stats type
export type AdminStats = {
  totalSubjects: number;
  activeSubjects: number;
  inactiveSubjects: number;
  totalQuestions: number;
  freeQuestions: number;
  premiumQuestions: number;
  totalTopics: number;
  activeTopics: number;
  inactiveTopics: number;
};

// Define subjects response type with admin stats
export type SubjectsResponse = {
  subjects: Subject[];
  adminStats?: AdminStats;
};

// Get all subjects
export async function getSubjects(): Promise<ApiResponse<SubjectsResponse>> {
  try {
    const authCheck = await checkAuth(true); // Require admin
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/subjects`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: {
        subjects: response.data.data || response.data,
        adminStats: response.data.adminStats,
      },
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch subjects"
    );
  }
}

// Get subject by ID
export async function getSubjectById(
  id: string
): Promise<ApiResponse<Subject>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/subjects/${id}`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: response.data.data || response.data,
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch subject"
    );
  }
}

// Create new subject
export async function createSubject(
  prevState: SubjectFormState,
  formData: FormData
): Promise<SubjectFormState> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        error: "Admin authentication required",
        success: false,
      };
    }

    // Extract form data
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const icon = formData.get("icon") as string;
    const color = formData.get("color") as string;
    const order = formData.get("order") as string;
    const isActive = formData.get("isActive") === "true";

    // Validate required fields
    if (!name || !description) {
      return {
        error: "Name and description are required",
        success: false,
      };
    }

    const subjectData: SubjectCreateData = {
      name: name.trim(),
      description: description.trim(),
      ...(icon && { icon: icon.trim() }),
      ...(color && { color: color.trim() }),
      ...(order && { order: parseInt(order) }),
      isActive,
    };

    const apiUrl = await getApiUrl();
    const response = await axios.post(`${apiUrl}/subjects`, subjectData, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
        "Content-Type": "application/json",
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/subjects");
      redirect("/dashboard/subjects");
    }

    return {
      error: response.data.errors?.[0]?.msg || "Failed to create subject",
      success: false,
    };
  } catch (error) {
    await handleRedirectError(error as Error);

    const errorResult = await handleServerActionError(
      error as Error,
      "Failed to create subject"
    );
    return {
      error: errorResult.errors[0]?.msg || "Failed to create subject",
      success: false,
    };
  }
}

// Update existing subject
export async function updateSubject(
  id: string,
  prevState: SubjectFormState,
  formData: FormData
): Promise<SubjectFormState> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        error: "Admin authentication required",
        success: false,
      };
    }

    // Extract form data
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const icon = formData.get("icon") as string;
    const color = formData.get("color") as string;
    const order = formData.get("order") as string;
    const isActive = formData.get("isActive") === "true";

    // Validate required fields
    if (!name || !description) {
      return {
        error: "Name and description are required",
        success: false,
      };
    }

    const subjectData: SubjectUpdateData = {
      name: name.trim(),
      description: description.trim(),
      ...(icon && { icon: icon.trim() }),
      ...(color && { color: color.trim() }),
      ...(order && { order: parseInt(order) }),
      isActive,
    };

    const apiUrl = await getApiUrl();
    const response = await axios.put(`${apiUrl}/subjects/${id}`, subjectData, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
        "Content-Type": "application/json",
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/subjects");
      revalidatePath(`/dashboard/subjects/${id}`);
      redirect("/dashboard/subjects");
    }

    return {
      error: response.data.errors?.[0]?.msg || "Failed to update subject",
      success: false,
    };
  } catch (error) {
    await handleRedirectError(error as Error);

    const errorResult = await handleServerActionError(
      error as Error,
      "Failed to update subject"
    );
    return {
      error: errorResult.errors[0]?.msg || "Failed to update subject",
      success: false,
    };
  }
}

// Delete subject
export async function deleteSubject(
  id: string
): Promise<ApiResponse<{ message: string }>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.delete(`${apiUrl}/subjects/${id}`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/subjects");
      revalidatePath(`/dashboard/subjects/${id}`);
      
      return {
        success: true,
        data: { message: "Subject deleted successfully" },
      };
    }

    return {
      success: false,
      errors: response.data.errors || [{ msg: "Failed to delete subject" }],
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to delete subject"
    );
  }
}
