const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to verify JWT token
const auth = async (req, res, next) => {
  // console.log(`Authenticating request: ${req.method} ${req.originalUrl}`);
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'No authentication token, access denied' }]
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    req.user = user;
    req.token = token;
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      errors: [{ msg: 'Token is not valid' }]
    });
  }
};

// Middleware to check if user is admin
const admin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      errors: [{ msg: 'Access denied. Admin privileges required.' }]
    });
  }
  next();
};

// Middleware to check if user has premium access
const premium = (req, res, next) => {
  if (!req.user.isPremiumActive()) {
    return res.status(403).json({
      success: false,
      errors: [{ msg: 'Premium access required' }]
    });
  }
  next();
};

// Middleware to check premium access for specific content
const checkPremiumAccess = (req, res, next) => {
  // If user is not authenticated, allow only free content
  if (!req.user) {
    req.allowedTiers = ['free'];
    return next();
  }

  // If user has premium access, allow all content
  if (req.user.isPremiumActive) {
    req.allowedTiers = ['free', 'premium'];
  } else {
    req.allowedTiers = ['free'];
  }

  next();
};

// Optional authentication middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');
    
    req.user = user;
    next();
  } catch (error) {
    // If token is invalid, continue without user
    req.user = null;
    next();
  }
};

// Middleware to rate limit based on user
const rateLimitByUser = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();

  return (req, res, next) => {
    const userId = req.user ? req.user._id.toString() : req.ip;
    const now = Date.now();
    
    if (!requests.has(userId)) {
      requests.set(userId, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const userRequests = requests.get(userId);
    
    if (now > userRequests.resetTime) {
      userRequests.count = 1;
      userRequests.resetTime = now + windowMs;
      return next();
    }

    if (userRequests.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        errors: [{ msg: 'Too many requests. Please try again later.' }]
      });
    }

    userRequests.count++;
    next();
  };
};

// Middleware to check if user owns resource
const checkResourceOwnership = (resourceModel, resourceIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params[resourceIdParam];
      const resource = await resourceModel.findById(resourceId);

      if (!resource) {
        return res.status(404).json({
          success: false,
          errors: [{ msg: 'Resource not found' }]
        });
      }

      // Check if user owns the resource or is admin
      if (resource.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          errors: [{ msg: 'Access denied - not resource owner' }]
        });
      }

      req.resource = resource;
      next();
    } catch (error) {
      console.error('Resource ownership check error:', error);
      return res.status(500).json({
        success: false,
        errors: [{ msg: 'Server error checking resource ownership' }]
      });
    }
  };
};

module.exports = {
  auth,
  admin,
  premium,
  checkPremiumAccess,
  optionalAuth,
  rateLimitByUser,
  checkResourceOwnership
};
