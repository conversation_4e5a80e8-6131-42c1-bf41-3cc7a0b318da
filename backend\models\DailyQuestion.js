const mongoose = require('mongoose');

const dailyQuestionSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    unique: true,
    index: true
  },
  question: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Question',
    required: true
  },
  tier: {
    type: String,
    enum: ['free', 'premium'],
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  stats: {
    totalAttempts: {
      type: Number,
      default: 0
    },
    correctAttempts: {
      type: Number,
      default: 0
    },
    uniqueUsers: {
      type: Number,
      default: 0
    }
  },
  userResponses: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    selectedOption: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    isCorrect: {
      type: Boolean,
      required: true
    },
    timeSpent: {
      type: Number,
      default: 0 // in seconds
    },
    answeredAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for accuracy percentage
dailyQuestionSchema.virtual('accuracyPercentage').get(function() {
  if (this.stats.totalAttempts === 0) return 0;
  return Math.round((this.stats.correctAttempts / this.stats.totalAttempts) * 100);
});

// Virtual for checking if date is today
dailyQuestionSchema.virtual('isToday').get(function() {
  const today = new Date();
  const questionDate = new Date(this.date);
  return today.toDateString() === questionDate.toDateString();
});

// Method to check if user has already answered
dailyQuestionSchema.methods.hasUserAnswered = function(userId) {
  return this.userResponses.some(response => 
    response.user.toString() === userId.toString()
  );
};

// Method to get user's response
dailyQuestionSchema.methods.getUserResponse = function(userId) {
  return this.userResponses.find(response => 
    response.user.toString() === userId.toString()
  );
};

// Method to submit user answer
dailyQuestionSchema.methods.submitAnswer = async function(userId, selectedOptionId, timeSpent = 0) {
  // Check if user already answered
  if (this.hasUserAnswered(userId)) {
    throw new Error('User has already answered today\'s question');
  }
  
  // Populate question to check answer
  await this.populate('question');
  
  const isCorrect = this.question.isAnswerCorrect(selectedOptionId);
  
  // Add user response
  this.userResponses.push({
    user: userId,
    selectedOption: selectedOptionId,
    isCorrect,
    timeSpent,
    answeredAt: new Date()
  });
  
  // Update stats
  this.stats.totalAttempts += 1;
  this.stats.uniqueUsers = new Set(this.userResponses.map(r => r.user.toString())).size;
  
  if (isCorrect) {
    this.stats.correctAttempts += 1;
  }
  
  // Update question stats
  await this.question.updateStats(isCorrect, timeSpent);
  
  return this.save();
};

// Static method to get today's question
dailyQuestionSchema.statics.getTodaysQuestion = function() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return this.findOne({ 
    date: today,
    isActive: true 
  }).populate('question');
};

// Static method to get question for specific date
dailyQuestionSchema.statics.getQuestionForDate = function(date) {
  const targetDate = new Date(date);
  targetDate.setHours(0, 0, 0, 0);
  
  return this.findOne({ 
    date: targetDate,
    isActive: true 
  }).populate('question');
};

// Static method to create daily question
dailyQuestionSchema.statics.createDailyQuestion = async function(questionId, tier = 'free', date = null) {
  const targetDate = date ? new Date(date) : new Date();
  targetDate.setHours(0, 0, 0, 0);
  
  // Check if question already exists for this date
  const existing = await this.findOne({ date: targetDate });
  if (existing) {
    throw new Error('Daily question already exists for this date');
  }
  
  const dailyQuestion = new this({
    date: targetDate,
    question: questionId,
    tier,
    isActive: true
  });
  
  return dailyQuestion.save();
};

// Static method to get user's daily question history
dailyQuestionSchema.statics.getUserHistory = function(userId, options = {}) {
  const { page = 1, limit = 30 } = options;
  const skip = (page - 1) * limit;
  
  return this.find({
    'userResponses.user': userId,
    isActive: true
  })
  .populate('question', 'questionText difficulty subject category')
  .populate('question.subject', 'name')
  .sort({ date: -1 })
  .skip(skip)
  .limit(limit)
  .select('date tier stats userResponses.$')
  .lean();
};

// Static method to get daily question statistics
dailyQuestionSchema.statics.getStatistics = function(options = {}) {
  const { startDate, endDate, tier } = options;
  
  const match = { isActive: true };
  
  if (startDate || endDate) {
    match.date = {};
    if (startDate) match.date.$gte = new Date(startDate);
    if (endDate) match.date.$lte = new Date(endDate);
  }
  
  if (tier) match.tier = tier;
  
  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: null,
        totalQuestions: { $sum: 1 },
        totalAttempts: { $sum: '$stats.totalAttempts' },
        totalCorrect: { $sum: '$stats.correctAttempts' },
        totalUniqueUsers: { $sum: '$stats.uniqueUsers' },
        averageAccuracy: { $avg: { $divide: ['$stats.correctAttempts', '$stats.totalAttempts'] } }
      }
    },
    {
      $project: {
        _id: 0,
        totalQuestions: 1,
        totalAttempts: 1,
        totalCorrect: 1,
        totalUniqueUsers: 1,
        averageAccuracy: { $multiply: ['$averageAccuracy', 100] }
      }
    }
  ]);
};

// Indexes for efficient queries
dailyQuestionSchema.index({ date: -1 });
dailyQuestionSchema.index({ tier: 1, date: -1 });
dailyQuestionSchema.index({ 'userResponses.user': 1 });
dailyQuestionSchema.index({ isActive: 1, date: -1 });

module.exports = mongoose.model('DailyQuestion', dailyQuestionSchema);
