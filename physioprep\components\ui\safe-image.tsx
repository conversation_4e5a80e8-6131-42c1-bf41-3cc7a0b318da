"use client";

import { useState } from "react";
import Image, { ImageProps } from "next/image";

interface SafeImageProps extends Omit<ImageProps, "onError"> {
  fallbackSrc?: string;
}

/**
 * A wrapper around Next.js Image component that handles errors gracefully
 * and works with any image source regardless of domain configuration.
 */
export function SafeImage({
  src,
  alt,
  fallbackSrc = "https://t3.ftcdn.net/jpg/04/26/53/58/360_F_426535804_YhDZZnBhA7S18jc7wTpkGTkaDVlidkB2.jpg",
  ...props
}: SafeImageProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [error, setError] = useState(false);

  return (
    <Image
      {...props}
      src={imgSrc}
      alt={alt}
      onError={() => {
        setImgSrc(fallbackSrc);
        setError(true);
      }}
      className={`${props.className || ""} ${error ? "opacity-70" : ""}`}
    />
  );
}
