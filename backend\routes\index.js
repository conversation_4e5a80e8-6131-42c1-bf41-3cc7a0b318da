const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const userRoutes = require('./user');
const quizRoutes = require('./quiz');
const subjectRoutes = require('./subject');
const dashboardRoutes = require('./dashboard');
const questionRoutes = require('./questions');
const dailyQuestionRoutes = require('./dailyQuestions');
const topicRoutes = require('./topics');

// Mount routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/quizzes', quizRoutes);
router.use('/subjects', subjectRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/questions', questionRoutes);
router.use('/daily-questions', dailyQuestionRoutes);
router.use('/topics', topicRoutes);

module.exports = router; 