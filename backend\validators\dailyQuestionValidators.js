const { check, validationResult } = require('express-validator');

// Validation middleware to check for errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// Submit daily answer validation
exports.validateSubmitDailyAnswer = [
  check('selectedOptionId')
    .isMongoId()
    .withMessage('Selected option ID must be a valid ObjectId'),
  
  check('timeSpent')
    .optional()
    .isInt({ min: 0, max: 3600 })
    .withMessage('Time spent must be between 0 and 3600 seconds'),
  
  handleValidationErrors
];

// Export validation middleware
exports.handleValidationErrors = handleValidationErrors; 