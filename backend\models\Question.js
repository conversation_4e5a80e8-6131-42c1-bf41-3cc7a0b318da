const mongoose = require("mongoose");

const questionSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true,
    trim: true,
  },
  options: [
    {
      text: {
        type: String,
        required: true,
        trim: true,
      },
      isCorrect: {
        type: Boolean,
        required: true,
      },
    },
  ],
  explanation: {
    type: String,
    required: true,
    trim: true,
  },
  topic: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Topic",
    required: true,
  },
  subject: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Subject",
    required: true,
  },
  difficulty: {
    type: String,
    enum: ["easy", "medium", "hard"],
    default: "medium",
  },
  tier: {
    type: String,
    enum: ["free", "premium"],
    default: "free",
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  usageCount: {
    type: Number,
    default: 0,
  },
  correctAnswerCount: {
    type: Number,
    default: 0,
  },
  // Additional fields for enhanced statistics
  lastAttemptDate: {
    type: Date,
    default: null
  },
  averageTimeSpent: {
    type: Number,
    default: 0 // in seconds
  },
  tags: [{
    type: String,
    trim: true
  }],
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Update the updatedAt timestamp before saving
questionSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Method to calculate success rate
questionSchema.methods.getSuccessRate = function () {
  if (this.usageCount === 0) return 0;
  return (this.correctAnswerCount / this.usageCount) * 100;
};

// Method to record an attempt
questionSchema.methods.recordAttempt = function(isCorrect = false, timeSpent = 0) {
  this.usageCount += 1;
  if (isCorrect) {
    this.correctAnswerCount += 1;
  }

  // Update average time spent
  if (timeSpent > 0) {
    const totalTime = this.averageTimeSpent * (this.usageCount - 1) + timeSpent;
    this.averageTimeSpent = Math.round(totalTime / this.usageCount);
  }

  this.lastAttemptDate = new Date();
  return this.save();
};

// Method to get difficulty as number for calculations
questionSchema.methods.getDifficultyValue = function() {
  const difficultyValues = { easy: 1, medium: 2, hard: 3 };
  return difficultyValues[this.difficulty] || 2;
};

// Virtual for isPremium (for backward compatibility)
questionSchema.virtual('isPremium').get(function() {
  return this.tier === 'premium';
});

// Validate that exactly one option is marked as correct
questionSchema.pre("save", function (next) {
  const correctOptions = this.options.filter((opt) => opt.isCorrect);
  if (correctOptions.length !== 1) {
    next(new Error("Question must have exactly one correct answer"));
  }
  next();
});

// Post-save hook to trigger topic statistics update
questionSchema.post('save', async function(doc) {
  try {
    const Topic = mongoose.model('Topic');
    const topic = await Topic.findById(doc.topic);
    if (topic) {
      // Update topic stats in background (don't await to avoid blocking)
      topic.calculateStats().catch(console.error);
    }
  } catch (error) {
    console.error('Error updating topic stats after question save:', error);
  }
});

// Post-remove hook to trigger topic statistics update
questionSchema.post('remove', async function(doc) {
  try {
    const Topic = mongoose.model('Topic');
    const topic = await Topic.findById(doc.topic);
    if (topic) {
      // Update topic stats in background
      topic.calculateStats().catch(console.error);
    }
  } catch (error) {
    console.error('Error updating topic stats after question removal:', error);
  }
});

// Index for efficient queries
questionSchema.index({ topic: 1, isActive: 1 });
questionSchema.index({ subject: 1, tier: 1 });
questionSchema.index({ difficulty: 1, tier: 1 });
questionSchema.index({ lastAttemptDate: -1 });

const Question = mongoose.model("Question", questionSchema);

module.exports = Question;
