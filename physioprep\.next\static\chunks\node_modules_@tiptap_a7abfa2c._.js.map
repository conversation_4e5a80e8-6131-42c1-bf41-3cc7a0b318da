{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/state/dist/index.js"], "sourcesContent": ["// state/index.ts\nexport * from \"prosemirror-state\";\n"], "names": [], "mappings": "AAAA,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/view/dist/index.js"], "sourcesContent": ["// view/index.ts\nexport * from \"prosemirror-view\";\n"], "names": [], "mappings": "AAAA,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/keymap/dist/index.js"], "sourcesContent": ["// keymap/index.ts\nexport * from \"prosemirror-keymap\";\n"], "names": [], "mappings": "AAAA,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/model/dist/index.js"], "sourcesContent": ["// model/index.ts\nexport * from \"prosemirror-model\";\n"], "names": [], "mappings": "AAAA,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/transform/dist/index.js"], "sourcesContent": ["// transform/index.ts\nexport * from \"prosemirror-transform\";\n"], "names": [], "mappings": "AAAA,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/commands/dist/index.js"], "sourcesContent": ["// commands/index.ts\nexport * from \"prosemirror-commands\";\n"], "names": [], "mappings": "AAAA,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/schema-list/dist/index.js"], "sourcesContent": ["// schema-list/index.ts\nexport * from \"prosemirror-schema-list\";\n"], "names": [], "mappings": "AAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/dropcursor/dist/index.js"], "sourcesContent": ["// dropcursor/index.ts\nexport * from \"prosemirror-dropcursor\";\n"], "names": [], "mappings": "AAAA,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/gapcursor/dist/index.js"], "sourcesContent": ["// gapcursor/index.ts\nexport * from \"prosemirror-gapcursor\";\n"], "names": [], "mappings": "AAAA,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/pm/history/dist/index.js"], "sourcesContent": ["// history/index.ts\nexport * from \"prosemirror-history\";\n"], "names": [], "mappings": "AAAA,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-text-style/src/text-style.ts"], "sourcesContent": ["import {\n  Mark,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface TextStyleOptions {\n  /**\n   * HTML attributes to add to the span element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n  /**\n   * When enabled, merges the styles of nested spans into the child span during HTML parsing.\n   * This prioritizes the style of the child span.\n   * Used when parsing content created in other editors.\n   * (Fix for ProseMirror's default behavior.)\n   * @default false\n   */\n  mergeNestedSpanStyles: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textStyle: {\n      /**\n       * Remove spans without inline style attributes.\n       * @example editor.commands.removeEmptyTextStyle()\n       */\n      removeEmptyTextStyle: () => ReturnType,\n    }\n  }\n}\n\nconst mergeNestedSpanStyles = (element: HTMLElement) => {\n  if (!element.children.length) { return }\n  const childSpans = element.querySelectorAll('span')\n\n  if (!childSpans) { return }\n\n  childSpans.forEach(childSpan => {\n    const childStyle = childSpan.getAttribute('style')\n    const closestParentSpanStyleOfChild = childSpan.parentElement?.closest('span')?.getAttribute('style')\n\n    childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`)\n\n  })\n}\n\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `textColor` and `backgroundColor` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nexport const TextStyle = Mark.create<TextStyleOptions>({\n  name: 'textStyle',\n\n  priority: 101,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      mergeNestedSpanStyles: false,\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'span',\n        getAttrs: element => {\n          const hasStyles = (element as HTMLElement).hasAttribute('style')\n\n          if (!hasStyles) {\n            return false\n          }\n          if (this.options.mergeNestedSpanStyles) { mergeNestedSpanStyles(element) }\n\n          return {}\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      removeEmptyTextStyle: () => ({ tr }) => {\n\n        const { selection } = tr\n\n        // Gather all of the nodes within the selection range.\n        // We would need to go through each node individually\n        // to check if it has any inline style attributes.\n        // Otherwise, calling commands.unsetMark(this.name)\n        // removes everything from all the nodes\n        // within the selection range.\n        tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n\n          // Check if it's a paragraph element, if so, skip this node as we apply\n          // the text style to inline text nodes only (span).\n          if (node.isTextblock) {\n            return true\n          }\n\n          // Check if the node has no inline style attributes.\n          // Filter out non-`textStyle` marks.\n          if (\n            !node.marks.filter(mark => mark.type === this.type).some(mark => Object.values(mark.attrs).some(value => !!value))) {\n            // Proceed with the removal of the `textStyle` mark for this node only\n            tr.removeMark(pos, pos + node.nodeSize, this.type)\n          }\n        })\n\n        return true\n      },\n    }\n  },\n\n})\n"], "names": [], "mappings": ";;;;;;AAkCA,MAAM,qBAAqB,GAAG,CAAC,OAAoB,KAAI;IACrD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE;QAAE;;IAChC,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;IAEnD,IAAI,CAAC,UAAU,EAAE;QAAE;;IAEnB,UAAU,CAAC,OAAO,EAAC,SAAS,IAAG;;QAC7B,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC;QAClD,MAAM,6BAA6B,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,SAAS,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,MAAM,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,OAAO,CAAC;QAErG,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,CAAG,EAAA,6BAA6B,CAAI,CAAA,EAAA,UAAU,CAAE,CAAA,CAAC;IAEnF,CAAC,CAAC;AACJ,CAAC;AAED;;;;CAIG,GACU,MAAA,SAAS,GAAG,4JAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,QAAQ,EAAE,GAAG;IAEb,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;YAClB,qBAAqB,EAAE,KAAK;SAC7B;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,MAAM;gBACX,QAAQ,GAAE,OAAO,IAAG;oBAClB,MAAM,SAAS,GAAI,OAAuB,CAAC,YAAY,CAAC,OAAO,CAAC;oBAEhE,IAAI,CAAC,SAAS,EAAE;wBACd,OAAO,KAAK;;oBAEd,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;wBAAE,qBAAqB,CAAC,OAAO,CAAC;;oBAExE,OAAO,CAAA,CAAE;iBACV;YACF,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,MAAM;aAAE,0KAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACjF;IAED,WAAW,GAAA;QACT,OAAO;YACL,oBAAoB,EAAE,IAAM,CAAC,EAAE,EAAE,EAAE,KAAI;oBAErC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;;;;;;;oBAQxB,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;;;wBAI9D,IAAI,IAAI,CAAC,WAAW,EAAE;4BACpB,OAAO,IAAI;;;;wBAKb,IACE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAC,IAAI,GAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAC,KAAK,GAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;;4BAEpH,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;;oBAEtD,CAAC,CAAC;oBAEF,OAAO,IAAI;iBACZ;SACF;KACF;AAEF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-color/src/color.ts"], "sourcesContent": ["import '@tiptap/extension-text-style'\n\nimport { Extension } from '@tiptap/core'\n\nexport type ColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n  */\n  types: string[],\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    color: {\n      /**\n       * Set the text color\n       * @param color The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setColor: (color: string) => ReturnType,\n\n      /**\n       * Unset the text color\n       * @example editor.commands.unsetColor()\n       */\n      unsetColor: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nexport const Color = Extension.create<ColorOptions>({\n  name: 'color',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          color: {\n            default: null,\n            parseHTML: element => element.style.color?.replace(/['\"]+/g, ''),\n            renderHTML: attributes => {\n              if (!attributes.color) {\n                return {}\n              }\n\n              return {\n                style: `color: ${attributes.color}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setColor: color => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color })\n          .run()\n      },\n      unsetColor: () => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color: null })\n          .removeEmptyTextStyle()\n          .run()\n      },\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;AAgCA;;;CAGG,GACU,MAAA,KAAK,wJAAG,YAAS,CAAC,MAAM,CAAe;IAClD,IAAI,EAAE,OAAO;IAEb,UAAU,GAAA;QACR,OAAO;YACL,KAAK,EAAE;gBAAC,WAAW;aAAC;SACrB;KACF;IAED,mBAAmB,GAAA;QACjB,OAAO;YACL;gBACE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;gBACzB,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,OAAO,EAAE,IAAI;wBACb,SAAS,GAAE,OAAO;4BAAA,IAAA;4BAAI,OAAA,CAAA,EAAA,GAAA,OAAO,CAAC,KAAK,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;wBAAA,CAAA;wBAChE,UAAU,GAAE,UAAU,IAAG;4BACvB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;gCACrB,OAAO,CAAA,CAAE;;4BAGX,OAAO;gCACL,KAAK,EAAE,CAAA,OAAA,EAAU,UAAU,CAAC,KAAK,CAAE,CAAA;6BACpC;yBACF;oBACF,CAAA;gBACF,CAAA;YACF,CAAA;SACF;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,QAAQ,GAAE,KAAK,GAAI,CAAC,EAAE,KAAK,EAAE,KAAI;oBAC/B,OAAO,KAAK,GACT,OAAO,CAAC,WAAW,EAAE;wBAAE,KAAK;oBAAA,CAAE,EAC9B,GAAG,EAAE;iBACT;YACD,UAAU,EAAE,IAAM,CAAC,EAAE,KAAK,EAAE,KAAI;oBAC9B,OAAO,KAAK,GACT,OAAO,CAAC,WAAW,EAAE;wBAAE,KAAK,EAAE,IAAI;oBAAA,CAAE,EACpC,oBAAoB,GACpB,GAAG,EAAE;iBACT;SACF;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-bubble-menu/src/bubble-menu-plugin.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-bubble-menu/src/bubble-menu.ts"], "sourcesContent": ["import {\n  Editor, isNodeSelection, isTextSelection, posToDOMRect,\n} from '@tiptap/core'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface BubbleMenuPluginProps {\n  /**\n   * The plugin key.\n   * @type {PluginKey | string}\n   * @default 'bubbleMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy.js instance.\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * The delay in milliseconds before the menu should be updated.\n   * This can be useful to prevent performance issues.\n   * @type {number}\n   * @default 250\n   */\n  updateDelay?: number\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        element: HTMLElement\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n        from: number\n        to: number\n      }) => boolean)\n    | null\n}\n\nexport type BubbleMenuViewProps = BubbleMenuPluginProps & {\n  view: EditorView\n}\n\nexport class BubbleMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  public updateDelay: number\n\n  private updateDebounceTimer: number | undefined\n\n  public shouldShow: Exclude<BubbleMenuPluginProps['shouldShow'], null> = ({\n    view,\n    state,\n    from,\n    to,\n  }) => {\n    const { doc, selection } = state\n    const { empty } = selection\n\n    // Sometime check for `empty` is not enough.\n    // Doubleclick an empty paragraph returns a node size of 2.\n    // So we check also for an empty text size.\n    const isEmptyTextBlock = !doc.textBetween(from, to).length && isTextSelection(state.selection)\n\n    // When clicking on a element inside the bubble menu the editor \"blur\" event\n    // is called and the bubble menu item is focussed. In this case we should\n    // consider the menu as part of the editor and keep showing the menu\n    const isChildOfMenu = this.element.contains(document.activeElement)\n\n    const hasEditorFocus = view.hasFocus() || isChildOfMenu\n\n    if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor,\n    element,\n    view,\n    tippyOptions = {},\n    updateDelay = 250,\n    shouldShow,\n  }: BubbleMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n    this.updateDelay = updateDelay\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.addEventListener('dragstart', this.dragstartHandler)\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  dragstartHandler = () => {\n    this.hide()\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    this.element.tabIndex = 0\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'top',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const hasValidSelection = state.selection.from !== state.selection.to\n\n    if (this.updateDelay > 0 && hasValidSelection) {\n      this.handleDebouncedUpdate(view, oldState)\n      return\n    }\n\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    this.updateHandler(view, selectionChanged, docChanged, oldState)\n  }\n\n  handleDebouncedUpdate = (view: EditorView, oldState?: EditorState) => {\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    if (!selectionChanged && !docChanged) {\n      return\n    }\n\n    if (this.updateDebounceTimer) {\n      clearTimeout(this.updateDebounceTimer)\n    }\n\n    this.updateDebounceTimer = window.setTimeout(() => {\n      this.updateHandler(view, selectionChanged, docChanged, oldState)\n    }, this.updateDelay)\n  }\n\n  updateHandler = (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => {\n    const { state, composing } = view\n    const { selection } = state\n\n    const isSame = !selectionChanged && !docChanged\n\n    if (composing || isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    // support for CellSelections\n    const { ranges } = selection\n    const from = Math.min(...ranges.map(range => range.$from.pos))\n    const to = Math.max(...ranges.map(range => range.$to.pos))\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      element: this.element,\n      view,\n      state,\n      oldState,\n      from,\n      to,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect\n        || (() => {\n          if (isNodeSelection(state.selection)) {\n            let node = view.nodeDOM(from) as HTMLElement\n\n            if (node) {\n              const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]')\n\n              if (nodeViewWrapper) {\n                node = nodeViewWrapper.firstChild as HTMLElement\n              }\n\n              if (node) {\n                return node.getBoundingClientRect()\n              }\n            }\n          }\n\n          return posToDOMRect(view, from, to)\n        }),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.removeEventListener('dragstart', this.dragstartHandler)\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const BubbleMenuPlugin = (options: BubbleMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new BubbleMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { BubbleMenuPlugin, BubbleMenuPluginProps } from './bubble-menu-plugin.js'\n\nexport type BubbleMenuOptions = Omit<BubbleMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nexport const BubbleMenu = Extension.create<BubbleMenuOptions>({\n  name: 'bubbleMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'bubbleMenu',\n      updateDelay: undefined,\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      BubbleMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        updateDelay: this.options.updateDelay,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;MA8Da,cAAc,CAAA;IA6CzB,WAAA,CAAY,EACV,MAAM,EACN,OAAO,EACP,IAAI,EACJ,YAAY,GAAG,CAAA,CAAE,EACjB,WAAW,GAAG,GAAG,EACjB,UAAU,EACU,CAAA;QA7Cf,IAAW,CAAA,WAAA,GAAG,KAAK;QAUnB,IAAA,CAAA,UAAU,GAAuD,CAAC,EACvE,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,EAAE,EACH,KAAI;YACH,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,KAAK;YAChC,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS;;;;YAK3B,MAAM,gBAAgB,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,MAAM,6JAAI,kBAAA,AAAe,EAAC,KAAK,CAAC,SAAS,CAAC;;;;YAK9F,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC;YAEnE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,aAAa;YAEvD,IAAI,CAAC,cAAc,IAAI,KAAK,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC3E,OAAO,KAAK;;YAGd,OAAO,IAAI;QACb,CAAC;QA6BD,IAAgB,CAAA,gBAAA,GAAG,MAAK;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI;QACzB,CAAC;QAED,IAAgB,CAAA,gBAAA,GAAG,MAAK;YACtB,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QAED,IAAY,CAAA,YAAA,GAAG,MAAK;;YAElB,UAAU,CAAC,IAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC;QAED,IAAA,CAAA,WAAW,GAAG,CAAC,EAAE,KAAK,EAAyB,KAAI;;YACjD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,KAAK;gBAExB;;YAGF,IAAI,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,KAAA,CAAI,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,KAAK,CAAC,aAAqB,CAAC,CAAA,EAAE;gBAC1F;;YAGF,IACE,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,MAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAC7C;gBACA;;YAGF,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QAED,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAAiB,KAAI;YACvC,IAAI,CAAC,WAAW,CAAC;gBAAE,KAAK;YAAA,CAAE,CAAC;QAC7B,CAAC;QA4CD,IAAA,CAAA,qBAAqB,GAAG,CAAC,IAAgB,EAAE,QAAsB,KAAI;YACnE,MAAM,gBAAgB,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACtE,MAAM,UAAU,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAEpD,IAAI,CAAC,gBAAgB,IAAI,CAAC,UAAU,EAAE;gBACpC;;YAGF,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC;;YAGxC,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;gBAChD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,CAAC;YAClE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC;QACtB,CAAC;QAED,IAAa,CAAA,aAAA,GAAG,CAAC,IAAgB,EAAE,gBAAyB,EAAE,UAAmB,EAAE,QAAsB,KAAI;;YAC3G,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI;YACjC,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;YAE3B,MAAM,MAAM,GAAG,CAAC,gBAAgB,IAAI,CAAC,UAAU;YAE/C,IAAI,SAAS,IAAI,MAAM,EAAE;gBACvB;;YAGF,IAAI,CAAC,aAAa,EAAE;;YAGpB,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAC,KAAK,GAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAC,KAAK,GAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAE1D,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;gBACnC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI;gBACJ,KAAK;gBACL,QAAQ;gBACR,IAAI;gBACJ,EAAE;YACH,CAAA,CAAC;YAEF,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,CAAC,IAAI,EAAE;gBAEX;;YAGF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC;gBACnB,sBAAsB,EACpB,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,sBAAsB,KACtC,CAAC,MAAK;oBACP,6JAAI,kBAAA,AAAe,EAAC,KAAK,CAAC,SAAS,CAAC,EAAE;wBACpC,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAgB;wBAE5C,IAAI,IAAI,EAAE;4BACR,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC;4BAE5G,IAAI,eAAe,EAAE;gCACnB,IAAI,GAAG,eAAe,CAAC,UAAyB;;4BAGlD,IAAI,IAAI,EAAE;gCACR,OAAO,IAAI,CAAC,qBAAqB,EAAE;;;;oBAKzC,WAAO,oKAAA,AAAY,EAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;gBACrC,CAAC,CAAC;YACL,CAAA,CAAC;YAEF,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QA7KC,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,WAAW,GAAG,WAAW;QAE9B,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,UAAU;;QAG9B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACpF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC;QAClE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,YAAY;;QAEhC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;;IAwC3C,aAAa,GAAA;QACX,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;QACtD,MAAM,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,aAAa;QAEtD,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC;QAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACnC;;QAGF,IAAI,CAAC,KAAK,8JAAG,UAAA,AAAK,EAAC,aAAa,EAAE;YAChC,QAAQ,EAAE,CAAC;YACX,sBAAsB,EAAE,IAAI;YAC5B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,QAAQ;YACrB,GAAG,IAAI,CAAC,YAAY;QACrB,CAAA,CAAC;;QAGF,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC;;;IAIjG,MAAM,CAAC,IAAgB,EAAE,QAAsB,EAAA;QAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QACtB,MAAM,iBAAiB,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAErE,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,iBAAiB,EAAE;YAC7C,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC;YAC1C;;QAGF,MAAM,gBAAgB,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACtE,MAAM,UAAU,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAEpD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,CAAC;;IAgFlE,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,OAAO,GAAA;;QACL,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,CAAC,UAAU,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,mBAAmB,CAC/D,MAAM,EACN,IAAI,CAAC,gBAAgB,CACtB;;QAEH,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACvF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;;AAE5C;AAEY,MAAA,gBAAgB,GAAG,CAAC,OAA8B,KAAI;IACjE,OAAO,6JAAI,SAAM,CAAC;QAChB,GAAG,EACD,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,GAAG,6JAAI,YAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS;QAC9F,IAAI,GAAE,IAAI,GAAI,IAAI,cAAc,CAAC;gBAAE,IAAI;gBAAE,GAAG,OAAO;YAAA,CAAE,CAAC;IACvD,CAAA,CAAC;AACJ;AClTA;;;CAGG,GACU,MAAA,UAAU,wJAAG,YAAS,CAAC,MAAM,CAAoB;IAC5D,IAAI,EAAE,YAAY;IAElB,UAAU,GAAA;QACR,OAAO;YACL,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,CAAA,CAAE;YAChB,SAAS,EAAE,YAAY;YACvB,WAAW,EAAE,SAAS;YACtB,UAAU,EAAE,IAAI;SACjB;KACF;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,OAAO,EAAE;;QAGX,OAAO;YACL,gBAAgB,CAAC;gBACf,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC7B,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;gBACvC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBACrC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;aACpC,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-floating-menu/src/floating-menu-plugin.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-floating-menu/src/floating-menu.ts"], "sourcesContent": ["import {\n  Editor, getText, getTextSerializersFromSchema, posToDOMRect,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface FloatingMenuPluginProps {\n  /**\n   * The plugin key for the floating menu.\n   * @default 'floatingMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   * @default null\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy instance.\n   * @default {}\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   * @default null\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n      }) => boolean)\n    | null\n}\n\nexport type FloatingMenuViewProps = FloatingMenuPluginProps & {\n  /**\n   * The editor view.\n   */\n  view: EditorView\n}\n\nexport class FloatingMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  private getTextContent(node:ProseMirrorNode) {\n    return getText(node, { textSerializers: getTextSerializersFromSchema(this.editor.schema) })\n  }\n\n  public shouldShow: Exclude<FloatingMenuPluginProps['shouldShow'], null> = ({ view, state }) => {\n    const { selection } = state\n    const { $anchor, empty } = selection\n    const isRootDepth = $anchor.depth === 1\n\n    const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent)\n\n    if (\n      !view.hasFocus()\n      || !empty\n      || !isRootDepth\n      || !isEmptyTextBlock\n      || !this.editor.isEditable\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor, element, view, tippyOptions = {}, shouldShow,\n  }: FloatingMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    this.element.tabIndex = 0\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'right',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const { doc, selection } = state\n    const { from, to } = selection\n    const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection)\n\n    if (isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      view,\n      state,\n      oldState,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect || (() => posToDOMRect(view, from, to)),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const FloatingMenuPlugin = (options: FloatingMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new FloatingMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { FloatingMenuPlugin, FloatingMenuPluginProps } from './floating-menu-plugin.js'\n\nexport type FloatingMenuOptions = Omit<FloatingMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nexport const FloatingMenu = Extension.create<FloatingMenuOptions>({\n  name: 'floatingMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'floatingMenu',\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      FloatingMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAwDa,gBAAgB,CAAA;IAanB,cAAc,CAAC,IAAoB,EAAA;QACzC,gKAAO,UAAA,AAAO,EAAC,IAAI,EAAE;YAAE,eAAe,2JAAE,+BAAA,AAA4B,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAAA,CAAE,CAAC;;IAuB7F,WAAA,CAAY,EACV,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,GAAG,CAAA,CAAE,EAAE,UAAU,EAC9B,CAAA;QAhCjB,IAAW,CAAA,WAAA,GAAG,KAAK;QAUnB,IAAU,CAAA,UAAA,GAAyD,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAI;YAC5F,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;YAC3B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS;YACpC,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,CAAC;YAEvC,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,OAAO,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC;YAE/L,IACE,CAAC,IAAI,CAAC,QAAQ,MACX,CAAC,SACD,CAAC,eACD,CAAC,oBACD,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAC1B;gBACA,OAAO,KAAK;;YAGd,OAAO,IAAI;QACb,CAAC;QAsBD,IAAgB,CAAA,gBAAA,GAAG,MAAK;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI;QACzB,CAAC;QAED,IAAY,CAAA,YAAA,GAAG,MAAK;;YAElB,UAAU,CAAC,IAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC;QAED,IAAA,CAAA,WAAW,GAAG,CAAC,EAAE,KAAK,EAAyB,KAAI;;YACjD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,KAAK;gBAExB;;YAGF,IAAI,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,KAAA,CAAI,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,KAAK,CAAC,aAAqB,CAAC,CAAA,EAAE;gBAC1F;;YAGF,IACE,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,MAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAC7C;gBACA;;YAGF,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QAED,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAAiB,KAAI;YACvC,IAAI,CAAC,WAAW,CAAC;gBAAE,KAAK;YAAA,CAAE,CAAC;QAC7B,CAAC;QAhDC,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI;QAEhB,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,UAAU;;QAG9B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACpF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,YAAY;;QAEhC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;;IAoC3C,aAAa,GAAA;QACX,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;QACtD,MAAM,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,aAAa;QAEtD,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC;QAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACnC;;QAGF,IAAI,CAAC,KAAK,8JAAG,UAAA,AAAK,EAAC,aAAa,EAAE;YAChC,QAAQ,EAAE,CAAC;YACX,sBAAsB,EAAE,IAAI;YAC5B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,OAAO;YAClB,WAAW,EAAE,QAAQ;YACrB,GAAG,IAAI,CAAC,YAAY;QACrB,CAAA,CAAC;;QAGF,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC;;;IAIjG,MAAM,CAAC,IAAgB,EAAE,QAAsB,EAAA;;QAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QACtB,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,KAAK;QAChC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,SAAS;QAC9B,MAAM,MAAM,GAAG,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;QAEnF,IAAI,MAAM,EAAE;YACV;;QAGF,IAAI,CAAC,aAAa,EAAE;QAEpB,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI;YACJ,KAAK;YACL,QAAQ;QACT,CAAA,CAAC;QAEF,IAAI,CAAC,UAAU,EAAE;YACf,IAAI,CAAC,IAAI,EAAE;YAEX;;QAGF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC;YACnB,sBAAsB,EACpB,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,sBAAsB,KAAA,CAAK,6JAAM,eAAA,AAAY,EAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACpF,CAAA,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE;;IAGb,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,OAAO,GAAA;;QACL,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,CAAC,UAAU,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,mBAAmB,CAC/D,MAAM,EACN,IAAI,CAAC,gBAAgB,CACtB;;QAEH,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACvF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;;AAE5C;AAEY,MAAA,kBAAkB,GAAG,CAAC,OAAgC,KAAI;IACrE,OAAO,6JAAI,SAAM,CAAC;QAChB,GAAG,EACD,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,GAAG,6JAAI,YAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS;QAC9F,IAAI,GAAE,IAAI,GAAI,IAAI,gBAAgB,CAAC;gBAAE,IAAI;gBAAE,GAAG,OAAO;YAAA,CAAE,CAAC;IACzD,CAAA,CAAC;AACJ;AC7NA;;;CAGG,GACU,MAAA,YAAY,GAAG,iKAAS,CAAC,MAAM,CAAsB;IAChE,IAAI,EAAE,cAAc;IAEpB,UAAU,GAAA;QACR,OAAO;YACL,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,CAAA,CAAE;YAChB,SAAS,EAAE,cAAc;YACzB,UAAU,EAAE,IAAI;SACjB;KACF;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,OAAO,EAAE;;QAGX,OAAO;YACL,kBAAkB,CAAC;gBACjB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC7B,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;gBACvC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;aACpC,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/use-sync-external-store/shim/index.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/EditorContent.tsx", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/fast-deep-equal/es6/react.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/use-sync-external-store/shim/with-selector.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/useEditorState.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/useEditor.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/Context.tsx", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/BubbleMenu.tsx", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/FloatingMenu.tsx", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/useReactNodeView.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/NodeViewContent.tsx", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/NodeViewWrapper.tsx", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/ReactRenderer.tsx", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/react/src/ReactNodeViewRenderer.tsx"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  ForwardedRef, forwardRef, HTMLProps, LegacyRef, MutableRefObject,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { ContentComponent, EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\n\nconst mergeRefs = <T extends HTMLDivElement>(\n  ...refs: Array<MutableRefObject<T> | LegacyRef<T> | undefined>\n) => {\n  return (node: T) => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(node)\n      } else if (ref) {\n        (ref as MutableRefObject<T | null>).current = node\n      }\n    })\n  }\n}\n\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals: React.FC<{ contentComponent: ContentComponent }> = ({\n  contentComponent,\n}) => {\n  // For performance reasons, we render the node view portals on state changes only\n  const renderers = useSyncExternalStore(\n    contentComponent.subscribe,\n    contentComponent.getSnapshot,\n    contentComponent.getServerSnapshot,\n  )\n\n  // This allows us to directly render the portals without any additional wrapper\n  return (\n    <>\n      {Object.values(renderers)}\n    </>\n  )\n}\n\nexport interface EditorContentProps extends HTMLProps<HTMLDivElement> {\n  editor: Editor | null;\n  innerRef?: ForwardedRef<HTMLDivElement | null>;\n}\n\nfunction getInstance(): ContentComponent {\n  const subscribers = new Set<() => void>()\n  let renderers: Record<string, React.ReactPortal> = {}\n\n  return {\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback: () => void) {\n      subscribers.add(callback)\n      return () => {\n        subscribers.delete(callback)\n      }\n    },\n    getSnapshot() {\n      return renderers\n    },\n    getServerSnapshot() {\n      return renderers\n    },\n    /**\n     * Adds a new NodeView Renderer to the editor.\n     */\n    setRenderer(id: string, renderer: ReactRenderer) {\n      renderers = {\n        ...renderers,\n        [id]: ReactDOM.createPortal(renderer.reactElement, renderer.element, id),\n      }\n\n      subscribers.forEach(subscriber => subscriber())\n    },\n    /**\n     * Removes a NodeView Renderer from the editor.\n     */\n    removeRenderer(id: string) {\n      const nextRenderers = { ...renderers }\n\n      delete nextRenderers[id]\n      renderers = nextRenderers\n      subscribers.forEach(subscriber => subscriber())\n    },\n  }\n}\n\nexport class PureEditorContent extends React.Component<\n  EditorContentProps,\n  { hasContentComponentInitialized: boolean }\n> {\n  editorContentRef: React.RefObject<any>\n\n  initialized: boolean\n\n  unsubscribeToContentComponent?: () => void\n\n  constructor(props: EditorContentProps) {\n    super(props)\n    this.editorContentRef = React.createRef()\n    this.initialized = false\n\n    this.state = {\n      hasContentComponentInitialized: Boolean((props.editor as EditorWithContentComponent | null)?.contentComponent),\n    }\n  }\n\n  componentDidMount() {\n    this.init()\n  }\n\n  componentDidUpdate() {\n    this.init()\n  }\n\n  init() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (editor && !editor.isDestroyed && editor.options.element) {\n      if (editor.contentComponent) {\n        return\n      }\n\n      const element = this.editorContentRef.current\n\n      element.append(...editor.options.element.childNodes)\n\n      editor.setOptions({\n        element,\n      })\n\n      editor.contentComponent = getInstance()\n\n      // Has the content component been initialized?\n      if (!this.state.hasContentComponentInitialized) {\n        // Subscribe to the content component\n        this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n          this.setState(prevState => {\n            if (!prevState.hasContentComponentInitialized) {\n              return {\n                hasContentComponentInitialized: true,\n              }\n            }\n            return prevState\n          })\n\n          // Unsubscribe to previous content component\n          if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent()\n          }\n        })\n      }\n\n      editor.createNodeViews()\n\n      this.initialized = true\n    }\n  }\n\n  componentWillUnmount() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (!editor) {\n      return\n    }\n\n    this.initialized = false\n\n    if (!editor.isDestroyed) {\n      editor.view.setProps({\n        nodeViews: {},\n      })\n    }\n\n    if (this.unsubscribeToContentComponent) {\n      this.unsubscribeToContentComponent()\n    }\n\n    editor.contentComponent = null\n\n    if (!editor.options.element.firstChild) {\n      return\n    }\n\n    const newElement = document.createElement('div')\n\n    newElement.append(...editor.options.element.childNodes)\n\n    editor.setOptions({\n      element: newElement,\n    })\n  }\n\n  render() {\n    const { editor, innerRef, ...rest } = this.props\n\n    return (\n      <>\n        <div ref={mergeRefs(innerRef, this.editorContentRef)} {...rest} />\n        {/* @ts-ignore */}\n        {editor?.contentComponent && <Portals contentComponent={editor.contentComponent} />}\n      </>\n    )\n  }\n}\n\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = forwardRef<HTMLDivElement, EditorContentProps>(\n  (props: Omit<EditorContentProps, 'innerRef'>, ref) => {\n    const key = React.useMemo(() => {\n      return Math.floor(Math.random() * 0xffffffff).toString()\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor])\n\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return React.createElement(PureEditorContent, {\n      key,\n      innerRef: ref,\n      ...props,\n    })\n  },\n)\n\nexport const EditorContent = React.memo(EditorContentWithKey)\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n  var envHasBigInt64Array = typeof BigInt64Array !== 'undefined';\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n    if ((a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      for (i of a.entries())\n        if (!equal(i[1], b.get(i[0]))) return false;\n      return true;\n    }\n\n    if ((a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      return true;\n    }\n\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        continue;\n      }\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var h=require(\"react\"),n=require(\"use-sync-external-store/shim\");function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\nexports.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return[function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\nu(function(){f.hasValue=!0;f.value=d},[d]);w(d);return d};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\nvar shim = require('use-sync-external-store/shim');\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\nvar useSyncExternalStore = shim.useSyncExternalStore;\n\n// for CommonJS interop.\n\nvar useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\nfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  // Use this to track the rendered snapshot.\n  var instRef = useRef(null);\n  var inst;\n\n  if (instRef.current === null) {\n    inst = {\n      hasValue: false,\n      value: null\n    };\n    instRef.current = inst;\n  } else {\n    inst = instRef.current;\n  }\n\n  var _useMemo = useMemo(function () {\n    // Track the memoized state using closure variables that are local to this\n    // memoized instance of a getSnapshot function. Intentionally not using a\n    // useRef hook, because that state would be shared across all concurrent\n    // copies of the hook/component.\n    var hasMemo = false;\n    var memoizedSnapshot;\n    var memoizedSelection;\n\n    var memoizedSelector = function (nextSnapshot) {\n      if (!hasMemo) {\n        // The first time the hook is called, there is no memoized result.\n        hasMemo = true;\n        memoizedSnapshot = nextSnapshot;\n\n        var _nextSelection = selector(nextSnapshot);\n\n        if (isEqual !== undefined) {\n          // Even if the selector has changed, the currently rendered selection\n          // may be equal to the new selection. We should attempt to reuse the\n          // current value if possible, to preserve downstream memoizations.\n          if (inst.hasValue) {\n            var currentSelection = inst.value;\n\n            if (isEqual(currentSelection, _nextSelection)) {\n              memoizedSelection = currentSelection;\n              return currentSelection;\n            }\n          }\n        }\n\n        memoizedSelection = _nextSelection;\n        return _nextSelection;\n      } // We may be able to reuse the previous invocation's result.\n\n\n      // We may be able to reuse the previous invocation's result.\n      var prevSnapshot = memoizedSnapshot;\n      var prevSelection = memoizedSelection;\n\n      if (objectIs(prevSnapshot, nextSnapshot)) {\n        // The snapshot is the same as last time. Reuse the previous selection.\n        return prevSelection;\n      } // The snapshot has changed, so we need to compute a new selection.\n\n\n      // The snapshot has changed, so we need to compute a new selection.\n      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n\n      // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n        return prevSelection;\n      }\n\n      memoizedSnapshot = nextSnapshot;\n      memoizedSelection = nextSelection;\n      return nextSelection;\n    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n    // Assigning this to a constant so that Flow knows it can't change.\n    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n    var getSnapshotWithSelector = function () {\n      return memoizedSelector(getSnapshot());\n    };\n\n    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    };\n    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n      getSelection = _useMemo[0],\n      getServerSelection = _useMemo[1];\n\n  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n  useEffect(function () {\n    inst.hasValue = true;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n}\n\nexports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import type { Editor } from '@tiptap/core'\nimport deepEqual from 'fast-deep-equal/es6/react'\nimport {\n  useDebugValue, useEffect, useLayoutEffect, useState,\n} from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector'\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport type EditorStateSnapshot<TEditor extends Editor | null = Editor | null> = {\n  editor: TEditor;\n  transactionNumber: number;\n};\n\nexport type UseEditorStateOptions<\n  TSelectorResult,\n  TEditor extends Editor | null = Editor | null,\n> = {\n  /**\n   * The editor instance.\n   */\n  editor: TEditor;\n  /**\n   * A selector function to determine the value to compare for re-rendering.\n   */\n  selector: (context: EditorStateSnapshot<TEditor>) => TSelectorResult;\n  /**\n   * A custom equality function to determine if the editor should re-render.\n   * @default `deepEqual` from `fast-deep-equal`\n   */\n  equalityFn?: (a: TSelectorR<PERSON>ult, b: TSelectorResult | null) => boolean;\n};\n\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager<TEditor extends Editor | null = Editor | null> {\n  private transactionNumber = 0\n\n  private lastTransactionNumber = 0\n\n  private lastSnapshot: EditorStateSnapshot<TEditor>\n\n  private editor: TEditor\n\n  private subscribers = new Set<() => void>()\n\n  constructor(initialEditor: TEditor) {\n    this.editor = initialEditor\n    this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 }\n\n    this.getSnapshot = this.getSnapshot.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.watch = this.watch.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getSnapshot(): EditorStateSnapshot<TEditor> {\n    if (this.transactionNumber === this.lastTransactionNumber) {\n      return this.lastSnapshot\n    }\n    this.lastTransactionNumber = this.transactionNumber\n    this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber }\n    return this.lastSnapshot\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): EditorStateSnapshot<null> {\n    return { editor: null, transactionNumber: 0 }\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(callback: () => void): () => void {\n    this.subscribers.add(callback)\n    return () => {\n      this.subscribers.delete(callback)\n    }\n  }\n\n  /**\n   * Watch the editor instance for changes.\n   */\n  watch(nextEditor: Editor | null): undefined | (() => void) {\n    this.editor = nextEditor as TEditor\n\n    if (this.editor) {\n      /**\n       * This will force a re-render when the editor state changes.\n       * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n       * This could be more efficient, but it's a good trade-off for now.\n       */\n      const fn = () => {\n        this.transactionNumber += 1\n        this.subscribers.forEach(callback => callback())\n      }\n\n      const currentEditor = this.editor\n\n      currentEditor.on('transaction', fn)\n      return () => {\n        currentEditor.off('transaction', fn)\n      }\n    }\n\n    return undefined\n  }\n}\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor>\n): TSelectorResult;\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor | null>\n): TSelectorResult | null;\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor> | UseEditorStateOptions<TSelectorResult, Editor | null>,\n): TSelectorResult | null {\n  const [editorStateManager] = useState(() => new EditorStateManager(options.editor))\n\n  // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n  const selectedState = useSyncExternalStoreWithSelector(\n    editorStateManager.subscribe,\n    editorStateManager.getSnapshot,\n    editorStateManager.getServerSnapshot,\n    options.selector as UseEditorStateOptions<TSelectorResult, Editor | null>['selector'],\n    options.equalityFn ?? deepEqual,\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    return editorStateManager.watch(options.editor)\n  }, [options.editor, editorStateManager])\n\n  useDebugValue(selectedState)\n\n  return selectedState\n}\n", "import { type EditorOptions, Editor } from '@tiptap/core'\nimport {\n  DependencyList,\n  MutableRefObject,\n  useDebugValue,\n  useEffect,\n  useRef,\n  useState,\n} from 'react'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { useEditorState } from './useEditorState.js'\n\nconst isDev = process.env.NODE_ENV !== 'production'\nconst isSSR = typeof window === 'undefined'\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && (window as any).next)\n\n/**\n * The options for the `useEditor` hook.\n */\nexport type UseEditorOptions = Partial<EditorOptions> & {\n  /**\n   * Whether to render the editor on the first render.\n   * If client-side rendering, set this to `true`.\n   * If server-side rendering, set this to `false`.\n   * @default true\n   */\n  immediatelyRender?: boolean;\n  /**\n   * Whether to re-render the editor on each transaction.\n   * This is legacy behavior that will be removed in future versions.\n   * @default true\n   */\n  shouldRerenderOnTransaction?: boolean;\n};\n\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n  /**\n   * The current editor instance.\n   */\n  private editor: Editor | null = null\n\n  /**\n   * The most recent options to apply to the editor.\n   */\n  private options: MutableRefObject<UseEditorOptions>\n\n  /**\n   * The subscriptions to notify when the editor instance\n   * has been created or destroyed.\n   */\n  private subscriptions = new Set<() => void>()\n\n  /**\n   * A timeout to destroy the editor if it was not mounted within a time frame.\n   */\n  private scheduledDestructionTimeout: ReturnType<typeof setTimeout> | undefined\n\n  /**\n   * Whether the editor has been mounted.\n   */\n  private isComponentMounted = false\n\n  /**\n   * The most recent dependencies array.\n   */\n  private previousDeps: DependencyList | null = null\n\n  /**\n   * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n   */\n  public instanceId = ''\n\n  constructor(options: MutableRefObject<UseEditorOptions>) {\n    this.options = options\n    this.subscriptions = new Set<() => void>()\n    this.setEditor(this.getInitialEditor())\n    this.scheduleDestroy()\n\n    this.getEditor = this.getEditor.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n    this.refreshEditorInstance = this.refreshEditorInstance.bind(this)\n    this.scheduleDestroy = this.scheduleDestroy.bind(this)\n    this.onRender = this.onRender.bind(this)\n    this.createEditor = this.createEditor.bind(this)\n  }\n\n  private setEditor(editor: Editor | null) {\n    this.editor = editor\n    this.instanceId = Math.random().toString(36).slice(2, 9)\n\n    // Notify all subscribers that the editor instance has been created\n    this.subscriptions.forEach(cb => cb())\n  }\n\n  private getInitialEditor() {\n    if (this.options.current.immediatelyRender === undefined) {\n      if (isSSR || isNext) {\n        // TODO in the next major release, we should throw an error here\n        if (isDev) {\n          /**\n           * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n           * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n           */\n          console.warn(\n            'Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.',\n          )\n        }\n\n        // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n        return null\n      }\n\n      // Default to immediately rendering when client-side rendering\n      return this.createEditor()\n    }\n\n    if (this.options.current.immediatelyRender && isSSR && isDev) {\n      // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n      throw new Error(\n        'Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.',\n      )\n    }\n\n    if (this.options.current.immediatelyRender) {\n      return this.createEditor()\n    }\n\n    return null\n  }\n\n  /**\n   * Create a new editor instance. And attach event listeners.\n   */\n  private createEditor(): Editor {\n    const optionsToApply: Partial<EditorOptions> = {\n      ...this.options.current,\n      // Always call the most recent version of the callback function by default\n      onBeforeCreate: (...args) => this.options.current.onBeforeCreate?.(...args),\n      onBlur: (...args) => this.options.current.onBlur?.(...args),\n      onCreate: (...args) => this.options.current.onCreate?.(...args),\n      onDestroy: (...args) => this.options.current.onDestroy?.(...args),\n      onFocus: (...args) => this.options.current.onFocus?.(...args),\n      onSelectionUpdate: (...args) => this.options.current.onSelectionUpdate?.(...args),\n      onTransaction: (...args) => this.options.current.onTransaction?.(...args),\n      onUpdate: (...args) => this.options.current.onUpdate?.(...args),\n      onContentError: (...args) => this.options.current.onContentError?.(...args),\n      onDrop: (...args) => this.options.current.onDrop?.(...args),\n      onPaste: (...args) => this.options.current.onPaste?.(...args),\n    }\n    const editor = new Editor(optionsToApply)\n\n    // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n\n    return editor\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getEditor(): Editor | null {\n    return this.editor\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): null {\n    return null\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(onStoreChange: () => void) {\n    this.subscriptions.add(onStoreChange)\n\n    return () => {\n      this.subscriptions.delete(onStoreChange)\n    }\n  }\n\n  static compareOptions(a: UseEditorOptions, b: UseEditorOptions) {\n    return (Object.keys(a) as (keyof UseEditorOptions)[]).every(key => {\n      if (['onCreate', 'onBeforeCreate', 'onDestroy', 'onUpdate', 'onTransaction', 'onFocus', 'onBlur', 'onSelectionUpdate', 'onContentError', 'onDrop', 'onPaste'].includes(key)) {\n        // we don't want to compare callbacks, they are always different and only registered once\n        return true\n      }\n\n      // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n      if (key === 'extensions' && a.extensions && b.extensions) {\n        if (a.extensions.length !== b.extensions.length) {\n          return false\n        }\n        return a.extensions.every((extension, index) => {\n          if (extension !== b.extensions?.[index]) {\n            return false\n          }\n          return true\n        })\n      }\n      if (a[key] !== b[key]) {\n        // if any of the options have changed, we should update the editor options\n        return false\n      }\n      return true\n    })\n  }\n\n  /**\n   * On each render, we will create, update, or destroy the editor instance.\n   * @param deps The dependencies to watch for changes\n   * @returns A cleanup function\n   */\n  onRender(deps: DependencyList) {\n    // The returned callback will run on each render\n    return () => {\n      this.isComponentMounted = true\n      // Cleanup any scheduled destructions, since we are currently rendering\n      clearTimeout(this.scheduledDestructionTimeout)\n\n      if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n        // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n        if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n          // But, the options are different, so we need to update the editor options\n          // Still, this is faster than re-creating the editor\n          this.editor.setOptions({\n            ...this.options.current,\n            editable: this.editor.isEditable,\n          })\n        }\n      } else {\n        // When the editor:\n        // - does not yet exist\n        // - is destroyed\n        // - the deps array changes\n        // We need to destroy the editor instance and re-initialize it\n        this.refreshEditorInstance(deps)\n      }\n\n      return () => {\n        this.isComponentMounted = false\n        this.scheduleDestroy()\n      }\n    }\n  }\n\n  /**\n   * Recreate the editor instance if the dependencies have changed.\n   */\n  private refreshEditorInstance(deps: DependencyList) {\n    if (this.editor && !this.editor.isDestroyed) {\n      // Editor instance already exists\n      if (this.previousDeps === null) {\n        // If lastDeps has not yet been initialized, reuse the current editor instance\n        this.previousDeps = deps\n        return\n      }\n      const depsAreEqual = this.previousDeps.length === deps.length\n        && this.previousDeps.every((dep, index) => dep === deps[index])\n\n      if (depsAreEqual) {\n        // deps exist and are equal, no need to recreate\n        return\n      }\n    }\n\n    if (this.editor && !this.editor.isDestroyed) {\n      // Destroy the editor instance if it exists\n      this.editor.destroy()\n    }\n\n    this.setEditor(this.createEditor())\n\n    // Update the lastDeps to the current deps\n    this.previousDeps = deps\n  }\n\n  /**\n   * Schedule the destruction of the editor instance.\n   * This will only destroy the editor if it was not mounted on the next tick.\n   * This is to avoid destroying the editor instance when it's actually still mounted.\n   */\n  private scheduleDestroy() {\n    const currentInstanceId = this.instanceId\n    const currentEditor = this.editor\n\n    // Wait two ticks to see if the component is still mounted\n    this.scheduledDestructionTimeout = setTimeout(() => {\n      if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n        // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n        if (currentEditor) {\n          // just re-apply options as they might have changed\n          currentEditor.setOptions(this.options.current)\n        }\n        return\n      }\n      if (currentEditor && !currentEditor.isDestroyed) {\n        currentEditor.destroy()\n        if (this.instanceId === currentInstanceId) {\n          this.setEditor(null)\n        }\n      }\n      // This allows the effect to run again between ticks\n      // which may save us from having to re-create the editor\n    }, 1)\n  }\n}\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(\n  options: UseEditorOptions & { immediatelyRender: true },\n  deps?: DependencyList\n): Editor;\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(options?: UseEditorOptions, deps?: DependencyList): Editor | null;\n\nexport function useEditor(\n  options: UseEditorOptions = {},\n  deps: DependencyList = [],\n): Editor | null {\n  const mostRecentOptions = useRef(options)\n\n  mostRecentOptions.current = options\n\n  const [instanceManager] = useState(() => new EditorInstanceManager(mostRecentOptions))\n\n  const editor = useSyncExternalStore(\n    instanceManager.subscribe,\n    instanceManager.getEditor,\n    instanceManager.getServerSnapshot,\n  )\n\n  useDebugValue(editor)\n\n  // This effect will handle creating/updating the editor instance\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(instanceManager.onRender(deps))\n\n  // The default behavior is to re-render on each transaction\n  // This is legacy behavior that will be removed in future versions\n  useEditorState({\n    editor,\n    selector: ({ transactionNumber }) => {\n      if (options.shouldRerenderOnTransaction === false) {\n        // This will prevent the editor from re-rendering on each transaction\n        return null\n      }\n\n      // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n      if (options.immediatelyRender && transactionNumber === 0) {\n        return 0\n      }\n      return transactionNumber + 1\n    },\n  })\n\n  return editor\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  createContext, HTMLAttributes, ReactNode, useContext,\n} from 'react'\n\nimport { EditorContent } from './EditorContent.js'\nimport { useEditor, UseEditorOptions } from './useEditor.js'\n\nexport type EditorContextValue = {\n  editor: Editor | null;\n}\n\nexport const EditorContext = createContext<EditorContextValue>({\n  editor: null,\n})\n\nexport const EditorConsumer = EditorContext.Consumer\n\n/**\n * A hook to get the current editor instance.\n */\nexport const useCurrentEditor = () => useContext(EditorContext)\n\nexport type EditorProviderProps = {\n  children?: ReactNode;\n  slotBefore?: ReactNode;\n  slotAfter?: ReactNode;\n  editorContainerProps?: HTMLAttributes<HTMLDivElement>;\n} & UseEditorOptions\n\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nexport function EditorProvider({\n  children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions\n}: EditorProviderProps) {\n  const editor = useEditor(editorOptions)\n\n  if (!editor) {\n    return null\n  }\n\n  return (\n    <EditorContext.Provider value={{ editor }}>\n      {slotBefore}\n      <EditorConsumer>\n        {({ editor: currentEditor }) => (\n          <EditorContent editor={currentEditor} {...editorContainerProps} />\n        )}\n      </EditorConsumer>\n      {children}\n      {slotAfter}\n    </EditorContext.Provider>\n  )\n}\n", "import { BubbleMenuPlugin, BubbleMenuPluginProps } from '@tiptap/extension-bubble-menu'\nimport React, { useEffect, useState } from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport type BubbleMenuProps = Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: BubbleMenuPluginProps['editor'] | null;\n  className?: string;\n  children: React.ReactNode;\n  updateDelay?: number;\n};\n\nexport const BubbleMenu = (props: BubbleMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = BubbleMenuPlugin({\n      updateDelay,\n      editor: menuEditor,\n      element,\n      pluginKey,\n      shouldShow,\n      tippyOptions,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [props.editor, currentEditor, element])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { FloatingMenuPlugin, FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport React, {\n  useEffect, useState,\n} from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>\n\nexport type FloatingMenuProps = Omit<Optional<FloatingMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: FloatingMenuPluginProps['editor'] | null;\n  className?: string,\n  children: React.ReactNode\n}\n\nexport const FloatingMenu = (props: FloatingMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'floatingMenu',\n      editor,\n      tippyOptions = {},\n      shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = FloatingMenuPlugin({\n      pluginKey,\n      editor: menuEditor,\n      element,\n      tippyOptions,\n      shouldShow,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [\n    props.editor,\n    currentEditor,\n    element,\n  ])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { createContext, useContext } from 'react'\n\nexport interface ReactNodeViewContextProps {\n  onDragStart: (event: DragEvent) => void,\n  nodeViewContentRef: (element: HTMLElement | null) => void,\n}\n\nexport const ReactNodeViewContext = createContext<Partial<ReactNodeViewContextProps>>({\n  onDragStart: undefined,\n})\n\nexport const useReactNodeView = () => useContext(ReactNodeViewContext)\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewContentProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewContent: React.FC<NodeViewContentProps> = props => {\n  const Tag = props.as || 'div'\n  const { nodeViewContentRef } = useReactNodeView()\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={nodeViewContentRef}\n      data-node-view-content=\"\"\n      style={{\n        whiteSpace: 'pre-wrap',\n        ...props.style,\n      }}\n    />\n  )\n}\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewWrapperProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewWrapper: React.FC<NodeViewWrapperProps> = React.forwardRef((props, ref) => {\n  const { onDragStart } = useReactNodeView()\n  const Tag = props.as || 'div'\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={ref}\n      data-node-view-wrapper=\"\"\n      onDragStart={onDragStart}\n      style={{\n        whiteSpace: 'normal',\n        ...props.style,\n      }}\n    />\n  )\n})\n", "import type { Editor } from '@tiptap/core'\nimport type {\n  ComponentClass,\n  ForwardRefExoticComponent,\n  FunctionComponent,\n  PropsWithoutRef,\n  ReactNode,\n  RefAttributes,\n} from 'react'\nimport React, { version as reactVersion } from 'react'\n\nimport { EditorWithContentComponent } from './Editor.js'\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component: any) {\n  return !!(\n    typeof Component === 'function'\n    && Component.prototype\n    && Component.prototype.isReactComponent\n  )\n}\n\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component: any) {\n  return !!(\n    typeof Component === 'object'\n    && Component.$$typeof\n    && (Component.$$typeof.toString() === 'Symbol(react.forward_ref)'\n      || Component.$$typeof.description === 'react.forward_ref')\n  )\n}\n\n/**\n * Check if a component is a memoized component.\n * @param Component\n * @returns {boolean}\n */\nfunction isMemoComponent(Component: any) {\n  return !!(\n    typeof Component === 'object'\n    && Component.$$typeof\n    && (Component.$$typeof.toString() === 'Symbol(react.memo)' || Component.$$typeof.description === 'react.memo')\n  )\n}\n\n/**\n * Check if a component can safely receive a ref prop.\n * This includes class components, forwardRef components, and memoized components\n * that wrap forwardRef or class components.\n * @param Component\n * @returns {boolean}\n */\nfunction canReceiveRef(Component: any) {\n  // Check if it's a class component\n  if (isClassComponent(Component)) {\n    return true\n  }\n\n  // Check if it's a forwardRef component\n  if (isForwardRefComponent(Component)) {\n    return true\n  }\n\n  // Check if it's a memoized component\n  if (isMemoComponent(Component)) {\n    // For memoized components, check the wrapped component\n    const wrappedComponent = Component.type\n\n    if (wrappedComponent) {\n      return isClassComponent(wrappedComponent) || isForwardRefComponent(wrappedComponent)\n    }\n  }\n\n  return false\n}\n\n/**\n * Check if we're running React 19+ by detecting if function components support ref props\n * @returns {boolean}\n */\nfunction isReact19Plus(): boolean {\n  // React 19 is detected by checking React version if available\n  // In practice, we'll use a more conservative approach and assume React 18 behavior\n  // unless we can definitively detect React 19\n  try {\n    // @ts-ignore\n    if (reactVersion) {\n      const majorVersion = parseInt(reactVersion.split('.')[0], 10)\n\n      return majorVersion >= 19\n    }\n  } catch {\n    // Fallback to React 18 behavior if we can't determine version\n  }\n  return false\n}\n\nexport interface ReactRendererOptions {\n  /**\n   * The editor instance.\n   * @type {Editor}\n   */\n  editor: Editor,\n\n  /**\n   * The props for the component.\n   * @type {Record<string, any>}\n   * @default {}\n   */\n  props?: Record<string, any>,\n\n  /**\n   * The tag name of the element.\n   * @type {string}\n   * @default 'div'\n   */\n  as?: string,\n\n  /**\n   * The class name of the element.\n   * @type {string}\n   * @default ''\n   * @example 'foo bar'\n   */\n  className?: string,\n}\n\ntype ComponentType<R, P> =\n  | ComponentClass<P>\n  | FunctionComponent<P>\n  | ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<R>>\n\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n*/\nexport class ReactRenderer<R = unknown, P extends Record<string, any> = object> {\n  id: string\n\n  editor: Editor\n\n  component: any\n\n  element: Element\n\n  props: P\n\n  reactElement: ReactNode\n\n  ref: R | null = null\n\n  /**\n   * Immediately creates element and renders the provided React component.\n   */\n  constructor(component: ComponentType<R, P>, {\n    editor,\n    props = {},\n    as = 'div',\n    className = '',\n  }: ReactRendererOptions) {\n    this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString()\n    this.component = component\n    this.editor = editor as EditorWithContentComponent\n    this.props = props as P\n    this.element = document.createElement(as)\n    this.element.classList.add('react-renderer')\n\n    if (className) {\n      this.element.classList.add(...className.split(' '))\n    }\n\n    queueMicrotask(() => {\n      this.render()\n    })\n  }\n\n  /**\n   * Render the React component.\n   */\n  render(): void {\n    const Component = this.component\n    const props = this.props\n    const editor = this.editor as EditorWithContentComponent\n\n    // Handle ref forwarding with React 18/19 compatibility\n    const isReact19 = isReact19Plus()\n    const componentCanReceiveRef = canReceiveRef(Component)\n\n    const elementProps = { ...props }\n\n    // Always remove ref if the component cannot receive it (unless React 19+)\n    if (elementProps.ref && !(isReact19 || componentCanReceiveRef)) {\n      delete elementProps.ref\n    }\n\n    // Only assign our own ref if allowed\n    if (!elementProps.ref && (isReact19 || componentCanReceiveRef)) {\n      // @ts-ignore - Setting ref prop for compatible components\n      elementProps.ref = (ref: R) => {\n        this.ref = ref\n      }\n    }\n\n    this.reactElement = <Component {...elementProps} />\n\n    editor?.contentComponent?.setRenderer(this.id, this)\n  }\n\n  /**\n   * Re-renders the React component with new props.\n   */\n  updateProps(props: Record<string, any> = {}): void {\n    this.props = {\n      ...this.props,\n      ...props,\n    }\n\n    this.render()\n  }\n\n  /**\n   * Destroy the React component.\n   */\n  destroy(): void {\n    const editor = this.editor as EditorWithContentComponent\n\n    editor?.contentComponent?.removeRenderer(this.id)\n  }\n\n  /**\n   * Update the attributes of the element that holds the React component.\n   */\n  updateAttributes(attributes: Record<string, string>): void {\n    Object.keys(attributes).forEach(key => {\n      this.element.setAttribute(key, attributes[key])\n    })\n  }\n}\n", "import type {\n  DecorationWithType,\n  Editor,\n  NodeViewRenderer,\n  NodeViewRendererOptions,\n  NodeViewRendererProps,\n} from '@tiptap/core'\nimport { getRenderedAttributes, NodeView } from '@tiptap/core'\nimport type { Node, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport type { ComponentType, NamedExoticComponent } from 'react'\nimport React, { createElement, createRef, memo } from 'react'\n\nimport { EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\nimport type { ReactNodeViewProps } from './types.js'\nimport type { ReactNodeViewContextProps } from './useReactNodeView.js'\nimport { ReactNodeViewContext } from './useReactNodeView.js'\n\nexport interface ReactNodeViewRendererOptions extends NodeViewRendererOptions {\n  /**\n   * This function is called when the node view is updated.\n   * It allows you to compare the old node with the new node and decide if the component should update.\n   */\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode;\n        oldDecorations: readonly Decoration[];\n        oldInnerDecorations: DecorationSource;\n        newNode: ProseMirrorNode;\n        newDecorations: readonly Decoration[];\n        innerDecorations: DecorationSource;\n        updateProps: () => void;\n      }) => boolean)\n    | null;\n  /**\n   * The tag name of the element wrapping the React component.\n   */\n  as?: string;\n  /**\n   * The class name of the element wrapping the React component.\n   */\n  className?: string;\n  /**\n   * Attributes that should be applied to the element wrapping the React component.\n   * If this is a function, it will be called each time the node view is updated.\n   * If this is an object, it will be applied once when the node view is mounted.\n   */\n  attrs?:\n    | Record<string, string>\n    | ((props: {\n        node: ProseMirrorNode;\n        HTMLAttributes: Record<string, any>;\n      }) => Record<string, string>);\n}\n\nexport class ReactNodeView<\n  T = HTMLElement,\n  Component extends ComponentType<ReactNodeViewProps<T>> = ComponentType<ReactNodeViewProps<T>>,\n  NodeEditor extends Editor = Editor,\n  Options extends ReactNodeViewRendererOptions = ReactNodeViewRendererOptions,\n> extends NodeView<Component, NodeEditor, Options> {\n  /**\n   * The renderer instance.\n   */\n  renderer!: ReactRenderer<unknown, ReactNodeViewProps<T>>\n\n  /**\n   * The element that holds the rich-text content of the node.\n   */\n  contentDOMElement!: HTMLElement | null\n\n  constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>) {\n    super(component, props, options)\n\n    if (!this.node.isLeaf) {\n      if (this.options.contentDOMElementTag) {\n        this.contentDOMElement = document.createElement(this.options.contentDOMElementTag)\n      } else {\n        this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div')\n      }\n\n      this.contentDOMElement.dataset.nodeViewContentReact = ''\n      this.contentDOMElement.dataset.nodeViewWrapper = ''\n\n      // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n      // With this fix it seems to work fine\n      // See: https://github.com/ueberdosis/tiptap/issues/1197\n      this.contentDOMElement.style.whiteSpace = 'inherit'\n\n      this.dom.appendChild(this.contentDOMElement)\n    }\n  }\n\n  /**\n   * Setup the React component.\n   * Called on initialization.\n   */\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n      ref: createRef<T>(),\n    } satisfies ReactNodeViewProps<T>\n\n    if (!(this.component as any).displayName) {\n      const capitalizeFirstChar = (string: string): string => {\n        return string.charAt(0).toUpperCase() + string.substring(1)\n      }\n\n      this.component.displayName = capitalizeFirstChar(this.extension.name)\n    }\n\n    const onDragStart = this.onDragStart.bind(this)\n    const nodeViewContentRef: ReactNodeViewContextProps['nodeViewContentRef'] = element => {\n      if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n        // remove the nodeViewWrapper attribute from the element\n        if (element.hasAttribute('data-node-view-wrapper')) {\n          element.removeAttribute('data-node-view-wrapper')\n        }\n        element.appendChild(this.contentDOMElement)\n      }\n    }\n    const context = { onDragStart, nodeViewContentRef }\n    const Component = this.component\n    // For performance reasons, we memoize the provider component\n    // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n    const ReactNodeViewProvider: NamedExoticComponent<ReactNodeViewProps<T>> = memo(componentProps => {\n      return (\n        <ReactNodeViewContext.Provider value={context}>\n          {createElement(Component, componentProps)}\n        </ReactNodeViewContext.Provider>\n      )\n    })\n\n    ReactNodeViewProvider.displayName = 'ReactNodeView'\n\n    let as = this.node.isInline ? 'span' : 'div'\n\n    if (this.options.as) {\n      as = this.options.as\n    }\n\n    const { className = '' } = this.options\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n\n    this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n      editor: this.editor,\n      props,\n      as,\n      className: `node-${this.node.type.name} ${className}`.trim(),\n    })\n\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n    this.updateElementAttributes()\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (\n      this.renderer.element.firstElementChild\n      && !this.renderer.element.firstElementChild?.hasAttribute('data-node-view-wrapper')\n    ) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.contentDOMElement\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(\n    node: Node,\n    decorations: readonly Decoration[],\n    innerDecorations: DecorationSource,\n  ): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.renderer.updateProps(props)\n      if (typeof this.options.attrs === 'function') {\n        this.updateElementAttributes()\n      }\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (\n      node === this.node\n      && this.decorations === decorations\n      && this.innerDecorations === innerDecorations\n    ) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    this.renderer.element.classList.add('ProseMirror-selectednode')\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    this.renderer.element.classList.remove('ProseMirror-selectednode')\n  }\n\n  /**\n   * Destroy the React component instance.\n   */\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n    this.contentDOMElement = null\n  }\n\n  /**\n   * Update the attributes of the top-level element that holds the React component.\n   * Applying the attributes defined in the `attrs` option.\n   */\n  updateElementAttributes() {\n    if (this.options.attrs) {\n      let attrsObj: Record<string, string> = {}\n\n      if (typeof this.options.attrs === 'function') {\n        const extensionAttributes = this.editor.extensionManager.attributes\n        const HTMLAttributes = getRenderedAttributes(this.node, extensionAttributes)\n\n        attrsObj = this.options.attrs({ node: this.node, HTMLAttributes })\n      } else {\n        attrsObj = this.options.attrs\n      }\n\n      this.renderer.updateAttributes(attrsObj)\n    }\n  }\n}\n\n/**\n * Create a React node view renderer.\n */\nexport function ReactNodeViewRenderer<T = HTMLElement>(\n  component: ComponentType<ReactNodeViewProps<T>>,\n  options?: Partial<ReactNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as EditorWithContentComponent).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n\n    return new ReactNodeView<T>(component, props, options)\n  }\n}\n"], "names": ["require$$0", "React", "shimModule", "require$$1", "useSyncExternalStore", "withSelectorModule", "useSyncExternalStoreWithSelector", "reactVersion"], "mappings": ";;;;;;;;;;;;;;;;;;;ACYI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDH5B,IAAI,CAAC,iKAACA,UAAgB;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,OAAO,CAAC,KAAG,CAAC,IAAA,CAAG,CAAC,KAAG,CAAC,IAAE,CAAC,GAAC,CAAC,KAAG,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,KAAG,CAAC,IAAE,CAAC,KAAG;IAAC;IAAC,IAAI,CAAC,GAAC,UAAU,KAAG,OAAO,MAAM,CAAC,EAAE,GAAC,MAAM,CAAC,EAAE,GAAC,CAAC,EAAC,CAAC,GAAC,CAAC,CAAC,QAAQ,EAAC,CAAC,GAAC,CAAC,CAAC,SAAS,EAAC,CAAC,GAAC,CAAC,CAAC,eAAe,EAAC,CAAC,GAAC,CAAC,CAAC,aAAa;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,EAAE,EAAC,CAAC,GAAC,CAAC,CAAC;YAAC,IAAI,EAAC;gBAAC,KAAK,EAAC,CAAC;gBAAC,WAAW,EAAC;YAAC;QAAC,CAAC,CAAC,EAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;QAAC,CAAC,CAAC,UAAU;YAAC,CAAC,CAAC,KAAK,GAAC,CAAC;YAAC,CAAC,CAAC,WAAW,GAAC,CAAC;YAAC,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC;gBAAC,IAAI,EAAC;YAAC,CAAC;QAAC,CAAC,EAAC;YAAC,CAAC;YAAC,CAAC;YAAC,CAAC;SAAC,CAAC;QAAC,CAAC,CAAC,UAAU;YAAC,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC;gBAAC,IAAI,EAAC;YAAC,CAAC,CAAC;YAAC,OAAO,CAAC,CAAC,UAAU;gBAAC,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC;oBAAC,IAAI,EAAC;gBAAC,CAAC;YAAC,CAAC;QAAC,CAAC,EAAC;YAAC,CAAC;SAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC;QAAC,OAAO;IAAC;IAClc,SAAS,CAAC,CAAC,CAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,CAAC,WAAW;QAAC,CAAC,GAAC,CAAC,CAAC,KAAK;QAAC,IAAG;YAAC,IAAI,CAAC,GAAC,CAAC,EAAE;YAAC,OAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;QAAC,EAAC,OAAM,CAAC,EAAC;YAAC,OAAM,CAAC;QAAC;IAAC;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,OAAO,CAAC;IAAE;IAAC,IAAI,CAAC,GAAC,WAAW,KAAG,OAAO,MAAM,IAAE,WAAW,KAAG,OAAO,MAAM,CAAC,QAAQ,IAAE,WAAW,KAAG,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,GAAC,CAAC,GAAC,CAAC;IAAC,uCAA4B,CAAA,oBAAA,GAAC,KAAK,CAAC,KAAG,CAAC,CAAC,oBAAoB,GAAC,CAAC,CAAC,oBAAoB,GAAC,CAAC;;;;;;;;;;;;;;;;ICE3U,wCAA2C;QACzC,CAAC,WAAW;YAId,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,2BAA2B,KAC/D,YACF;gBACA,8BAA8B,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAAE,CAAC;;YAE/D,IAAIC,OAAK,iKAAGD,UAAgB;YAEtC,IAAI,oBAAoB,GAAGC,OAAK,CAAC,kDAAkD;YAEnF,SAAS,KAAK,CAAC,MAAM,EAAE;gBACrB;oBACE;wBACE,IAAK,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE;4BACjH,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;;wBAGpC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;;;;YAKzC,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;gBAC3C,mDAAA;gBACA,6CAAA;gBACE;oBACE,IAAI,sBAAsB,GAAG,oBAAoB,CAAC,sBAAsB;oBACxE,IAAI,KAAK,GAAG,sBAAsB,CAAC,gBAAgB,EAAE;oBAErD,IAAI,KAAK,KAAK,EAAE,EAAE;wBAChB,MAAM,IAAI,IAAI;wBACd,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;4BAAC,KAAK;yBAAC,CAAC;qBAC5B,CAAA,+DAAA;oBAGD,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,SAAU,IAAI,EAAE;wBAC5C,OAAO,MAAM,CAAC,IAAI,CAAC;oBACzB,CAAK,CAAC,CAAC,CAAA,+CAAA;oBAEH,cAAc,CAAC,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAA,oEAAA;oBACjD,6DAAA;oBACA,gEAAA;oBAEI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,cAAc,CAAC;;;YAI1E;;;EAGA,GACA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gBAChB,OAAO,CAAC,KAAK,CAAC,IAAA,CAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,sCAAA;;;YAItE,IAAI,QAAQ,GAAG,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE;YAE/D,+CAAA;YAEA,IAAI,QAAQ,GAAGA,OAAK,CAAC,QAAQ,EACzB,SAAS,GAAGA,OAAK,CAAC,SAAS,EAC3B,eAAe,GAAGA,OAAK,CAAC,eAAe,EACvC,aAAa,GAAGA,OAAK,CAAC,aAAa;YACvC,IAAI,iBAAiB,GAAG,KAAK;YAC7B,IAAI,0BAA0B,GAAG,KAAK,CAAC,CAAA,0EAAA;YACvC,6EAAA;YACA,6EAAA;YACA,0EAAA;YACA,gEAAA;YACA,4EAAA;YACA,kBAAA;YACA,EAAA;YACA,8EAAA;YACA,8EAAA;YAEA,SAAS,oBAAoB,CAAC,SAAS,EAAE,WAAW,EACpD,8EAAA;YACA,kEAAA;YACA,sBAAA;YACA,iBAAiB,EAAE;gBACjB;oBACE,IAAI,CAAC,iBAAiB,EAAE;wBACtB,IAAIA,OAAK,CAAC,eAAe,KAAK,SAAS,EAAE;4BACvC,iBAAiB,GAAG,IAAI;4BAExB,KAAK,CAAC,gEAAgE,GAAG,6CAA6C,GAAG,gEAAgE,GAAG,yBAAyB,CAAC;;;iBAG3N;gBACH,qEAAA;gBACA,4DAAA;gBACA,sBAAA;gBAGE,IAAI,KAAK,GAAG,WAAW,EAAE;gBAEzB;oBACE,IAAI,CAAC,0BAA0B,EAAE;wBAC/B,IAAI,WAAW,GAAG,WAAW,EAAE;wBAE/B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;4BACjC,KAAK,CAAC,sEAAsE,CAAC;4BAE7E,0BAA0B,GAAG,IAAI;;;iBAGtC;gBACH,sEAAA;gBACA,4EAAA;gBACA,qBAAA;gBACA,EAAA;gBACA,4EAAA;gBACA,gEAAA;gBACA,EAAA;gBACA,4EAAA;gBACA,8EAAA;gBACA,6BAAA;gBACA,EAAA;gBACA,4EAAA;gBACA,6CAAA;gBAGE,IAAI,SAAS,GAAG,QAAQ,CAAC;oBACvB,IAAI,EAAE;wBACJ,KAAK,EAAE,KAAK;wBACZ,WAAW,EAAE;;gBAEnB,CAAG,CAAC,EACE,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EACxB,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA,6EAAA;gBACjC,wEAAA;gBACA,wBAAA;gBAGE,eAAe;wGAAC,YAAY;wBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK;wBAClB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAA,qEAAA;wBACnC,wEAAA;wBACA,yEAAA;wBACA,qCAAA;wBAEI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;4BACtC,qBAAA;4BACM,WAAW,CAAC;gCACV,IAAI,EAAE;4BACd,CAAO,CAAC;;qBAEL;uGAAE;oBAAC,SAAS;oBAAE,KAAK;oBAAE,WAAW;iBAAC,CAAC;gBACnC,SAAS;kGAAC,YAAY;wBACxB,yEAAA;wBACA,wCAAA;wBACI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;4BACtC,qBAAA;4BACM,WAAW,CAAC;gCACV,IAAI,EAAE;4BACd,CAAO,CAAC;;wBAGJ,IAAI,iBAAiB;4HAAG,YAAY;gCACxC,0EAAA;gCACA,sEAAA;gCACA,wEAAA;gCACA,+CAAA;gCACA,0EAAA;gCACA,uBAAA;gCACM,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;oCACxC,qBAAA;oCACQ,WAAW,CAAC;wCACV,IAAI,EAAE;oCAChB,CAAS,CAAC;;4BAEV,CAAK,CAAC;2HAAA,yDAAA;wBAGF,OAAO,SAAS,CAAC,iBAAiB,CAAC;oBACvC,CAAG;iGAAE;oBAAC,SAAS;iBAAC,CAAC;gBACf,aAAa,CAAC,KAAK,CAAC;gBACpB,OAAO,KAAK;;YAGd,SAAS,sBAAsB,CAAC,IAAI,EAAE;gBACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC,WAAW;gBACxC,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK;gBAE1B,IAAI;oBACF,IAAI,SAAS,GAAG,iBAAiB,EAAE;oBACnC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;iBACvC,CAAC,OAAO,KAAK,EAAE;oBACd,OAAO,IAAI;;;YAIf,SAAS,sBAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE;gBAC3E,4EAAA;gBACA,8EAAA;gBACA,kEAAA;gBACA,sBAAA;gBACE,OAAO,WAAW,EAAE;;YAGtB,IAAI,SAAS,GAAG,CAAC,CAAA,CAAE,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,KAAK,WAAW,CAAC;YAEnJ,IAAI,mBAAmB,GAAG,CAAC,SAAS;YAEpC,IAAI,IAAI,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,oBAAoB;YAC9E,IAAI,sBAAsB,GAAGA,OAAK,CAAC,oBAAoB,KAAK,SAAS,GAAGA,OAAK,CAAC,oBAAoB,GAAG,IAAI;YAE7E,oCAAA,CAAA,oBAAA,GAAG,sBAAsB;YACrD,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,0BAA0B,KAC9D,YACF;gBACA,8BAA8B,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,CAAC;;QAGxE,CAAG,GAAG;IACN;;;AC5OA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,UAAc,EAAF;;AAEzC,CAAC,MAAM;IACLC,IAAA,CAAA,OAAc,GAAGC,2CAAA,EAA6D;AAChF;;ACIA,MAAM,SAAS,GAAG,CAChB,GAAG,IAA2D,KAC5D;IACF,OAAO,CAAC,IAAO,KAAI;QACjB,IAAI,CAAC,OAAO,EAAC,GAAG,IAAG;YACjB,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;gBAC7B,GAAG,CAAC,IAAI,CAAC;mBACJ,IAAI,GAAG,EAAE;gBACb,GAAkC,CAAC,OAAO,GAAG,IAAI;;QAEtD,CAAC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;CAEG,GACH,MAAM,OAAO,GAAqD,CAAC,EACjE,gBAAgB,EACjB,KAAI;;IAEH,MAAM,SAAS,GAAGC,YAAAA,oBAAoB,CACpC,gBAAgB,CAAC,SAAS,EAC1B,gBAAgB,CAAC,WAAW,EAC5B,gBAAgB,CAAC,iBAAiB,CACnC;;IAGD,qKACE,UACG,CAAA,aAAA,+JAAA,UAAA,CAAA,QAAA,EAAA,IAAA,EAAA,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CACxB;AAEP,CAAC;AAOD,SAAS,WAAW,GAAA;IAClB,MAAM,WAAW,GAAG,IAAI,GAAG,EAAc;IACzC,IAAI,SAAS,GAAsC,CAAA,CAAE;IAErD,OAAO;QACL;;SAEG,GACH,SAAS,EAAC,QAAoB,EAAA;YAC5B,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;YACzB,OAAO,MAAK;gBACV,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC9B,CAAC;SACF;QACD,WAAW,GAAA;YACT,OAAO,SAAS;SACjB;QACD,iBAAiB,GAAA;YACf,OAAO,SAAS;SACjB;QACD;;SAEG,GACH,WAAW,EAAC,EAAU,EAAE,QAAuB,EAAA;YAC7C,SAAS,GAAG;gBACV,GAAG,SAAS;gBACZ,CAAC,EAAE,CAAA,uKAAG,UAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;aACzE;YAED,WAAW,CAAC,OAAO,EAAC,UAAU,GAAI,UAAU,EAAE,CAAC;SAChD;QACD;;SAEG,GACH,cAAc,EAAC,EAAU,EAAA;YACvB,MAAM,aAAa,GAAG;gBAAE,GAAG,SAAS;YAAA,CAAE;YAEtC,OAAO,aAAa,CAAC,EAAE,CAAC;YACxB,SAAS,GAAG,aAAa;YACzB,WAAW,CAAC,OAAO,EAAC,UAAU,GAAI,UAAU,EAAE,CAAC;SAChD;KACF;AACH;AAEa,MAAA,iBAAkB,uKAAQ,UAAK,CAAC,SAG5C,CAAA;IAOC,WAAA,CAAY,KAAyB,CAAA;;QACnC,KAAK,CAAC,KAAK,CAAC;QACZ,IAAI,CAAC,gBAAgB,iKAAG,UAAK,CAAC,SAAS,EAAE;QACzC,IAAI,CAAC,WAAW,GAAG,KAAK;QAExB,IAAI,CAAC,KAAK,GAAG;YACX,8BAA8B,EAAE,OAAO,CAAC,CAAA,EAAA,GAAC,KAAK,CAAC,MAA4C,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC;SAC/G;;IAGH,iBAAiB,GAAA;QACf,IAAI,CAAC,IAAI,EAAE;;IAGb,kBAAkB,GAAA;QAChB,IAAI,CAAC,IAAI,EAAE;;IAGb,IAAI,GAAA;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAA2C;QAErE,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;YAC3D,IAAI,MAAM,CAAC,gBAAgB,EAAE;gBAC3B;;YAGF,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAE7C,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;YAEpD,MAAM,CAAC,UAAU,CAAC;gBAChB,OAAO;YACR,CAAA,CAAC;YAEF,MAAM,CAAC,gBAAgB,GAAG,WAAW,EAAE;;YAGvC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;;gBAE9C,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;oBAC1E,IAAI,CAAC,QAAQ,EAAC,SAAS,IAAG;wBACxB,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE;4BAC7C,OAAO;gCACL,8BAA8B,EAAE,IAAI;6BACrC;;wBAEH,OAAO,SAAS;oBAClB,CAAC,CAAC;;oBAGF,IAAI,IAAI,CAAC,6BAA6B,EAAE;wBACtC,IAAI,CAAC,6BAA6B,EAAE;;gBAExC,CAAC,CAAC;;YAGJ,MAAM,CAAC,eAAe,EAAE;YAExB,IAAI,CAAC,WAAW,GAAG,IAAI;;;IAI3B,oBAAoB,GAAA;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAA2C;QAErE,IAAI,CAAC,MAAM,EAAE;YACX;;QAGF,IAAI,CAAC,WAAW,GAAG,KAAK;QAExB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB,SAAS,EAAE,CAAA,CAAE;YACd,CAAA,CAAC;;QAGJ,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACtC,IAAI,CAAC,6BAA6B,EAAE;;QAGtC,MAAM,CAAC,gBAAgB,GAAG,IAAI;QAE9B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE;YACtC;;QAGF,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;QAEhD,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;QAEvD,MAAM,CAAC,UAAU,CAAC;YAChB,OAAO,EAAE,UAAU;QACpB,CAAA,CAAC;;IAGJ,MAAM,GAAA;QACJ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK;QAEhD,qKACE,UAAA,CAAA,aAAA,+JAAA,UAAA,CAAA,QAAA,EAAA,IAAA,gKACE,UAAA,CAAA,aAAA,CAAA,KAAA,EAAA;YAAK,GAAG,EAAE,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC;YAAM,GAAA,IAAI;QAAA,CAAI,CAAA,EAEjE,CAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,gBAAgB,KAAI,wKAAA,CAAA,aAAA,CAAC,OAAO,EAAC;YAAA,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;QAAA,CAAI,CAAA,CAClF;;AAGR;AAED,0EAAA;AACA,MAAM,oBAAoB,qKAAG,aAAA,AAAU,EACrC,CAAC,KAA2C,EAAE,GAAG,KAAI;IACnD,MAAM,GAAG,iKAAG,UAAK,CAAC,OAAO;6CAAC,MAAK;YAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,QAAQ,EAAE;;QAE1D,CAAC;4CAAE;QAAC,KAAK,CAAC,MAAM;KAAC,CAAC;;IAGlB,qKAAO,UAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE;QAC5C,GAAG;QACH,QAAQ,EAAE,GAAG;QACb,GAAG,KAAK;IACT,CAAA,CAAC;AACJ,CAAC,CACF;AAEY,MAAA,aAAa,iKAAG,UAAK,CAAC,IAAI,CAAC,oBAAoB;AC9N5D,IAAA,KAAc,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;IACpC,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;IAExB,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;QAC1D,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,WAAW,EAAE,OAAO,KAAK;QAEjD,IAAI,MAAM,EAAE,CAAC,EAAE,IAAI;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACpB,MAAM,GAAG,CAAC,CAAC,MAAM;YACjB,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;YACpC,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YACtC,OAAO,IAAI;QACjB;QAGI,IAAK,AAAD,CAAE,YAAY,GAAG,IAAM,CAAC,YAAY,GAAG,CAAC,CAAE;YAC5C,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;YACnC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CACnB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YAChC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CACnB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YAC7C,OAAO,IAAI;QACjB;QAEI,IAAI,AAAC,CAAC,YAAY,GAAG,IAAM,CAAC,YAAY,GAAG,CAAC,CAAE;YAC5C,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;YACnC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CACnB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YAChC,OAAO,IAAI;QACjB;QAEI,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAClD,MAAM,GAAG,CAAC,CAAC,MAAM;YACjB,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;YACpC,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EACxB,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YACjC,OAAO,IAAI;QACjB;QAGI,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK;QACjF,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,OAAO,EAAE;QAC9E,IAAI,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE;QAElF,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACrB,MAAM,GAAG,IAAI,CAAC,MAAM;QACpB,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;QAElD,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EACxB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;QAErE,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EAAG;YAC3B,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;YAEjB,IAAI,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE;gBAIlC;YACR;YAEM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,KAAK;QAC9C;QAEI,OAAO,IAAI;IACf;IAEA,oCAAA;IACE,OAAO,CAAC,KAAG,CAAC,IAAI,CAAC,KAAG,CAAC;AACvB,CAAC;;;;;;;;;;;;;;;;;;ICrEY,IAAI,CAAC,iKAACJ,UAAgB,EAAC,CAAC,GAACG,WAAuC;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,OAAO,CAAC,KAAG,CAAC,IAAA,CAAG,CAAC,KAAG,CAAC,IAAE,CAAC,GAAC,CAAC,KAAG,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,KAAG,CAAC,IAAE,CAAC,KAAG;IAAC;IAAC,IAAI,CAAC,GAAC,UAAU,KAAG,OAAO,MAAM,CAAC,EAAE,GAAC,MAAM,CAAC,EAAE,GAAC,CAAC,EAAC,CAAC,GAAC,CAAC,CAAC,oBAAoB,EAAC,CAAC,GAAC,CAAC,CAAC,MAAM,EAAC,CAAC,GAAC,CAAC,CAAC,SAAS,EAAC,CAAC,GAAC,CAAC,CAAC,OAAO,EAAC,CAAC,GAAC,CAAC,CAAC,aAAa;IAC5Q,2BAAA,CAAA,gCAAwC,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC;QAAC,IAAG,IAAI,KAAG,CAAC,CAAC,OAAO,EAAC;YAAC,IAAI,CAAC,GAAC;gBAAC,QAAQ,EAAC,CAAC,CAAC;gBAAC,KAAK,EAAC;YAAI,CAAC;YAAC,CAAC,CAAC,OAAO,GAAC;QAAC,CAAC,MAAK,CAAC,GAAC,CAAC,CAAC,OAAO;QAAC,CAAC,GAAC,CAAC,CAAC,UAAU;YAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAAC,IAAG,CAAC,CAAC,EAAC;oBAAC,CAAC,GAAC,CAAC,CAAC;oBAAC,CAAC,GAAC,CAAC;oBAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;oBAAC,IAAG,KAAK,CAAC,KAAG,CAAC,IAAE,CAAC,CAAC,QAAQ,EAAC;wBAAC,IAAI,CAAC,GAAC,CAAC,CAAC,KAAK;wBAAC,IAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,OAAO,CAAC,GAAC;oBAAC;oBAAC,OAAO,CAAC,GAAC;gBAAC;gBAAC,CAAC,GAAC,CAAC;gBAAC,IAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;gBAAC,IAAG,KAAK,CAAC,KAAG,CAAC,IAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,OAAO,CAAC;gBAAC,CAAC,GAAC,CAAC;gBAAC,OAAO,CAAC,GAAC;YAAC;YAAC,IAAI,CAAC,GAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,GAAC,KAAK,CAAC,KAAG,CAAC,GAAC,IAAI,GAAC,CAAC;YAAC,OAAM;gBAAC,UAAU;oBAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAAC,CAAC;gBAAC,IAAI,KAAG,CAAC,GAAC,KAAK,CAAC,GAAC,UAAU;oBAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAAC,CAAC;;QAAC,CAAC,EAAC;YAAC,CAAC;YAAC,CAAC;YAAC,CAAC;YAAC,CAAC;SAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtf,CAAC,CAAC,UAAU;YAAC,CAAC,CAAC,QAAQ,GAAC,CAAC,CAAC;YAAC,CAAC,CAAC,KAAK,GAAC;QAAC,CAAC,EAAC;YAAC,CAAC;SAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC;QAAC,OAAO;IAAC,CAAC;;;;;;;;;;;;;;;;ICCzD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,WAAc,CAAF;QACvC,CAAC,WAAW;YAId,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,2BAA2B,KAC/D,YACF;gBACA,8BAA8B,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAAE,CAAC;;YAE/D,IAAIF,OAAK,iKAAGD,UAAgB;YACtC,IAAI,IAAI,GAAGG,WAAuC;YAElD;;;EAGA,GACA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gBAChB,OAAO,CAAC,KAAK,CAAC,IAAA,CAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,sCAAA;;;YAItE,IAAI,QAAQ,GAAG,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE;YAE/D,IAAI,oBAAoB,GAAG,IAAI,CAAC,oBAAoB;YAEpD,wBAAA;YAEA,IAAI,MAAM,GAAGF,OAAK,CAAC,MAAM,EACrB,SAAS,GAAGA,OAAK,CAAC,SAAS,EAC3B,OAAO,GAAGA,OAAK,CAAC,OAAO,EACvB,aAAa,GAAGA,OAAK,CAAC,aAAa,CAAC,CAAA,6EAAA;YAExC,SAAS,gCAAgC,CAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,OAAO,EAAE;gBACxG,2CAAA;gBACE,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC;gBAC1B,IAAI,IAAI;gBAER,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;oBAC5B,IAAI,GAAG;wBACL,QAAQ,EAAE,KAAK;wBACf,KAAK,EAAE;oBACb,CAAK;oBACD,OAAO,CAAC,OAAO,GAAG,IAAI;gBAC1B,CAAG,MAAM;oBACL,IAAI,GAAG,OAAO,CAAC,OAAO;;gBAGxB,IAAI,QAAQ,GAAG,OAAO;0GAAC,YAAY;wBACrC,0EAAA;wBACA,yEAAA;wBACA,wEAAA;wBACA,gCAAA;wBACI,IAAI,OAAO,GAAG,KAAK;wBACnB,IAAI,gBAAgB;wBACpB,IAAI,iBAAiB;wBAErB,IAAI,gBAAgB;mIAAG,SAAU,YAAY,EAAE;gCAC7C,IAAI,CAAC,OAAO,EAAE;oCACpB,kEAAA;oCACQ,OAAO,GAAG,IAAI;oCACd,gBAAgB,GAAG,YAAY;oCAE/B,IAAI,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC;oCAE3C,IAAI,OAAO,KAAK,SAAS,EAAE;wCACnC,qEAAA;wCACA,oEAAA;wCACA,kEAAA;wCACU,IAAI,IAAI,CAAC,QAAQ,EAAE;4CACjB,IAAI,gBAAgB,GAAG,IAAI,CAAC,KAAK;4CAEjC,IAAI,OAAO,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE;gDAC7C,iBAAiB,GAAG,gBAAgB;gDACpC,OAAO,gBAAgB;;;;oCAK7B,iBAAiB,GAAG,cAAc;oCAClC,OAAO,cAAc;iCACtB,CAAA,4DAAA;gCAGP,4DAAA;gCACM,IAAI,YAAY,GAAG,gBAAgB;gCACnC,IAAI,aAAa,GAAG,iBAAiB;gCAErC,IAAI,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE;oCAChD,uEAAA;oCACQ,OAAO,aAAa;iCACrB,CAAA,mEAAA;gCAGP,mEAAA;gCACM,IAAI,aAAa,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAA,0EAAA;gCACjD,yEAAA;gCACA,uEAAA;gCACA,oBAAA;gCAEA,0EAAA;gCACA,yEAAA;gCACA,uEAAA;gCACA,oBAAA;gCACM,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE;oCAClE,OAAO,aAAa;;gCAGtB,gBAAgB,GAAG,YAAY;gCAC/B,iBAAiB,GAAG,aAAa;gCACjC,OAAO,aAAa;4BAC1B,CAAK,CAAC;kIAAA,mEAAA;wBAGN,mEAAA;wBACI,IAAI,sBAAsB,GAAG,iBAAiB,KAAK,SAAS,GAAG,IAAI,GAAG,iBAAiB;wBAEvF,IAAI,uBAAuB;0IAAG,YAAY;gCACxC,OAAO,gBAAgB,CAAC,WAAW,EAAE,CAAC;4BAC5C,CAAK;;wBAED,IAAI,6BAA6B,GAAG,sBAAsB,KAAK,IAAI,GAAG,SAAS;kHAAG,YAAY;gCAC5F,OAAO,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;4BACvD,CAAK;;wBACD,OAAO;4BAAC,uBAAuB;4BAAE,6BAA6B;yBAAC;qBAChE;yGAAE;oBAAC,WAAW;oBAAE,iBAAiB;oBAAE,QAAQ;oBAAE,OAAO;iBAAC,CAAC,EACnD,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,EAC1B,kBAAkB,GAAG,QAAQ,CAAC,CAAC,CAAC;gBAEpC,IAAI,KAAK,GAAG,oBAAoB,CAAC,SAAS,EAAE,YAAY,EAAE,kBAAkB,CAAC;gBAC7E,SAAS;kGAAC,YAAY;wBACpB,IAAI,CAAC,QAAQ,GAAG,IAAI;wBACpB,IAAI,CAAC,KAAK,GAAG,KAAK;oBACtB,CAAG;iGAAE;oBAAC,KAAK;iBAAC,CAAC;gBACX,aAAa,CAAC,KAAK,CAAC;gBACpB,OAAO,KAAK;;YAG0B,wBAAA,CAAA,gCAAA,GAAG,gCAAgC;YAC3E,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,0BAA0B,KAC9D,YACF;gBACA,8BAA8B,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,CAAC;;QAGxE,CAAG,GAAG;IACN;;;AClKA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,UAAc,EAAF;;AAEzC,CAAC,MAAM;IACLI,YAAA,CAAA,OAAc,GAAGF,+BAAA,EAA2E;AAC9F;;ACCA,MAAM,yBAAyB,GAAG,OAAO,MAAM,KAAK,WAAW,iKAAG,kBAAe,iKAAG,YAAS;AA0B7F;;;CAGG,GACH,MAAM,kBAAkB,CAAA;IAWtB,WAAA,CAAY,aAAsB,CAAA;QAV1B,IAAiB,CAAA,iBAAA,GAAG,CAAC;QAErB,IAAqB,CAAA,qBAAA,GAAG,CAAC;QAMzB,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAc;QAGzC,IAAI,CAAC,MAAM,GAAG,aAAa;QAC3B,IAAI,CAAC,YAAY,GAAG;YAAE,MAAM,EAAE,aAAa;YAAE,iBAAiB,EAAE,CAAC;QAAA,CAAE;QAEnE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;IAG5C;;KAEG,GACH,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,qBAAqB,EAAE;YACzD,OAAO,IAAI,CAAC,YAAY;;QAE1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB;QACnD,IAAI,CAAC,YAAY,GAAG;YAAE,MAAM,EAAE,IAAI,CAAC,MAAM;YAAE,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;QAAA,CAAE;QACtF,OAAO,IAAI,CAAC,YAAY;;IAG1B;;KAEG,GACH,iBAAiB,GAAA;QACf,OAAO;YAAE,MAAM,EAAE,IAAI;YAAE,iBAAiB,EAAE,CAAC;QAAA,CAAE;;IAG/C;;KAEG,GACH,SAAS,CAAC,QAAoB,EAAA;QAC5B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;QAC9B,OAAO,MAAK;YACV,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;QACnC,CAAC;;IAGH;;KAEG,GACH,KAAK,CAAC,UAAyB,EAAA;QAC7B,IAAI,CAAC,MAAM,GAAG,UAAqB;QAEnC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf;;;;aAIG,GACH,MAAM,EAAE,GAAG,MAAK;gBACd,IAAI,CAAC,iBAAiB,IAAI,CAAC;gBAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,EAAC,QAAQ,GAAI,QAAQ,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM;YAEjC,aAAa,CAAC,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC;YACnC,OAAO,MAAK;gBACV,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC;YACtC,CAAC;;QAGH,OAAO,SAAS;;AAEnB;AA+BD;;;;;;;;;;CAUG,GACG,SAAU,cAAc,CAC5B,OAA+G,EAAA;;IAE/G,MAAM,CAAC,kBAAkB,CAAC,GAAG,6KAAA,AAAQ;mCAAC,IAAM,IAAI,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;;IAGnF,MAAM,aAAa,GAAGG,oBAAAA,gCAAgC,CACpD,kBAAkB,CAAC,SAAS,EAC5B,kBAAkB,CAAC,WAAW,EAC9B,kBAAkB,CAAC,iBAAiB,EACpC,OAAO,CAAC,QAA6E,EACrF,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,SAAS,CAChC;IAED,yBAAyB;oDAAC,MAAK;YAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;SAChD;mDAAE;QAAC,OAAO,CAAC,MAAM;QAAE,kBAAkB;KAAC,CAAC;sKAExC,gBAAA,AAAa,EAAC,aAAa,CAAC;IAE5B,OAAO,aAAa;AACtB;ACpKA,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,gCAAK,YAAY;AACnD,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,WAAW;AAC3C,MAAM,MAAM,GAAG,KAAK,IAAI,OAAO,CAAC,OAAO,MAAM,KAAK,WAAW,IAAK,MAAc,CAAC,IAAI,CAAC;AAqBtF;;CAEG,GACH,MAAM,qBAAqB,CAAA;IAqCzB,WAAA,CAAY,OAA2C,CAAA;QApCvD;;SAEG,GACK,IAAM,CAAA,MAAA,GAAkB,IAAI;QAOpC;;;SAGG,GACK,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAc;QAO7C;;SAEG,GACK,IAAkB,CAAA,kBAAA,GAAG,KAAK;QAElC;;SAEG,GACK,IAAY,CAAA,YAAA,GAA0B,IAAI;QAElD;;SAEG,GACI,IAAU,CAAA,UAAA,GAAG,EAAE;QAGpB,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAc;QAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvC,IAAI,CAAC,eAAe,EAAE;QAEtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;QAClE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;IAG1C,SAAS,CAAC,MAAqB,EAAA;QACrC,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;QAGxD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAC,EAAE,GAAI,EAAE,EAAE,CAAC;;IAGhC,gBAAgB,GAAA;QACtB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACxD,IAAI,KAAK,IAAI,MAAM,EAAE;;gBAEnB,IAAI,KAAK,+BAAE;oBACT;;;qBAGG,GACH,OAAO,CAAC,IAAI,CACV,0HAA0H,CAC3H;;;gBAIH,OAAO,IAAI;;;YAIb,OAAO,IAAI,CAAC,YAAY,EAAE;;QAG5B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,IAAI,KAAK,IAAI,KAAK,EAAE;;YAE5D,MAAM,IAAI,KAAK,CACb,kOAAkO,CACnO;;QAGH,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC1C,OAAO,IAAI,CAAC,YAAY,EAAE;;QAG5B,OAAO,IAAI;;IAGb;;KAEG,GACK,YAAY,GAAA;QAClB,MAAM,cAAc,GAA2B;YAC7C,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;;YAEvB,cAAc,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3E,MAAM,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3D,QAAQ,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC/D,SAAS,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YACjE,OAAO,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC7D,iBAAiB,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,iBAAiB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YACjF,aAAa,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YACzE,QAAQ,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC/D,cAAc,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3E,MAAM,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3D,OAAO,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;SAC9D;QACD,MAAM,MAAM,GAAG,IAAI,8JAAM,CAAC,cAAc,CAAC;;QAIzC,OAAO,MAAM;;IAGf;;KAEG,GACH,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAGpB;;KAEG,GACH,iBAAiB,GAAA;QACf,OAAO,IAAI;;IAGb;;KAEG,GACH,SAAS,CAAC,aAAyB,EAAA;QACjC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;QAErC,OAAO,MAAK;YACV,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC;QAC1C,CAAC;;IAGH,OAAO,cAAc,CAAC,CAAmB,EAAE,CAAmB,EAAA;QAC5D,OAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAgC,CAAC,KAAK,EAAC,GAAG,IAAG;YAChE,IAAI;gBAAC,UAAU;gBAAE,gBAAgB;gBAAE,WAAW;gBAAE,UAAU;gBAAE,eAAe;gBAAE,SAAS;gBAAE,QAAQ;gBAAE,mBAAmB;gBAAE,gBAAgB;gBAAE,QAAQ;gBAAE,SAAS;aAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;;gBAE3K,OAAO,IAAI;;;YAIb,IAAI,GAAG,KAAK,YAAY,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,EAAE;gBACxD,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE;oBAC/C,OAAO,KAAK;;gBAEd,OAAO,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,KAAK,KAAI;;oBAC7C,IAAI,SAAS,KAAA,CAAK,CAAA,EAAA,GAAA,CAAC,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,KAAK,CAAC,CAAA,EAAE;wBACvC,OAAO,KAAK;;oBAEd,OAAO,IAAI;gBACb,CAAC,CAAC;;YAEJ,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;;gBAErB,OAAO,KAAK;;YAEd,OAAO,IAAI;QACb,CAAC,CAAC;;IAGJ;;;;KAIG,GACH,QAAQ,CAAC,IAAoB,EAAA;;QAE3B,OAAO,MAAK;YACV,IAAI,CAAC,kBAAkB,GAAG,IAAI;;YAE9B,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC;YAE9C,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;;gBAEhE,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;;;oBAGpF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;wBACrB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;wBACvB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;oBACjC,CAAA,CAAC;;mBAEC;;;;;;gBAML,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;;YAGlC,OAAO,MAAK;gBACV,IAAI,CAAC,kBAAkB,GAAG,KAAK;gBAC/B,IAAI,CAAC,eAAe,EAAE;YACxB,CAAC;QACH,CAAC;;IAGH;;KAEG,GACK,qBAAqB,CAAC,IAAoB,EAAA;QAChD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;;YAE3C,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;;gBAE9B,IAAI,CAAC,YAAY,GAAG,IAAI;gBACxB;;YAEF,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,MAAA,IAClD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,GAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjE,IAAI,YAAY,EAAE;;gBAEhB;;;QAIJ,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;;YAE3C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;;QAGvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;;QAGnC,IAAI,CAAC,YAAY,GAAG,IAAI;;IAG1B;;;;KAIG,GACK,eAAe,GAAA;QACrB,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM;;QAGjC,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC,MAAK;YACjD,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE;;gBAEpE,IAAI,aAAa,EAAE;;oBAEjB,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;;gBAEhD;;YAEF,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;gBAC/C,aAAa,CAAC,OAAO,EAAE;gBACvB,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE;oBACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;;;;SAKzB,EAAE,CAAC,CAAC;;AAER;SAuBe,SAAS,CACvB,UAA4B,CAAA,CAAE,EAC9B,OAAuB,EAAE,EAAA;IAEzB,MAAM,iBAAiB,qKAAG,SAAA,AAAM,EAAC,OAAO,CAAC;IAEzC,iBAAiB,CAAC,OAAO,GAAG,OAAO;IAEnC,MAAM,CAAC,eAAe,CAAC,qKAAG,WAAA,AAAQ;8BAAC,IAAM,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;;IAEtF,MAAM,MAAM,GAAGF,YAAAA,oBAAoB,CACjC,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,iBAAiB,CAClC;qKAED,iBAAa,AAAb,EAAc,MAAM,CAAC;;;sKAIrB,YAAA,AAAS,EAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;;IAIzC,cAAc,CAAC;QACb,MAAM;QACN,QAAQ;wCAAE,CAAC,EAAE,iBAAiB,EAAE,KAAI;gBAClC,IAAI,OAAO,CAAC,2BAA2B,KAAK,KAAK,EAAE;;oBAEjD,OAAO,IAAI;;;gBAIb,IAAI,OAAO,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,CAAC,EAAE;oBACxD,OAAO,CAAC;;gBAEV,OAAO,iBAAiB,GAAG,CAAC;aAC7B;;IACF,CAAA,CAAC;IAEF,OAAO,MAAM;AACf;AC3WO,MAAM,aAAa,qKAAG,gBAAA,AAAa,EAAqB;IAC7D,MAAM,EAAE,IAAI;AACb,CAAA;AAEY,MAAA,cAAc,GAAG,aAAa,CAAC,QAAA;AAE5C;;CAEG,GACU,MAAA,gBAAgB,GAAG,sKAAM,aAAA,AAAU,EAAC,aAAa;AAS9D;;;;CAIG,YACa,cAAc,CAAC,EAC7B,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,oBAAoB,GAAG,CAAA,CAAE,EAAE,GAAG,aAAa,EACxD,EAAA;IACpB,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC;IAEvC,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,IAAI;;IAGb,qKACE,UAAC,CAAA,aAAA,CAAA,aAAa,CAAC,QAAQ,EAAA;QAAC,KAAK,EAAE;YAAE,MAAM;QAAA,CAAE;IAAA,CAAA,EACtC,UAAU,gKACX,UAAC,CAAA,aAAA,CAAA,cAAc,EAAA,MACZ,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,iKACzB,UAAC,CAAA,aAAA,CAAA,aAAa,EAAA;YAAC,MAAM,EAAE,aAAa;YAAM,GAAA,oBAAoB;QAAA,CAAI,CAAA,CACnE,CACc,CAChB,QAAQ,EACR,SAAS,CACa;AAE7B;AC1Ca,MAAA,UAAU,GAAG,CAAC,KAAsB,KAAI;IACnD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,oKAAG,YAAA,AAAQ,EAAwB,IAAI,CAAC;IACnE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,EAAE;sKAEpD,YAAA,AAAS;gCAAC,MAAK;;YACb,IAAI,CAAC,OAAO,EAAE;gBACZ;;YAGF,IAAI,CAAA,CAAA,EAAA,GAAA,KAAK,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,WAAW,KAAA,CAAI,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,WAAW,CAAA,EAAE;gBAC3D;;YAGF,MAAM,EACJ,SAAS,GAAG,YAAY,EAAE,MAAM,EAAE,YAAY,GAAG,CAAA,CAAE,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI,EACpF,GAAG,KAAK;YAET,MAAM,UAAU,GAAG,MAAM,IAAI,aAAa;YAE1C,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,kGAAkG,CAAC;gBAChH;;YAGF,MAAM,MAAM,mLAAG,mBAAA,AAAgB,EAAC;gBAC9B,WAAW;gBACX,MAAM,EAAE,UAAU;gBAClB,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,YAAY;YACb,CAAA,CAAC;YAEF,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;YACjC;wCAAO,MAAQ;oBAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;gBAAA,CAAE;;SACxD;+BAAE;QAAC,KAAK,CAAC,MAAM;QAAE,aAAa;QAAE,OAAO;KAAC,CAAC;IAE1C,qKACE,UAAK,CAAA,aAAA,CAAA,KAAA,EAAA;QAAA,GAAG,EAAE,UAAU;QAAE,SAAS,EAAE,KAAK,CAAC,SAAS;QAAE,KAAK,EAAE;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;IAAA,CAC9E,EAAA,KAAK,CAAC,QAAQ,CACX;AAEV;ACzCa,MAAA,YAAY,GAAG,CAAC,KAAwB,KAAI;IACvD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,qKAAG,WAAA,AAAQ,EAAwB,IAAI,CAAC;IACnE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,EAAE;sKAEpD,YAAA,AAAS;kCAAC,MAAK;;YACb,IAAI,CAAC,OAAO,EAAE;gBACZ;;YAGF,IAAI,CAAA,CAAA,EAAA,GAAA,KAAK,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,WAAW,KAAA,CAAI,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,WAAW,CAAA,EAAE;gBAC3D;;YAGF,MAAM,EACJ,SAAS,GAAG,cAAc,EAC1B,MAAM,EACN,YAAY,GAAG,CAAA,CAAE,EACjB,UAAU,GAAG,IAAI,EAClB,GAAG,KAAK;YAET,MAAM,UAAU,GAAG,MAAM,IAAI,aAAa;YAE1C,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,oGAAoG,CAAC;gBAClH;;YAGF,MAAM,MAAM,qLAAG,qBAAA,AAAkB,EAAC;gBAChC,SAAS;gBACT,MAAM,EAAE,UAAU;gBAClB,OAAO;gBACP,YAAY;gBACZ,UAAU;YACX,CAAA,CAAC;YAEF,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;YACjC;0CAAO,MAAQ;oBAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;gBAAA,CAAE;;QACzD,CAAC;iCAAE;QACD,KAAK,CAAC,MAAM;QACZ,aAAa;QACb,OAAO;KACR,CAAC;IAEF,qKACE,UAAK,CAAA,aAAA,CAAA,KAAA,EAAA;QAAA,GAAG,EAAE,UAAU;QAAE,SAAS,EAAE,KAAK,CAAC,SAAS;QAAE,KAAK,EAAE;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;IAAA,CAC9E,EAAA,KAAK,CAAC,QAAQ,CACX;AAEV;ACxDO,MAAM,oBAAoB,OAAG,8KAAA,AAAa,EAAqC;IACpF,WAAW,EAAE,SAAS;AACvB,CAAA;AAEY,MAAA,gBAAgB,GAAG,sKAAM,aAAA,AAAU,EAAC,oBAAoB;ACFxD,MAAA,eAAe,IAAmC,KAAK,IAAG;IACrE,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK;IAC7B,MAAM,EAAE,kBAAkB,EAAE,GAAG,gBAAgB,EAAE;IAEjD;kKAEE,UAAC,CAAA,aAAA,CAAA,GAAG,EACE;QAAA,GAAA,KAAK;QACT,GAAG,EAAE,kBAAkB;QACA,wBAAA,EAAA,EAAE;QACzB,KAAK,EAAE;YACL,UAAU,EAAE,UAAU;YACtB,GAAG,KAAK,CAAC,KAAK;QACf,CAAA;IAAA,CAAA,CACD;AAEN;AChBO,MAAM,eAAe,iKAAmC,UAAK,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;IAC7F,MAAM,EAAE,WAAW,EAAE,GAAG,gBAAgB,EAAE;IAC1C,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK;IAE7B;IAEE,wKAAA,CAAA,aAAA,CAAC,GAAG,EAAA;QAAA,GACE,KAAK;QACT,GAAG,EAAE,GAAG;QACe,wBAAA,EAAA,EAAE;QACzB,WAAW,EAAE,WAAW;QACxB,KAAK,EAAE;YACL,UAAU,EAAE,QAAQ;YACpB,GAAG,KAAK,CAAC,KAAK;QACf,CAAA;IAAA,CAAA,CACD;AAEN,CAAC;ACbD;;;;CAIG,GACH,SAAS,gBAAgB,CAAC,SAAc,EAAA;IACtC,OAAO,CAAC,CAAA,CACN,OAAO,SAAS,KAAK,cAClB,SAAS,CAAC,SAAA,IACV,SAAS,CAAC,SAAS,CAAC,gBAAgB,CACxC;AACH;AAEA;;;;CAIG,GACH,SAAS,qBAAqB,CAAC,SAAc,EAAA;IAC3C,OAAO,CAAC,CAAA,CACN,OAAO,SAAS,KAAK,YAClB,SAAS,CAAC,QAAA,IACV,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,+BACjC,SAAS,CAAC,QAAQ,CAAC,WAAW,KAAK,mBAAmB,CAAC,CAC7D;AACH;AAEA;;;;CAIG,GACH,SAAS,eAAe,CAAC,SAAc,EAAA;IACrC,OAAO,CAAC,CAAA,CACN,OAAO,SAAS,KAAK,YAClB,SAAS,CAAC,QAAA,IACV,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,oBAAoB,IAAI,SAAS,CAAC,QAAQ,CAAC,WAAW,KAAK,YAAY,CAAC,CAC/G;AACH;AAEA;;;;;;CAMG,GACH,SAAS,aAAa,CAAC,SAAc,EAAA;;IAEnC,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;QAC/B,OAAO,IAAI;;;IAIb,IAAI,qBAAqB,CAAC,SAAS,CAAC,EAAE;QACpC,OAAO,IAAI;;;IAIb,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;;QAE9B,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI;QAEvC,IAAI,gBAAgB,EAAE;YACpB,OAAO,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,qBAAqB,CAAC,gBAAgB,CAAC;;;IAIxF,OAAO,KAAK;AACd;AAEA;;;CAGG,GACH,SAAS,aAAa,GAAA;;;;IAIpB,IAAI;;QAEF,kKAAIG,UAAY,EAAE;YAChB,MAAM,YAAY,GAAG,QAAQ,+JAACA,UAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAE7D,OAAO,YAAY,IAAI,EAAE;;MAE3B,OAAM;;;IAGR,OAAO,KAAK;AACd;AAqCA;;;;;;;;;;AAUE,SACW,aAAa,CAAA;IAexB;;KAEG,GACH,WAAA,CAAY,SAA8B,EAAE,EAC1C,MAAM,EACN,KAAK,GAAG,CAAA,CAAE,EACV,EAAE,GAAG,KAAK,EACV,SAAS,GAAG,EAAE,EACO,CAAA;QAVvB,IAAG,CAAA,GAAA,GAAa,IAAI;QAWlB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,QAAQ,EAAE;QAC3D,IAAI,CAAC,SAAS,GAAG,SAAS;QAC1B,IAAI,CAAC,MAAM,GAAG,MAAoC;QAClD,IAAI,CAAC,KAAK,GAAG,KAAU;QACvB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;QAE5C,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;QAGrD,cAAc,CAAC,MAAK;YAClB,IAAI,CAAC,MAAM,EAAE;QACf,CAAC,CAAC;;IAGJ;;KAEG,GACH,MAAM,GAAA;;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAoC;;QAGxD,MAAM,SAAS,GAAG,aAAa,EAAE;QACjC,MAAM,sBAAsB,GAAG,aAAa,CAAC,SAAS,CAAC;QAEvD,MAAM,YAAY,GAAG;YAAE,GAAG,KAAK;QAAA,CAAE;;QAGjC,IAAI,YAAY,CAAC,GAAG,IAAI,CAAA,CAAE,SAAS,IAAI,sBAAsB,CAAC,EAAE;YAC9D,OAAO,YAAY,CAAC,GAAG;;;QAIzB,IAAI,CAAC,YAAY,CAAC,GAAG,IAAA,CAAK,SAAS,IAAI,sBAAsB,CAAC,EAAE;;YAE9D,YAAY,CAAC,GAAG,GAAG,CAAC,GAAM,KAAI;gBAC5B,IAAI,CAAC,GAAG,GAAG,GAAG;YAChB,CAAC;;QAGH,IAAI,CAAC,YAAY,iKAAG,UAAA,CAAA,aAAA,CAAC,SAAS,EAAK;YAAA,GAAA,YAAY;QAAA,EAAI;QAEnD,CAAA,EAAA,GAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;;IAGtD;;KAEG,GACH,WAAW,CAAC,QAA6B,CAAA,CAAE,EAAA;QACzC,IAAI,CAAC,KAAK,GAAG;YACX,GAAG,IAAI,CAAC,KAAK;YACb,GAAG,KAAK;SACT;QAED,IAAI,CAAC,MAAM,EAAE;;IAGf;;KAEG,GACH,OAAO,GAAA;;QACL,MAAM,MAAM,GAAG,IAAI,CAAC,MAAoC;QAExD,CAAA,EAAA,GAAA,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;;IAGnD;;KAEG,GACH,gBAAgB,CAAC,UAAkC,EAAA;QACjD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAC,GAAG,IAAG;YACpC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC;;AAEL;ACpMK,MAAO,aAKX,8JAAQ,WAAwC,CAAA;IAWhD,WAAA,CAAY,SAAoB,EAAE,KAA4B,EAAE,OAA0B,CAAA;QACxF,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACrB,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;gBACrC,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;mBAC7E;gBACL,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC;;YAGtF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,oBAAoB,GAAG,EAAE;YACxD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,GAAG,EAAE;;;;YAKnD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;YAEnD,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;;;IAIhD;;;KAGG,GACH,KAAK,GAAA;QACH,MAAM,KAAK,GAAG;YACZ,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAmC;YACrD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,MAAM,EAAE,IAAM,IAAI,CAAC,MAAM,EAAE;YAC3B,gBAAgB,EAAE,CAAC,UAAU,GAAG,CAAA,CAAE,GAAK,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;YACxE,UAAU,EAAE,IAAM,IAAI,CAAC,UAAU,EAAE;YACnC,GAAG,oKAAE,YAAA,AAAS,EAAK;SACY;QAEjC,IAAI,CAAE,IAAI,CAAC,SAAiB,CAAC,WAAW,EAAE;YACxC,MAAM,mBAAmB,GAAG,CAAC,MAAc,KAAY;gBACrD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;QAGvE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;QAC/C,MAAM,kBAAkB,IAAoD,OAAO,IAAG;YACpF,IAAI,OAAO,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,CAAC,iBAAiB,EAAE;;gBAEtF,IAAI,OAAO,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE;oBAClD,OAAO,CAAC,eAAe,CAAC,wBAAwB,CAAC;;gBAEnD,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;;QAE/C,CAAC;QACD,MAAM,OAAO,GAAG;YAAE,WAAW;YAAE,kBAAkB;QAAA,CAAE;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;;;QAGhC,MAAM,qBAAqB,IAAgD,wKAAA,AAAI,GAAC,cAAc,IAAG;YAC/F,qKACE,UAAC,CAAA,aAAA,CAAA,oBAAoB,CAAC,QAAQ,EAAA;gBAAC,KAAK,EAAE,OAAO;YAAA,CAC1C,oKAAA,gBAAA,AAAa,EAAC,SAAS,EAAE,cAAc,CAAC,CACX;QAEpC,CAAC,CAAC;QAEF,qBAAqB,CAAC,WAAW,GAAG,eAAe;QAEnD,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,KAAK;QAE5C,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;YACnB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;;QAGtB,MAAM,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO;QAEvC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;QAElE,IAAI,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAC,qBAAqB,EAAE;YACvD,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK;YACL,EAAE;YACF,SAAS,EAAE,CAAA,KAAA,EAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAI,CAAA,EAAA,SAAS,CAAE,CAAA,CAAC,IAAI,EAAE;QAC7D,CAAA,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC;QAC7D,IAAI,CAAC,uBAAuB,EAAE;;IAGhC;;;KAGG,GACH,IAAI,GAAG,GAAA;;QACL,IACE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAA,IACnB,CAAA,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,wBAAwB,CAAC,CAAA,EACnF;YACA,MAAM,KAAK,CAAC,8DAA8D,CAAC;;QAG7E,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAsB;;IAG7C;;;KAGG,GACH,IAAI,UAAU,GAAA;QACZ,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpB,OAAO,IAAI;;QAGb,OAAO,IAAI,CAAC,iBAAiB;;IAG/B;;;KAGG,GACH,qBAAqB,GAAA;QACnB,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS;QAChD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;QAEzB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B;;QAGF,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAChC;;YAGF,IAAI,CAAC,UAAU,EAAE;eACZ;YACL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACjC;;YAGF,IAAI,CAAC,YAAY,EAAE;;;IAIvB;;;KAGG,GACH,MAAM,CACJ,IAAU,EACV,WAAkC,EAClC,gBAAkC,EAAA;QAElC,MAAM,iBAAiB,GAAG,CAAC,KAA2B,KAAI;YACxD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;YAChC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;gBAC5C,IAAI,CAAC,uBAAuB,EAAE;;QAElC,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAChC,OAAO,KAAK;;QAGd,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;YACzB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW;YACvC,MAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB;YAEjD,IAAI,CAAC,IAAI,GAAG,IAAI;YAChB,IAAI,CAAC,WAAW,GAAG,WAAW;YAC9B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;YAExC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;gBACzB,OAAO;gBACP,cAAc;gBACd,OAAO,EAAE,IAAI;gBACb,cAAc,EAAE,WAAW;gBAC3B,mBAAmB;gBACnB,gBAAgB;gBAChB,WAAW,EAAE,IAAM,iBAAiB,CAAC;wBAAE,IAAI;wBAAE,WAAW;wBAAE,gBAAgB;oBAAA,CAAE,CAAC;YAC9E,CAAA,CAAC;;QAGJ,IACE,IAAI,KAAK,IAAI,CAAC,IAAA,IACX,IAAI,CAAC,WAAW,KAAK,eACrB,IAAI,CAAC,gBAAgB,KAAK,gBAAgB,EAC7C;YACA,OAAO,IAAI;;QAGb,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,WAAW,GAAG,WAAW;QAC9B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;QAExC,iBAAiB,CAAC;YAAE,IAAI;YAAE,WAAW;YAAE,gBAAgB;QAAA,CAAE,CAAC;QAE1D,OAAO,IAAI;;IAGb;;;KAGG,GACH,UAAU,GAAA;QACR,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACxB,QAAQ,EAAE,IAAI;QACf,CAAA,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,0BAA0B,CAAC;;IAGjE;;;KAGG,GACH,YAAY,GAAA;QACV,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACxB,QAAQ,EAAE,KAAK;QAChB,CAAA,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,0BAA0B,CAAC;;IAGpE;;KAEG,GACH,OAAO,GAAA;QACL,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;QACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC;QAC9D,IAAI,CAAC,iBAAiB,GAAG,IAAI;;IAG/B;;;KAGG,GACH,uBAAuB,GAAA;QACrB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,IAAI,QAAQ,GAA2B,CAAA,CAAE;YAEzC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;gBAC5C,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;gBACnE,MAAM,cAAc,4JAAG,wBAAqB,AAArB,EAAsB,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;gBAE5E,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;oBAAE,IAAI,EAAE,IAAI,CAAC,IAAI;oBAAE,cAAc;gBAAA,CAAE,CAAC;mBAC7D;gBACL,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;;YAG/B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;;AAG7C;AAED;;CAEG,GACa,SAAA,qBAAqB,CACnC,SAA+C,EAC/C,OAA+C,EAAA;IAE/C,QAAO,KAAK,IAAG;;;;QAIb,IAAI,CAAE,KAAK,CAAC,MAAqC,CAAC,gBAAgB,EAAE;YAClE,OAAO,CAAA,CAAoC;;QAG7C,OAAO,IAAI,aAAa,CAAI,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC;IACxD,CAAC;AACH", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "debugId": null}}, {"offset": {"line": 2476, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-highlight/src/highlight.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface HighlightOptions {\n  /**\n   * Allow multiple highlight colors\n   * @default false\n   * @example true\n   */\n  multicolor: boolean,\n\n  /**\n   * HTML attributes to add to the highlight element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    highlight: {\n      /**\n       * Set a highlight mark\n       * @param attributes The highlight attributes\n       * @example editor.commands.setHighlight({ color: 'red' })\n       */\n      setHighlight: (attributes?: { color: string }) => ReturnType,\n      /**\n       * Toggle a highlight mark\n       * @param attributes The highlight attributes\n       * @example editor.commands.toggleHighlight({ color: 'red' })\n       */\n      toggleHighlight: (attributes?: { color: string }) => ReturnType,\n      /**\n       * Unset a highlight mark\n       * @example editor.commands.unsetHighlight()\n       */\n      unsetHighlight: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a highlight to a ==highlight== on input.\n */\nexport const inputRegex = /(?:^|\\s)(==(?!\\s+==)((?:[^=]+))==(?!\\s+==))$/\n\n/**\n * Matches a highlight to a ==highlight== on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(==(?!\\s+==)((?:[^=]+))==(?!\\s+==))/g\n\n/**\n * This extension allows you to highlight text.\n * @see https://www.tiptap.dev/api/marks/highlight\n */\nexport const Highlight = Mark.create<HighlightOptions>({\n  name: 'highlight',\n\n  addOptions() {\n    return {\n      multicolor: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  addAttributes() {\n    if (!this.options.multicolor) {\n      return {}\n    }\n\n    return {\n      color: {\n        default: null,\n        parseHTML: element => element.getAttribute('data-color') || element.style.backgroundColor,\n        renderHTML: attributes => {\n          if (!attributes.color) {\n            return {}\n          }\n\n          return {\n            'data-color': attributes.color,\n            style: `background-color: ${attributes.color}; color: inherit`,\n          }\n        },\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'mark',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['mark', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHighlight: attributes => ({ commands }) => {\n        return commands.setMark(this.name, attributes)\n      },\n      toggleHighlight: attributes => ({ commands }) => {\n        return commands.toggleMark(this.name, attributes)\n      },\n      unsetHighlight: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-h': () => this.editor.commands.toggleHighlight(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;AA+CA;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,SAAS,wJAAG,OAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;YACL,UAAU,EAAE,KAAK;YACjB,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,aAAa,GAAA;QACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC5B,OAAO,CAAA,CAAE;;QAGX,OAAO;YACL,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,GAAE,OAAO,GAAI,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,eAAe;gBACzF,UAAU,GAAE,UAAU,IAAG;oBACvB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;wBACrB,OAAO,CAAA,CAAE;;oBAGX,OAAO;wBACL,YAAY,EAAE,UAAU,CAAC,KAAK;wBAC9B,KAAK,EAAE,CAAA,kBAAA,EAAqB,UAAU,CAAC,KAAK,CAAkB,gBAAA,CAAA;qBAC/D;iBACF;YACF,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,MAAM;YACZ,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,MAAM;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACjF;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,EAAE,UAAU,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/C;YACD,eAAe,GAAE,UAAU,GAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC9C,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;iBAClD;YACD,cAAc,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACrC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;SAC5D;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2578, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-horizontal-rule/src/horizontal-rule.ts"], "sourcesContent": ["import {\n  canInsertNode, isNodeSelection, mergeAttributes, Node, nodeInputRule,\n} from '@tiptap/core'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\n\nexport interface HorizontalRuleOptions {\n  /**\n   * The HTML attributes for a horizontal rule node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    horizontalRule: {\n      /**\n       * Add a horizontal rule\n       * @example editor.commands.setHorizontalRule()\n       */\n      setHorizontalRule: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nexport const HorizontalRule = Node.create<HorizontalRuleOptions>({\n  name: 'horizontalRule',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'hr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setHorizontalRule:\n        () => ({ chain, state }) => {\n          // Check if we can insert the node at the current selection\n          if (!canInsertNode(state, state.schema.nodes[this.name])) {\n            return false\n          }\n\n          const { selection } = state\n          const { $from: $originFrom, $to: $originTo } = selection\n\n          const currentChain = chain()\n\n          if ($originFrom.parentOffset === 0) {\n            currentChain.insertContentAt(\n              {\n                from: Math.max($originFrom.pos - 1, 0),\n                to: $originTo.pos,\n              },\n              {\n                type: this.name,\n              },\n            )\n          } else if (isNodeSelection(selection)) {\n            currentChain.insertContentAt($originTo.pos, {\n              type: this.name,\n            })\n          } else {\n            currentChain.insertContent({ type: this.name })\n          }\n\n          return (\n            currentChain\n              // set cursor after horizontal rule\n              .command(({ tr, dispatch }) => {\n                if (dispatch) {\n                  const { $to } = tr.selection\n                  const posAfter = $to.end()\n\n                  if ($to.nodeAfter) {\n                    if ($to.nodeAfter.isTextblock) {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos + 1))\n                    } else if ($to.nodeAfter.isBlock) {\n                      tr.setSelection(NodeSelection.create(tr.doc, $to.pos))\n                    } else {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos))\n                    }\n                  } else {\n                    // add node after horizontal rule if it’s the end of the document\n                    const node = $to.parent.type.contentMatch.defaultType?.create()\n\n                    if (node) {\n                      tr.insert(posAfter, node)\n                      tr.setSelection(TextSelection.create(tr.doc, posAfter + 1))\n                    }\n                  }\n\n                  tr.scrollIntoView()\n                }\n\n                return true\n              })\n              .run()\n          )\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;AA0BA;;;CAGG,GACU,MAAA,cAAc,wJAAG,OAAI,CAAC,MAAM,CAAwB;IAC/D,IAAI,EAAE,gBAAgB;IAEtB,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,KAAK,EAAE,OAAO;IAEd,SAAS,GAAA;QACP,OAAO;YAAC;gBAAE,GAAG,EAAE,IAAI;YAAA,CAAE;SAAC;KACvB;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;SAAC;KAC5E;IAED,WAAW,GAAA;QACT,OAAO;YACL,iBAAiB,EACf,IAAM,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;;oBAEzB,IAAI,0JAAC,gBAAA,AAAa,EAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;wBACxD,OAAO,KAAK;;oBAGd,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;oBAC3B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAS;oBAExD,MAAM,YAAY,GAAG,KAAK,EAAE;oBAE5B,IAAI,WAAW,CAAC,YAAY,KAAK,CAAC,EAAE;wBAClC,YAAY,CAAC,eAAe,CAC1B;4BACE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;4BACtC,EAAE,EAAE,SAAS,CAAC,GAAG;yBAClB,EACD;4BACE,IAAI,EAAE,IAAI,CAAC,IAAI;wBAChB,CAAA,CACF;2BACI,6JAAI,kBAAA,AAAe,EAAC,SAAS,CAAC,EAAE;wBACrC,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,EAAE;4BAC1C,IAAI,EAAE,IAAI,CAAC,IAAI;wBAChB,CAAA,CAAC;2BACG;wBACL,YAAY,CAAC,aAAa,CAAC;4BAAE,IAAI,EAAE,IAAI,CAAC,IAAI;wBAAA,CAAE,CAAC;;oBAGjD,OACE;qBAEG,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;;wBAC5B,IAAI,QAAQ,EAAE;4BACZ,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,SAAS;4BAC5B,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,EAAE;4BAE1B,IAAI,GAAG,CAAC,SAAS,EAAE;gCACjB,IAAI,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE;oCAC7B,EAAE,CAAC,YAAY,0JAAC,gBAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;uCACrD,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE;oCAChC,EAAE,CAAC,YAAY,yJAAC,iBAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;uCACjD;oCACL,EAAE,CAAC,YAAY,CAAC,yKAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;;mCAEnD;;gCAEL,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,EAAE;gCAE/D,IAAI,IAAI,EAAE;oCACR,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;oCACzB,EAAE,CAAC,YAAY,0JAAC,gBAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;;;4BAI/D,EAAE,CAAC,cAAc,EAAE;;wBAGrB,OAAO,IAAI;oBACb,CAAC,EACA,GAAG,EAAE;iBAEX;SACJ;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,6BAA6B;gBACnC,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2683, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-image/src/image.ts"], "sourcesContent": ["import {\n  mergeAttributes,\n  Node,\n  nodeInputRule,\n} from '@tiptap/core'\n\nexport interface ImageOptions {\n  /**\n   * Controls if the image node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean,\n\n  /**\n   * Controls if base64 images are allowed. Enable this if you want to allow\n   * base64 image urls in the `src` attribute.\n   * @default false\n   * @example true\n   */\n  allowBase64: boolean,\n\n  /**\n   * HTML attributes to add to the image element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    image: {\n      /**\n       * Add an image\n       * @param options The image attributes\n       * @example\n       * editor\n       *   .commands\n       *   .setImage({ src: 'https://tiptap.dev/logo.png', alt: 'tiptap', title: 'tiptap logo' })\n       */\n      setImage: (options: { src: string, alt?: string, title?: string }) => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nexport const inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/\n\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nexport const Image = Node.create<ImageOptions>({\n  name: 'image',\n\n  addOptions() {\n    return {\n      inline: false,\n      allowBase64: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      alt: {\n        default: null,\n      },\n      title: {\n        default: null,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: this.options.allowBase64\n          ? 'img[src]'\n          : 'img[src]:not([src^=\"data:\"])',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setImage: options => ({ commands }) => {\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => {\n          const [,, alt, src, title] = match\n\n          return { src, alt, title }\n        },\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;AA8CA;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,KAAK,wJAAG,OAAI,CAAC,MAAM,CAAe;IAC7C,IAAI,EAAE,OAAO;IAEb,UAAU,GAAA;QACR,OAAO;YACL,MAAM,EAAE,KAAK;YACb,WAAW,EAAE,KAAK;YAClB,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;KAC3B;IAED,KAAK,GAAA;QACH,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;KAChD;IAED,SAAS,EAAE,IAAI;IAEf,aAAa,GAAA;QACX,OAAO;YACL,GAAG,EAAE;gBACH,OAAO,EAAE,IAAI;YACd,CAAA;YACD,GAAG,EAAE;gBACH,OAAO,EAAE,IAAI;YACd,CAAA;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI;YACd,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,WAAA,GACd,aACA,8BAA8B;YACnC,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,KAAK;YAAE,2KAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;SAAC;KAC7E;IAED,WAAW,GAAA;QACT,OAAO;YACL,QAAQ,EAAE,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACpC,OAAO,QAAQ,CAAC,aAAa,CAAC;wBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,KAAK,EAAE,OAAO;oBACf,CAAA,CAAC;iBACH;SACF;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,GAAE,KAAK,IAAG;oBACrB,MAAM,KAAI,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK;oBAElC,OAAO;wBAAE,GAAG;wBAAE,GAAG;wBAAE,KAAK;oBAAA,CAAE;iBAC3B;aACF,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-link/src/helpers/whitespace.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-link/src/helpers/autolink.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-link/src/helpers/clickHandler.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-link/src/helpers/pasteHandler.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-link/src/link.ts"], "sourcesContent": ["// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.ts\nexport const UNICODE_WHITESPACE_PATTERN = '[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]'\n\nexport const UNICODE_WHITESPACE_REGEX = new RegExp(UNICODE_WHITESPACE_PATTERN)\nexport const UNICODE_WHITESPACE_REGEX_END = new RegExp(`${UNICODE_WHITESPACE_PATTERN}$`)\nexport const UNICODE_WHITESPACE_REGEX_GLOBAL = new RegExp(UNICODE_WHITESPACE_PATTERN, 'g')\n", "import {\n  combineTransactionSteps,\n  findChildrenInRange,\n  getChangedRanges,\n  getMarksBetween,\n  NodeWithPos,\n} from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { MultiToken, tokenize } from 'linkifyjs'\n\nimport { UNICODE_WHITESPACE_REGEX, UNICODE_WHITESPACE_REGEX_END } from './whitespace.js'\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens: Array<ReturnType<MultiToken['toObject']>>) {\n  if (tokens.length === 1) {\n    return tokens[0].isLink\n  }\n\n  if (tokens.length === 3 && tokens[1].isLink) {\n    return ['()', '[]'].includes(tokens[0].value + tokens[2].value)\n  }\n\n  return false\n}\n\ntype AutolinkOptions = {\n  type: MarkType\n  defaultProtocol: string\n  validate: (url: string) => boolean\n  shouldAutoLink: (url: string) => boolean\n}\n\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nexport function autolink(options: AutolinkOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('autolink'),\n    appendTransaction: (transactions, oldState, newState) => {\n      /**\n       * Does the transaction change the document?\n       */\n      const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)\n\n      /**\n       * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n       */\n      const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'))\n\n      /**\n       * Prevent autolink if the transaction is not a document change\n       * or if the transaction has the meta `preventAutolink`.\n       */\n      if (!docChanges || preventAutolink) {\n        return\n      }\n\n      const { tr } = newState\n      const transform = combineTransactionSteps(oldState.doc, [...transactions])\n      const changes = getChangedRanges(transform)\n\n      changes.forEach(({ newRange }) => {\n        // Now let’s see if we can add new links.\n        const nodesInChangedRanges = findChildrenInRange(\n          newState.doc,\n          newRange,\n          node => node.isTextblock,\n        )\n\n        let textBlock: NodeWithPos | undefined\n        let textBeforeWhitespace: string | undefined\n\n        if (nodesInChangedRanges.length > 1) {\n          // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            textBlock.pos + textBlock.node.nodeSize,\n            undefined,\n            ' ',\n          )\n        } else if (nodesInChangedRanges.length) {\n          const endText = newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ')\n\n          if (!UNICODE_WHITESPACE_REGEX_END.test(endText)) {\n            return\n          }\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            newRange.to,\n            undefined,\n            ' ',\n          )\n        }\n\n        if (textBlock && textBeforeWhitespace) {\n          const wordsBeforeWhitespace = textBeforeWhitespace.split(UNICODE_WHITESPACE_REGEX).filter(Boolean)\n\n          if (wordsBeforeWhitespace.length <= 0) {\n            return false\n          }\n\n          const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1]\n          const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace)\n\n          if (!lastWordBeforeSpace) {\n            return false\n          }\n\n          const linksBeforeSpace = tokenize(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol))\n\n          if (!isValidLinkStructure(linksBeforeSpace)) {\n            return false\n          }\n\n          linksBeforeSpace\n            .filter(link => link.isLink)\n            // Calculate link position.\n            .map(link => ({\n              ...link,\n              from: lastWordAndBlockOffset + link.start + 1,\n              to: lastWordAndBlockOffset + link.end + 1,\n            }))\n            // ignore link inside code mark\n            .filter(link => {\n              if (!newState.schema.marks.code) {\n                return true\n              }\n\n              return !newState.doc.rangeHasMark(\n                link.from,\n                link.to,\n                newState.schema.marks.code,\n              )\n            })\n            // validate link\n            .filter(link => options.validate(link.value))\n            // check whether should autolink\n            .filter(link => options.shouldAutoLink(link.value))\n            // Add link mark.\n            .forEach(link => {\n              if (getMarksBetween(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                return\n              }\n\n              tr.addMark(\n                link.from,\n                link.to,\n                options.type.create({\n                  href: link.href,\n                }),\n              )\n            })\n        }\n      })\n\n      if (!tr.steps.length) {\n        return\n      }\n\n      return tr\n    },\n  })\n}\n", "import { getAttributes } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\n\ntype ClickHandlerOptions = {\n  type: MarkType;\n}\n\nexport function clickHandler(options: ClickHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handleClickLink'),\n    props: {\n      handleClick: (view, pos, event) => {\n        if (event.button !== 0) {\n          return false\n        }\n\n        if (!view.editable) {\n          return false\n        }\n\n        let a = event.target as HTMLElement\n        const els = []\n\n        while (a.nodeName !== 'DIV') {\n          els.push(a)\n          a = a.parentNode as HTMLElement\n        }\n\n        if (!els.find(value => value.nodeName === 'A')) {\n          return false\n        }\n\n        const attrs = getAttributes(view.state, options.type.name)\n        const link = (event.target as HTMLAnchorElement)\n\n        const href = link?.href ?? attrs.href\n        const target = link?.target ?? attrs.target\n\n        if (link && href) {\n          window.open(href, target)\n\n          return true\n        }\n\n        return false\n      },\n    },\n  })\n}\n", "import { Editor } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { find } from 'linkifyjs'\n\ntype PasteHandlerOptions = {\n  editor: Editor\n  defaultProtocol: string\n  type: MarkType\n}\n\nexport function pasteHandler(options: PasteHandlerOptions): Plugin {\n  return new Plugin({\n    key: new Plugin<PERSON>ey('handlePasteLink'),\n    props: {\n      handlePaste: (view, event, slice) => {\n        const { state } = view\n        const { selection } = state\n        const { empty } = selection\n\n        if (empty) {\n          return false\n        }\n\n        let textContent = ''\n\n        slice.content.forEach(node => {\n          textContent += node.textContent\n        })\n\n        const link = find(textContent, { defaultProtocol: options.defaultProtocol }).find(item => item.isLink && item.value === textContent)\n\n        if (!textContent || !link) {\n          return false\n        }\n\n        return options.editor.commands.setMark(options.type, {\n          href: link.href,\n        })\n      },\n    },\n  })\n}\n", "import {\n  <PERSON>, markPaste<PERSON><PERSON>, merge<PERSON>ttribut<PERSON>, PasteRuleMatch,\n} from '@tiptap/core'\nimport { Plugin } from '@tiptap/pm/state'\nimport { find, registerCustomProtocol, reset } from 'linkifyjs'\n\nimport { autolink } from './helpers/autolink.js'\nimport { clickHandler } from './helpers/clickHandler.js'\nimport { pasteHandler } from './helpers/pasteHandler.js'\nimport { UNICODE_WHITESPACE_REGEX_GLOBAL } from './helpers/whitespace.js'\n\nexport interface LinkProtocolOptions {\n  /**\n   * The protocol scheme to be registered.\n   * @default '''\n   * @example 'ftp'\n   * @example 'git'\n   */\n  scheme: string;\n\n  /**\n   * If enabled, it allows optional slashes after the protocol.\n   * @default false\n   * @example true\n   */\n  optionalSlashes?: boolean;\n}\n\nexport const pasteRegex = /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi\n\n/**\n * @deprecated The default behavior is now to open links when the editor is not editable.\n */\ntype DeprecatedOpenWhenNotEditable = 'whenNotEditable';\n\nexport interface LinkOptions {\n  /**\n   * If enabled, the extension will automatically add links as you type.\n   * @default true\n   * @example false\n   */\n  autolink: boolean;\n\n  /**\n   * An array of custom protocols to be registered with linkifyjs.\n   * @default []\n   * @example ['ftp', 'git']\n   */\n  protocols: Array<LinkProtocolOptions | string>;\n\n  /**\n   * Default protocol to use when no protocol is specified.\n   * @default 'http'\n   */\n  defaultProtocol: string;\n  /**\n   * If enabled, links will be opened on click.\n   * @default true\n   * @example false\n   */\n  openOnClick: boolean | DeprecatedOpenWhenNotEditable;\n  /**\n   * Adds a link to the current selection if the pasted content only contains an url.\n   * @default true\n   * @example false\n   */\n  linkOnPaste: boolean;\n\n  /**\n   * HTML attributes to add to the link element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * @deprecated Use the `shouldAutoLink` option instead.\n   * A validation function that modifies link verification for the auto linker.\n   * @param url - The url to be validated.\n   * @returns - True if the url is valid, false otherwise.\n   */\n  validate: (url: string) => boolean;\n\n  /**\n   * A validation function which is used for configuring link verification for preventing XSS attacks.\n   * Only modify this if you know what you're doing.\n   *\n   * @returns {boolean} `true` if the URL is valid, `false` otherwise.\n   *\n   * @example\n   * isAllowedUri: (url, { defaultValidate, protocols, defaultProtocol }) => {\n   * return url.startsWith('./') || defaultValidate(url)\n   * }\n   */\n  isAllowedUri: (\n    /**\n     * The URL to be validated.\n     */\n    url: string,\n    ctx: {\n      /**\n       * The default validation function.\n       */\n      defaultValidate: (url: string) => boolean;\n      /**\n       * An array of allowed protocols for the URL (e.g., \"http\", \"https\"). As defined in the `protocols` option.\n       */\n      protocols: Array<LinkProtocolOptions | string>;\n      /**\n       * A string that represents the default protocol (e.g., 'http'). As defined in the `defaultProtocol` option.\n       */\n      defaultProtocol: string;\n    }\n  ) => boolean;\n\n  /**\n   * Determines whether a valid link should be automatically linked in the content.\n   *\n   * @param {string} url - The URL that has already been validated.\n   * @returns {boolean} - True if the link should be auto-linked; false if it should not be auto-linked.\n   */\n  shouldAutoLink: (url: string) => boolean;\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    link: {\n      /**\n       * Set a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.setLink({ href: 'https://tiptap.dev' })\n       */\n      setLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Toggle a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.toggleLink({ href: 'https://tiptap.dev' })\n       */\n      toggleLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Unset a link mark\n       * @example editor.commands.unsetLink()\n       */\n      unsetLink: () => ReturnType;\n    };\n  }\n}\n\nexport function isAllowedUri(uri: string | undefined, protocols?: LinkOptions['protocols']) {\n  const allowedProtocols: string[] = [\n    'http',\n    'https',\n    'ftp',\n    'ftps',\n    'mailto',\n    'tel',\n    'callto',\n    'sms',\n    'cid',\n    'xmpp',\n  ]\n\n  if (protocols) {\n    protocols.forEach(protocol => {\n      const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme\n\n      if (nextProtocol) {\n        allowedProtocols.push(nextProtocol)\n      }\n    })\n  }\n\n  return (\n    !uri\n    || uri.replace(UNICODE_WHITESPACE_REGEX_GLOBAL, '').match(\n      new RegExp(\n        // eslint-disable-next-line no-useless-escape\n        `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`,\n        'i',\n      ),\n    )\n  )\n}\n\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nexport const Link = Mark.create<LinkOptions>({\n  name: 'link',\n\n  priority: 1000,\n\n  keepOnSplit: false,\n\n  exitable: true,\n\n  onCreate() {\n    if (this.options.validate && !this.options.shouldAutoLink) {\n      // Copy the validate function to the shouldAutoLink option\n      this.options.shouldAutoLink = this.options.validate\n      console.warn(\n        'The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.',\n      )\n    }\n    this.options.protocols.forEach(protocol => {\n      if (typeof protocol === 'string') {\n        registerCustomProtocol(protocol)\n        return\n      }\n      registerCustomProtocol(protocol.scheme, protocol.optionalSlashes)\n    })\n  },\n\n  onDestroy() {\n    reset()\n  },\n\n  inclusive() {\n    return this.options.autolink\n  },\n\n  addOptions() {\n    return {\n      openOnClick: true,\n      linkOnPaste: true,\n      autolink: true,\n      protocols: [],\n      defaultProtocol: 'http',\n      HTMLAttributes: {\n        target: '_blank',\n        rel: 'noopener noreferrer nofollow',\n        class: null,\n      },\n      isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n      validate: url => !!url,\n      shouldAutoLink: url => !!url,\n    }\n  },\n\n  addAttributes() {\n    return {\n      href: {\n        default: null,\n        parseHTML(element) {\n          return element.getAttribute('href')\n        },\n      },\n      target: {\n        default: this.options.HTMLAttributes.target,\n      },\n      rel: {\n        default: this.options.HTMLAttributes.rel,\n      },\n      class: {\n        default: this.options.HTMLAttributes.class,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'a[href]',\n        getAttrs: dom => {\n          const href = (dom as HTMLElement).getAttribute('href')\n\n          // prevent XSS attacks\n          if (\n            !href\n            || !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n          return null\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    // prevent XSS attacks\n    if (\n      !this.options.isAllowedUri(HTMLAttributes.href, {\n        defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n        protocols: this.options.protocols,\n        defaultProtocol: this.options.defaultProtocol,\n      })\n    ) {\n      // strip out the href\n      return [\n        'a',\n        mergeAttributes(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }),\n        0,\n      ]\n    }\n\n    return ['a', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run()\n        },\n\n      toggleLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain()\n            .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n\n      unsetLink:\n        () => ({ chain }) => {\n          return chain()\n            .unsetMark(this.name, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n    }\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: text => {\n          const foundLinks: PasteRuleMatch[] = []\n\n          if (text) {\n            const { protocols, defaultProtocol } = this.options\n            const links = find(text).filter(\n              item => item.isLink\n                && this.options.isAllowedUri(item.value, {\n                  defaultValidate: href => !!isAllowedUri(href, protocols),\n                  protocols,\n                  defaultProtocol,\n                }),\n            )\n\n            if (links.length) {\n              links.forEach(link => foundLinks.push({\n                text: link.value,\n                data: {\n                  href: link.href,\n                },\n                index: link.start,\n              }))\n            }\n          }\n\n          return foundLinks\n        },\n        type: this.type,\n        getAttributes: match => {\n          return {\n            href: match.data?.href,\n          }\n        },\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    const plugins: Plugin[] = []\n    const { protocols, defaultProtocol } = this.options\n\n    if (this.options.autolink) {\n      plugins.push(\n        autolink({\n          type: this.type,\n          defaultProtocol: this.options.defaultProtocol,\n          validate: url => this.options.isAllowedUri(url, {\n            defaultValidate: href => !!isAllowedUri(href, protocols),\n            protocols,\n            defaultProtocol,\n          }),\n          shouldAutoLink: this.options.shouldAutoLink,\n        }),\n      )\n    }\n\n    if (this.options.openOnClick === true) {\n      plugins.push(\n        clickHandler({\n          type: this.type,\n        }),\n      )\n    }\n\n    if (this.options.linkOnPaste) {\n      plugins.push(\n        pasteHandler({\n          editor: this.editor,\n          defaultProtocol: this.options.defaultProtocol,\n          type: this.type,\n        }),\n      )\n    }\n\n    return plugins\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,iBAAA;AACA,8DAAA;AACO,MAAM,0BAA0B,GAAG,4DAA4D;AAE/F,MAAM,wBAAwB,GAAG,IAAI,MAAM,CAAC,0BAA0B,CAAC;AACvE,MAAM,4BAA4B,GAAG,IAAI,MAAM,CAAC,CAAG,EAAA,0BAA0B,CAAG,CAAA,CAAA,CAAC;AACjF,MAAM,+BAA+B,GAAG,IAAI,MAAM,CAAC,0BAA0B,EAAE,GAAG,CAAC;ACO1F;;;;;;;;;CASG,GACH,SAAS,oBAAoB,CAAC,MAAiD,EAAA;IAC7E,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM;;IAGzB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;QAC3C,OAAO;YAAC,IAAI;YAAE,IAAI;SAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;;IAGjE,OAAO,KAAK;AACd;AASA;;;;CAIG,GACG,SAAU,QAAQ,CAAC,OAAwB,EAAA;IAC/C,OAAO,IAAI,kKAAM,CAAC;QAChB,GAAG,EAAE,6JAAI,YAAS,CAAC,UAAU,CAAC;QAC9B,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;YACtD;;aAEG,GACH,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,EAAC,WAAW,GAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;YAE7G;;aAEG,GACH,MAAM,eAAe,GAAG,YAAY,CAAC,IAAI,EAAC,WAAW,GAAI,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAEhG;;;aAGG,GACH,IAAI,CAAC,UAAU,IAAI,eAAe,EAAE;gBAClC;;YAGF,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ;YACvB,MAAM,SAAS,4JAAG,0BAAA,AAAuB,EAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;mBAAG,YAAY;aAAC,CAAC;YAC1E,MAAM,OAAO,IAAG,2KAAA,AAAgB,EAAC,SAAS,CAAC;YAE3C,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAI;;gBAE/B,MAAM,oBAAoB,GAAG,+KAAA,AAAmB,EAC9C,QAAQ,CAAC,GAAG,EACZ,QAAQ,GACR,IAAI,GAAI,IAAI,CAAC,WAAW,CACzB;gBAED,IAAI,SAAkC;gBACtC,IAAI,oBAAwC;gBAE5C,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;;oBAEnC,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;oBACnC,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EACvC,SAAS,EACT,GAAG,CACJ;uBACI,IAAI,oBAAoB,CAAC,MAAM,EAAE;oBACtC,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;oBAE9E,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;wBAC/C;;oBAEF,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;oBACnC,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,QAAQ,CAAC,EAAE,EACX,SAAS,EACT,GAAG,CACJ;;gBAGH,IAAI,SAAS,IAAI,oBAAoB,EAAE;oBACrC,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;oBAElG,IAAI,qBAAqB,CAAC,MAAM,IAAI,CAAC,EAAE;wBACrC,OAAO,KAAK;;oBAGd,MAAM,mBAAmB,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;oBACnF,MAAM,sBAAsB,GAAG,SAAS,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,mBAAmB,CAAC;oBAEpG,IAAI,CAAC,mBAAmB,EAAE;wBACxB,OAAO,KAAK;;oBAGd,MAAM,gBAAgB,wJAAG,WAAA,AAAQ,EAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;oBAEpG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;wBAC3C,OAAO,KAAK;;oBAGd,iBACG,MAAM,EAAC,IAAI,GAAI,IAAI,CAAC,MAAM;qBAE1B,GAAG,EAAC,IAAI,GAAA,CAAK;4BACZ,GAAG,IAAI;4BACP,IAAI,EAAE,sBAAsB,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;4BAC7C,EAAE,EAAE,sBAAsB,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;wBAC1C,CAAA,CAAC;qBAED,MAAM,EAAC,IAAI,IAAG;wBACb,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;4BAC/B,OAAO,IAAI;;wBAGb,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAC/B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAC3B;oBACH,CAAC;qBAEA,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;qBAE3C,MAAM,EAAC,IAAI,GAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;qBAEjD,OAAO,EAAC,IAAI,IAAG;wBACd,IAAI,2KAAA,AAAe,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE;4BACnG;;wBAGF,EAAE,CAAC,OAAO,CACR,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;4BAClB,IAAI,EAAE,IAAI,CAAC,IAAI;wBAChB,CAAA,CAAC,CACH;oBACH,CAAC,CAAC;;YAER,CAAC,CAAC;YAEF,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;gBACpB;;YAGF,OAAO,EAAE;SACV;IACF,CAAA,CAAC;AACJ;ACxKM,SAAU,YAAY,CAAC,OAA4B,EAAA;IACvD,OAAO,6JAAI,SAAM,CAAC;QAChB,GAAG,EAAE,IAAI,qKAAS,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE;YACL,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,KAAI;;gBAChC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACtB,OAAO,KAAK;;gBAGd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAClB,OAAO,KAAK;;gBAGd,IAAI,CAAC,GAAG,KAAK,CAAC,MAAqB;gBACnC,MAAM,GAAG,GAAG,EAAE;gBAEd,MAAO,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAE;oBAC3B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;oBACX,CAAC,GAAG,CAAC,CAAC,UAAyB;;gBAGjC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAC,KAAK,GAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE;oBAC9C,OAAO,KAAK;;gBAGd,MAAM,KAAK,OAAG,qKAAA,AAAa,EAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC1D,MAAM,IAAI,GAAI,KAAK,CAAC,MAA4B;gBAEhD,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,IAAI;gBACrC,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,MAAM;gBAE3C,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;oBAEzB,OAAO,IAAI;;gBAGb,OAAO,KAAK;aACb;QACF,CAAA;IACF,CAAA,CAAC;AACJ;ACtCM,SAAU,YAAY,CAAC,OAA4B,EAAA;IACvD,OAAO,4JAAI,UAAM,CAAC;QAChB,GAAG,EAAE,6JAAI,YAAS,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE;YACL,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,KAAI;gBAClC,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;gBACtB,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;gBAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS;gBAE3B,IAAI,KAAK,EAAE;oBACT,OAAO,KAAK;;gBAGd,IAAI,WAAW,GAAG,EAAE;gBAEpB,KAAK,CAAC,OAAO,CAAC,OAAO,EAAC,IAAI,IAAG;oBAC3B,WAAW,IAAI,IAAI,CAAC,WAAW;gBACjC,CAAC,CAAC;gBAEF,MAAM,IAAI,wJAAG,OAAA,AAAI,EAAC,WAAW,EAAE;oBAAE,eAAe,EAAE,OAAO,CAAC,eAAe;gBAAA,CAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC;gBAEpI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE;oBACzB,OAAO,KAAK;;gBAGd,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;oBACnD,IAAI,EAAE,IAAI,CAAC,IAAI;gBAChB,CAAA,CAAC;aACH;QACF,CAAA;IACF,CAAA,CAAC;AACJ;ACdO,MAAM,UAAU,GAAG;AAkIV,SAAA,YAAY,CAAC,GAAuB,EAAE,SAAoC,EAAA;IACxF,MAAM,gBAAgB,GAAa;QACjC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,KAAK;QACL,KAAK;QACL,MAAM;KACP;IAED,IAAI,SAAS,EAAE;QACb,SAAS,CAAC,OAAO,EAAC,QAAQ,IAAG;YAC3B,MAAM,YAAY,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,MAAM;YAE9E,IAAI,YAAY,EAAE;gBAChB,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;;QAEvC,CAAC,CAAC;;IAGJ,OACE,CAAC,OACE,GAAG,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC,KAAK,CACvD,IAAI,MAAM;IAER,CAAA,OAAA,EAAU,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2C,yCAAA,CAAA,EAC/E,GAAG,CACJ,CACF;AAEL;AAEA;;;CAGG,GACU,MAAA,IAAI,wJAAG,OAAI,CAAC,MAAM,CAAc;IAC3C,IAAI,EAAE,MAAM;IAEZ,QAAQ,EAAE,IAAI;IAEd,WAAW,EAAE,KAAK;IAElB,QAAQ,EAAE,IAAI;IAEd,QAAQ,GAAA;QACN,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;;YAEzD,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;YACnD,OAAO,CAAC,IAAI,CACV,qFAAqF,CACtF;;QAEH,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAC,QAAQ,IAAG;YACxC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;qKAChC,yBAAA,AAAsB,EAAC,QAAQ,CAAC;gBAChC;;YAEF,8KAAA,AAAsB,EAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC;QACnE,CAAC,CAAC;KACH;IAED,SAAS,GAAA;6JACP,QAAA,AAAK,EAAE;KACR;IAED,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ;KAC7B;IAED,UAAU,GAAA;QACR,OAAO;YACL,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;YACjB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,MAAM;YACvB,cAAc,EAAE;gBACd,MAAM,EAAE,QAAQ;gBAChB,GAAG,EAAE,8BAA8B;gBACnC,KAAK,EAAE,IAAI;YACZ,CAAA;YACD,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,GAAK,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC;YAC9D,QAAQ,GAAE,GAAG,GAAI,CAAC,CAAC,GAAG;YACtB,cAAc,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG;SAC7B;KACF;IAED,aAAa,GAAA;QACX,OAAO;YACL,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI;gBACb,SAAS,EAAC,OAAO,EAAA;oBACf,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;iBACpC;YACF,CAAA;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM;YAC5C,CAAA;YACD,GAAG,EAAE;gBACH,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG;YACzC,CAAA;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK;YAC3C,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,SAAS;gBACd,QAAQ,GAAE,GAAG,IAAG;oBACd,MAAM,IAAI,GAAI,GAAmB,CAAC,YAAY,CAAC,MAAM,CAAC;;oBAGtD,IACE,CAAC,QACE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;wBAClC,eAAe,GAAE,GAAG,GAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;wBACnE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;wBACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC9C,CAAA,CAAC,EACF;wBACA,OAAO,KAAK;;oBAEd,OAAO,IAAI;iBACZ;YACF,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;;QAE3B,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE;YAC9C,eAAe,GAAE,IAAI,GAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACrE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;YACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;QAC9C,CAAA,CAAC,EACF;;YAEA,OAAO;gBACL,GAAG;wKACH,mBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;oBAAE,GAAG,cAAc;oBAAE,IAAI,EAAE,EAAE;gBAAA,CAAE,CAAC;gBAC7E,CAAC;aACF;;QAGH,OAAO;YAAC,GAAG;YAAE,2KAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,GACL,UAAU,GAAI,CAAC,EAAE,KAAK,EAAE,KAAI;oBAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;oBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;wBACnC,eAAe,GAAE,GAAG,GAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;wBACnE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;wBACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC9C,CAAA,CAAC,EAAE;wBACF,OAAO,KAAK;;oBAGd,OAAO,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;iBACrF;YAEH,UAAU,GACR,UAAU,GAAI,CAAC,EAAE,KAAK,EAAE,KAAI;oBAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;oBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;wBACnC,eAAe,GAAE,GAAG,GAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;wBACnE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;wBACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC9C,CAAA,CAAC,EAAE;wBACF,OAAO,KAAK;;oBAGd,OAAO,KAAK,GACT,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE;wBAAE,oBAAoB,EAAE,IAAI;oBAAA,CAAE,EAChE,OAAO,CAAC,iBAAiB,EAAE,IAAI,EAC/B,GAAG,EAAE;iBACT;YAEH,SAAS,EACP,IAAM,CAAC,EAAE,KAAK,EAAE,KAAI;oBAClB,OAAO,KAAK,GACT,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;wBAAE,oBAAoB,EAAE,IAAI;oBAAA,CAAE,EACnD,OAAO,CAAC,iBAAiB,EAAE,IAAI,EAC/B,GAAG,EAAE;iBACT;SACJ;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,GAAE,IAAI,IAAG;oBACX,MAAM,UAAU,GAAqB,EAAE;oBAEvC,IAAI,IAAI,EAAE;wBACR,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;wBACnD,MAAM,KAAK,wJAAG,OAAA,AAAI,EAAC,IAAI,CAAC,CAAC,MAAM,EAC7B,IAAI,GAAI,IAAI,CAAC,MAAA,IACR,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE;gCACvC,eAAe,GAAE,IAAI,GAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;gCACxD,SAAS;gCACT,eAAe;4BAChB,CAAA,CAAC,CACL;wBAED,IAAI,KAAK,CAAC,MAAM,EAAE;4BAChB,KAAK,CAAC,OAAO,EAAC,IAAI,GAAI,UAAU,CAAC,IAAI,CAAC;oCACpC,IAAI,EAAE,IAAI,CAAC,KAAK;oCAChB,IAAI,EAAE;wCACJ,IAAI,EAAE,IAAI,CAAC,IAAI;oCAChB,CAAA;oCACD,KAAK,EAAE,IAAI,CAAC,KAAK;gCAClB,CAAA,CAAC,CAAC;;;oBAIP,OAAO,UAAU;iBAClB;gBACD,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,GAAE,KAAK,IAAG;;oBACrB,OAAO;wBACL,IAAI,EAAE,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI;qBACvB;iBACF;aACF,CAAC;SACH;KACF;IAED,qBAAqB,GAAA;QACnB,MAAM,OAAO,GAAa,EAAE;QAC5B,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;QAEnD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,OAAO,CAAC,IAAI,CACV,QAAQ,CAAC;gBACP,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;gBAC7C,QAAQ,GAAE,GAAG,GAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE;wBAC9C,eAAe,GAAE,IAAI,GAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;wBACxD,SAAS;wBACT,eAAe;qBAChB,CAAC;gBACF,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;YAC5C,CAAA,CAAC,CACH;;QAGH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;YACrC,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;YAChB,CAAA,CAAC,CACH;;QAGH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;gBACX,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;gBAC7C,IAAI,EAAE,IAAI,CAAC,IAAI;YAChB,CAAA,CAAC,CACH;;QAGH,OAAO,OAAO;KACf;AACF,CAAA", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 3218, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-placeholder/src/placeholder.ts"], "sourcesContent": ["import { Editor, Extension, isNodeEmpty } from '@tiptap/core'\nimport { Node as ProsemirrorNode } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport interface PlaceholderOptions {\n  /**\n   * **The class name for the empty editor**\n   * @default 'is-editor-empty'\n   */\n  emptyEditorClass: string\n\n  /**\n   * **The class name for empty nodes**\n   * @default 'is-empty'\n   */\n  emptyNodeClass: string\n\n  /**\n   * **The placeholder content**\n   *\n   * You can use a function to return a dynamic placeholder or a string.\n   * @default 'Write something …'\n   */\n  placeholder:\n    | ((PlaceholderProps: {\n        editor: Editor\n        node: ProsemirrorNode\n        pos: number\n        hasAnchor: boolean\n      }) => string)\n    | string\n\n  /**\n   * See https://github.com/ueberdosis/tiptap/pull/5278 for more information.\n   * @deprecated This option is no longer respected and this type will be removed in the next major version.\n   */\n  considerAnyAsEmpty?: boolean\n\n  /**\n   * **Checks if the placeholder should be only shown when the editor is editable.**\n   *\n   * If true, the placeholder will only be shown when the editor is editable.\n   * If false, the placeholder will always be shown.\n   * @default true\n   */\n  showOnlyWhenEditable: boolean\n\n  /**\n   * **Checks if the placeholder should be only shown when the current node is empty.**\n   *\n   * If true, the placeholder will only be shown when the current node is empty.\n   * If false, the placeholder will be shown when any node is empty.\n   * @default true\n   */\n  showOnlyCurrent: boolean\n\n  /**\n   * **Controls if the placeholder should be shown for all descendents.**\n   *\n   * If true, the placeholder will be shown for all descendents.\n   * If false, the placeholder will only be shown for the current node.\n   * @default false\n   */\n  includeChildren: boolean\n}\n\n/**\n * This extension allows you to add a placeholder to your editor.\n * A placeholder is a text that appears when the editor or a node is empty.\n * @see https://www.tiptap.dev/api/extensions/placeholder\n */\nexport const Placeholder = Extension.create<PlaceholderOptions>({\n  name: 'placeholder',\n\n  addOptions() {\n    return {\n      emptyEditorClass: 'is-editor-empty',\n      emptyNodeClass: 'is-empty',\n      placeholder: 'Write something …',\n      showOnlyWhenEditable: true,\n      showOnlyCurrent: true,\n      includeChildren: false,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('placeholder'),\n        props: {\n          decorations: ({ doc, selection }) => {\n            const active = this.editor.isEditable || !this.options.showOnlyWhenEditable\n            const { anchor } = selection\n            const decorations: Decoration[] = []\n\n            if (!active) {\n              return null\n            }\n\n            const isEmptyDoc = this.editor.isEmpty\n\n            doc.descendants((node, pos) => {\n              const hasAnchor = anchor >= pos && anchor <= pos + node.nodeSize\n              const isEmpty = !node.isLeaf && isNodeEmpty(node)\n\n              if ((hasAnchor || !this.options.showOnlyCurrent) && isEmpty) {\n                const classes = [this.options.emptyNodeClass]\n\n                if (isEmptyDoc) {\n                  classes.push(this.options.emptyEditorClass)\n                }\n\n                const decoration = Decoration.node(pos, pos + node.nodeSize, {\n                  class: classes.join(' '),\n                  'data-placeholder':\n                    typeof this.options.placeholder === 'function'\n                      ? this.options.placeholder({\n                        editor: this.editor,\n                        node,\n                        pos,\n                        hasAnchor,\n                      })\n                      : this.options.placeholder,\n                })\n\n                decorations.push(decoration)\n              }\n\n              return this.options.includeChildren\n            })\n\n            return DecorationSet.create(doc, decorations)\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;AAmEA;;;;CAIG,GACU,MAAA,WAAW,wJAAG,YAAS,CAAC,MAAM,CAAqB;IAC9D,IAAI,EAAE,aAAa;IAEnB,UAAU,GAAA;QACR,OAAO;YACL,gBAAgB,EAAE,iBAAiB;YACnC,cAAc,EAAE,UAAU;YAC1B,WAAW,EAAE,mBAAmB;YAChC,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,KAAK;SACvB;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;YACL,6JAAI,SAAM,CAAC;gBACT,GAAG,EAAE,6JAAI,YAAS,CAAC,aAAa,CAAC;gBACjC,KAAK,EAAE;oBACL,WAAW,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,KAAI;wBAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB;wBAC3E,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;wBAC5B,MAAM,WAAW,GAAiB,EAAE;wBAEpC,IAAI,CAAC,MAAM,EAAE;4BACX,OAAO,IAAI;;wBAGb,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;wBAEtC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,GAAG,KAAI;4BAC5B,MAAM,SAAS,GAAG,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ;4BAChE,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,6JAAI,cAAA,AAAW,EAAC,IAAI,CAAC;4BAEjD,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,OAAO,EAAE;gCAC3D,MAAM,OAAO,GAAG;oCAAC,IAAI,CAAC,OAAO,CAAC,cAAc;iCAAC;gCAE7C,IAAI,UAAU,EAAE;oCACd,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;;gCAG7C,MAAM,UAAU,2JAAG,aAAU,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE;oCAC3D,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;oCACxB,kBAAkB,EAChB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,aAChC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;wCACzB,MAAM,EAAE,IAAI,CAAC,MAAM;wCACnB,IAAI;wCACJ,GAAG;wCACH,SAAS;qCACV,IACC,IAAI,CAAC,OAAO,CAAC,WAAW;gCAC/B,CAAA,CAAC;gCAEF,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;;4BAG9B,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;wBACrC,CAAC,CAAC;wBAEF,+JAAO,gBAAa,CAAC,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC;qBAC9C;gBACF,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3297, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-task-item/src/task-item.ts"], "sourcesContent": ["import {\n  KeyboardShortcutCommand, mergeAttributes, Node, wrappingInputRule,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nexport interface TaskItemOptions {\n  /**\n   * A callback function that is called when the checkbox is clicked while the editor is in readonly mode.\n   * @param node The prosemirror node of the task item\n   * @param checked The new checked state\n   * @returns boolean\n   */\n  onReadOnlyChecked?: (node: ProseMirrorNode, checked: boolean) => boolean\n\n  /**\n   * Controls whether the task items can be nested or not.\n   * @default false\n   * @example true\n   */\n  nested: boolean\n\n  /**\n   * HTML attributes to add to the task item element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * The node type for taskList nodes\n   * @default 'taskList'\n   * @example 'myCustomTaskList'\n   */\n  taskListTypeName: string\n\n  /**\n   * Accessibility options for the task item.\n   * @default {}\n   * @example\n   * ```js\n   * {\n   *   checkboxLabel: (node) => `Task item: ${node.textContent || 'empty task item'}`\n   * }\n   */\n  a11y?: {\n    checkboxLabel?: (node: ProseMirrorNode, checked: boolean) => string\n  }\n}\n\n/**\n * Matches a task item to a - [ ] on input.\n */\nexport const inputRegex = /^\\s*(\\[([( |x])?\\])\\s$/\n\n/**\n * This extension allows you to create task items.\n * @see https://www.tiptap.dev/api/nodes/task-item\n */\nexport const TaskItem = Node.create<TaskItemOptions>({\n  name: 'taskItem',\n\n  addOptions() {\n    return {\n      nested: false,\n      HTMLAttributes: {},\n      taskListTypeName: 'taskList',\n      a11y: undefined,\n    }\n  },\n\n  content() {\n    return this.options.nested ? 'paragraph block*' : 'paragraph+'\n  },\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      checked: {\n        default: false,\n        keepOnSplit: false,\n        parseHTML: element => {\n          const dataChecked = element.getAttribute('data-checked')\n\n          return dataChecked === '' || dataChecked === 'true'\n        },\n        renderHTML: attributes => ({\n          'data-checked': attributes.checked,\n        }),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `li[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'li',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {\n        'data-type': this.name,\n      }),\n      [\n        'label',\n        [\n          'input',\n          {\n            type: 'checkbox',\n            checked: node.attrs.checked ? 'checked' : null,\n          },\n        ],\n        ['span'],\n      ],\n      ['div', 0],\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    const shortcuts: {\n      [key: string]: KeyboardShortcutCommand\n    } = {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n\n    if (!this.options.nested) {\n      return shortcuts\n    }\n\n    return {\n      ...shortcuts,\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n    }\n  },\n\n  addNodeView() {\n    return ({\n      node, HTMLAttributes, getPos, editor,\n    }) => {\n      const listItem = document.createElement('li')\n      const checkboxWrapper = document.createElement('label')\n      const checkboxStyler = document.createElement('span')\n      const checkbox = document.createElement('input')\n      const content = document.createElement('div')\n\n      const updateA11Y = () => {\n        checkbox.ariaLabel = this.options.a11y?.checkboxLabel?.(node, checkbox.checked)\n          || `Task item checkbox for ${node.textContent || 'empty task item'}`\n      }\n\n      updateA11Y()\n\n      checkboxWrapper.contentEditable = 'false'\n      checkbox.type = 'checkbox'\n      checkbox.addEventListener('mousedown', event => event.preventDefault())\n      checkbox.addEventListener('change', event => {\n        // if the editor isn’t editable and we don't have a handler for\n        // readonly checks we have to undo the latest change\n        if (!editor.isEditable && !this.options.onReadOnlyChecked) {\n          checkbox.checked = !checkbox.checked\n\n          return\n        }\n\n        const { checked } = event.target as any\n\n        if (editor.isEditable && typeof getPos === 'function') {\n          editor\n            .chain()\n            .focus(undefined, { scrollIntoView: false })\n            .command(({ tr }) => {\n              const position = getPos()\n\n              if (typeof position !== 'number') {\n                return false\n              }\n              const currentNode = tr.doc.nodeAt(position)\n\n              tr.setNodeMarkup(position, undefined, {\n                ...currentNode?.attrs,\n                checked,\n              })\n\n              return true\n            })\n            .run()\n        }\n        if (!editor.isEditable && this.options.onReadOnlyChecked) {\n          // Reset state if onReadOnlyChecked returns false\n          if (!this.options.onReadOnlyChecked(node, checked)) {\n            checkbox.checked = !checkbox.checked\n          }\n        }\n      })\n\n      Object.entries(this.options.HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      listItem.dataset.checked = node.attrs.checked\n      checkbox.checked = node.attrs.checked\n\n      checkboxWrapper.append(checkbox, checkboxStyler)\n      listItem.append(checkboxWrapper, content)\n\n      Object.entries(HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      return {\n        dom: listItem,\n        contentDOM: content,\n        update: updatedNode => {\n          if (updatedNode.type !== this.type) {\n            return false\n          }\n\n          listItem.dataset.checked = updatedNode.attrs.checked\n          checkbox.checked = updatedNode.attrs.checked\n          updateA11Y()\n\n          return true\n        },\n      }\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          checked: match[match.length - 1] === 'x',\n        }),\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;AAiDA;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,QAAQ,wJAAG,OAAI,CAAC,MAAM,CAAkB;IACnD,IAAI,EAAE,UAAU;IAEhB,UAAU,GAAA;QACR,OAAO;YACL,MAAM,EAAE,KAAK;YACb,cAAc,EAAE,CAAA,CAAE;YAClB,gBAAgB,EAAE,UAAU;YAC5B,IAAI,EAAE,SAAS;SAChB;KACF;IAED,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,kBAAkB,GAAG,YAAY;KAC/D;IAED,QAAQ,EAAE,IAAI;IAEd,aAAa,GAAA;QACX,OAAO;YACL,OAAO,EAAE;gBACP,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,KAAK;gBAClB,SAAS,GAAE,OAAO,IAAG;oBACnB,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC;oBAExD,OAAO,WAAW,KAAK,EAAE,IAAI,WAAW,KAAK,MAAM;iBACpD;gBACD,UAAU,GAAE,UAAU,GAAA,CAAK;wBACzB,cAAc,EAAE,UAAU,CAAC,OAAO;qBACnC,CAAC;YACH,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,CAAA,cAAA,EAAiB,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA;gBACnC,QAAQ,EAAE,EAAE;YACb,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,EAAA;QACjC,OAAO;YACL,IAAI;YACJ,2KAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,EAAE;gBAC3D,WAAW,EAAE,IAAI,CAAC,IAAI;aACvB,CAAC;YACF;gBACE,OAAO;gBACP;oBACE,OAAO;oBACP;wBACE,IAAI,EAAE,UAAU;wBAChB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,SAAS,GAAG,IAAI;oBAC/C,CAAA;iBACF;gBACD;oBAAC,MAAM;iBAAC;aACT;YACD;gBAAC,KAAK;gBAAE,CAAC;aAAC;SACX;KACF;IAED,oBAAoB,GAAA;QAClB,MAAM,SAAS,GAEX;YACF,KAAK,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1D,WAAW,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;SAChE;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACxB,OAAO,SAAS;;QAGlB,OAAO;YACL,GAAG,SAAS;YACZ,GAAG,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;SACxD;KACF;IAED,WAAW,GAAA;QACT,OAAO,CAAC,EACN,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EACrC,KAAI;YACH,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;YAC7C,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;YACvD,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC;YACrD,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;YAChD,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;YAE7C,MAAM,UAAU,GAAG,MAAK;;gBACtB,QAAQ,CAAC,SAAS,GAAG,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,aAAa,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,KAC1E,CAAA,uBAAA,EAA0B,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;YACxE,CAAC;YAED,UAAU,EAAE;YAEZ,eAAe,CAAC,eAAe,GAAG,OAAO;YACzC,QAAQ,CAAC,IAAI,GAAG,UAAU;YAC1B,QAAQ,CAAC,gBAAgB,CAAC,WAAW,GAAE,KAAK,GAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACvE,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,GAAE,KAAK,IAAG;;;gBAG1C,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBACzD,QAAQ,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO;oBAEpC;;gBAGF,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,MAAa;gBAEvC,IAAI,MAAM,CAAC,UAAU,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;oBACrD,OACG,KAAK,GACL,KAAK,CAAC,SAAS,EAAE;wBAAE,cAAc,EAAE,KAAK;oBAAA,CAAE,EAC1C,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;wBAClB,MAAM,QAAQ,GAAG,MAAM,EAAE;wBAEzB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;4BAChC,OAAO,KAAK;;wBAEd,MAAM,WAAW,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;wBAE3C,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE;4BACpC,GAAG,WAAW,KAAX,IAAA,IAAA,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,KAAK;4BACrB,OAAO;wBACR,CAAA,CAAC;wBAEF,OAAO,IAAI;oBACb,CAAC,EACA,GAAG,EAAE;;gBAEV,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;;oBAExD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;wBAClD,QAAQ,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO;;;YAG1C,CAAC,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;gBACnE,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;YACnC,CAAC,CAAC;YAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;YAC7C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;YAErC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC;YAChD,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,OAAO,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;gBACtD,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;YACnC,CAAC,CAAC;YAEF,OAAO;gBACL,GAAG,EAAE,QAAQ;gBACb,UAAU,EAAE,OAAO;gBACnB,MAAM,GAAE,WAAW,IAAG;oBACpB,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;wBAClC,OAAO,KAAK;;oBAGd,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO;oBACpD,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO;oBAC5C,UAAU,EAAE;oBAEZ,OAAO,IAAI;iBACZ;aACF;QACH,CAAC;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,oBAAA,AAAiB,EAAC;gBAChB,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,GAAE,KAAK,GAAA,CAAK;wBACvB,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;qBACzC,CAAC;aACH,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3476, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-task-list/src/task-list.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TaskListOptions {\n  /**\n   * The node type name for a task item.\n   * @default 'taskItem'\n   * @example 'myCustomTaskItem'\n   */\n  itemTypeName: string,\n\n  /**\n   * The HTML attributes for a task list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    taskList: {\n      /**\n       * Toggle a task list\n       * @example editor.commands.toggleTaskList()\n       */\n      toggleTaskList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create task lists.\n * @see https://www.tiptap.dev/api/nodes/task-list\n */\nexport const TaskList = Node.create<TaskListOptions>({\n  name: 'taskList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'taskItem',\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `ul[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, { 'data-type': this.name }), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleTaskList: () => ({ commands }) => {\n        return commands.toggleList(this.name, this.options.itemTypeName)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-9': () => this.editor.commands.toggleTaskList(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;AA8BA;;;CAGG,GACU,MAAA,QAAQ,wJAAG,OAAI,CAAC,MAAM,CAAkB;IACnD,IAAI,EAAE,UAAU;IAEhB,UAAU,GAAA;QACR,OAAO;YACL,YAAY,EAAE,UAAU;YACxB,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,KAAK,EAAE,YAAY;IAEnB,OAAO,GAAA;QACL,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAA,CAAA,CAAG;KACvC;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,CAAA,cAAA,EAAiB,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA;gBACnC,QAAQ,EAAE,EAAE;YACb,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,EAAE;gBAAE,WAAW,EAAE,IAAI,CAAC,IAAI;YAAA,CAAE,CAAC;YAAE,CAAC;SAAC;KAC3G;IAED,WAAW,GAAA;QACT,OAAO;YACL,cAAc,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACrC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;iBACjE;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE;SAC3D;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-underline/src/underline.ts"], "sourcesContent": ["import { Mark, mergeAttributes } from '@tiptap/core'\n\nexport interface UnderlineOptions {\n  /**\n   * HTML attributes to add to the underline element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    underline: {\n      /**\n       * Set an underline mark\n       * @example editor.commands.setUnderline()\n       */\n      setUnderline: () => ReturnType,\n      /**\n       * Toggle an underline mark\n       * @example editor.commands.toggleUnderline()\n       */\n      toggleUnderline: () => ReturnType,\n      /**\n       * Unset an underline mark\n       * @example editor.commands.unsetUnderline()\n       */\n      unsetUnderline: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create underline text.\n * @see https://www.tiptap.dev/api/marks/underline\n */\nexport const Underline = Mark.create<UnderlineOptions>({\n  name: 'underline',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'u',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('underline') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['u', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setUnderline: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleUnderline: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetUnderline: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-u': () => this.editor.commands.toggleUnderline(),\n      'Mod-U': () => this.editor.commands.toggleUnderline(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;AAiCA;;;CAGG,GACU,MAAA,SAAS,wJAAG,OAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,GAAG;YACT,CAAA;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,KAAK;gBAChB,QAAQ,GAAE,KAAK,GAAM,KAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAA,CAAE,GAAG,KAAK,CAAC;YAC1E,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,GAAG;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACnC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,eAAe,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACtC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,cAAc,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACrC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;YACrD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;SACtD;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3608, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-blockquote/src/blockquote.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nexport interface BlockquoteOptions {\n  /**\n   * HTML attributes to add to the blockquote element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blockQuote: {\n      /**\n       * Set a blockquote node\n       */\n      setBlockquote: () => ReturnType,\n      /**\n       * Toggle a blockquote node\n       */\n      toggleBlockquote: () => ReturnType,\n      /**\n       * Unset a blockquote node\n       */\n      unsetBlockquote: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a blockquote to a `>` as input.\n */\nexport const inputRegex = /^\\s*>\\s$/\n\n/**\n * This extension allows you to create blockquotes.\n * @see https://tiptap.dev/api/nodes/blockquote\n */\nexport const Blockquote = Node.create<BlockquoteOptions>({\n\n  name: 'blockquote',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  group: 'block',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      { tag: 'blockquote' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['blockquote', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBlockquote: () => ({ commands }) => {\n        return commands.wrapIn(this.name)\n      },\n      toggleBlockquote: () => ({ commands }) => {\n        return commands.toggleWrap(this.name)\n      },\n      unsetBlockquote: () => ({ commands }) => {\n        return commands.lift(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-b': () => this.editor.commands.toggleBlockquote(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;AA8BA;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,UAAU,wJAAG,OAAI,CAAC,MAAM,CAAoB;IAEvD,IAAI,EAAE,YAAY;IAElB,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,OAAO,EAAE,QAAQ;IAEjB,KAAK,EAAE,OAAO;IAEd,QAAQ,EAAE,IAAI;IAEd,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,YAAY;YAAA,CAAE;SACtB;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,YAAY;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACvF;IAED,WAAW,GAAA;QACT,OAAO;YACL,aAAa,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACpC,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;iBAClC;YACD,gBAAgB,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACvC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,eAAe,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACtC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;iBAChC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE;SAC7D;KACF;IAED,aAAa,GAAA;QACX,OAAO;YACL,6KAAA,AAAiB,EAAC;gBAChB,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3680, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-bold/src/bold.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType,\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType,\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['strong', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBold: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleBold: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetBold: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAmCA;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;;CAGG,GACU,MAAA,IAAI,uJAAG,QAAI,CAAC,MAAM,CAAc;IAC3C,IAAI,EAAE,MAAM;IAEZ,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,QAAQ;YACd,CAAA;YACD;gBACE,GAAG,EAAE,GAAG;gBACR,QAAQ,GAAE,IAAI,GAAK,IAAoB,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI;YAC9E,CAAA;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAChD,CAAA;YACD;gBACE,KAAK,EAAE,aAAa;gBACpB,QAAQ,GAAE,KAAK,GAAI,2BAA2B,CAAC,IAAI,CAAC,KAAe,CAAC,IAAI,IAAI;YAC7E,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,QAAQ;gBAAE,uKAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACnF;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,UAAU,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACjC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;YAChD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;SACjD;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;aACF,wKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;qKACF,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3790, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-bullet-list/src/bullet-list.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface BulletListOptions {\n  /**\n   * The node name for the list items\n   * @default 'listItem'\n   * @example 'paragraph'\n   */\n  itemTypeName: string,\n\n  /**\n   * HTML attributes to add to the bullet list element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * Keep the marks when splitting the list\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean,\n\n  /**\n   * Keep the attributes when splitting the list\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bulletList: {\n      /**\n       * Toggle a bullet list\n       */\n      toggleBulletList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nexport const inputRegex = /^\\s*([-+*])\\s$/\n\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nexport const BulletList = Node.create<BulletListOptions>({\n  name: 'bulletList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [\n      { tag: 'ul' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleBulletList: () => ({ commands, chain }) => {\n        if (this.options.keepAttributes) {\n          return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run()\n        }\n        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: inputRegex,\n      type: this.type,\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: () => { return this.editor.getAttributes(TextStyleName) },\n        editor: this.editor,\n      })\n    }\n    return [\n      inputRule,\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,YAAY,GAAG,UAAU;AAC/B,MAAM,aAAa,GAAG,WAAW;AA2CjC;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;;;CAKG,GACU,MAAA,UAAU,wJAAG,OAAI,CAAC,MAAM,CAAoB;IACvD,IAAI,EAAE,YAAY;IAElB,UAAU,GAAA;QACR,OAAO;YACL,YAAY,EAAE,UAAU;YACxB,cAAc,EAAE,CAAA,CAAE;YAClB,SAAS,EAAE,KAAK;YAChB,cAAc,EAAE,KAAK;SACtB;KACF;IAED,KAAK,EAAE,YAAY;IAEnB,OAAO,GAAA;QACL,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAA,CAAA,CAAG;KACvC;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,IAAI;YAAA,CAAE;SACd;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC/E;IAED,WAAW,GAAA;QACT,OAAO;YACL,gBAAgB,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAI;oBAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;wBAC/B,OAAO,KAAK,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE;;oBAExK,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;iBACzF;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE;SAC7D;KACF;IAED,aAAa,GAAA;QACX,IAAI,SAAS,4JAAG,oBAAA,AAAiB,EAAC;YAChC,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI;QAChB,CAAA,CAAC;QAEF,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YACzD,SAAS,4JAAG,oBAAA,AAAiB,EAAC;gBAC5B,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC3C,aAAa,EAAE,MAAK;oBAAG,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAA;gBAAA,CAAE;gBACxE,MAAM,EAAE,IAAI,CAAC,MAAM;YACpB,CAAA,CAAC;;QAEJ,OAAO;YACL,SAAS;SACV;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3880, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-code/src/code.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface CodeOptions {\n  /**\n   * The HTML attributes applied to the code element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    code: {\n      /**\n       * Set a code mark\n       */\n      setCode: () => ReturnType,\n      /**\n       * Toggle inline code\n       */\n      toggleCode: () => ReturnType,\n      /**\n       * Unset a code mark\n       */\n      unsetCode: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Regular expressions to match inline code blocks enclosed in backticks.\n *  It matches:\n *     - An opening backtick, followed by\n *     - Any text that doesn't include a backtick (captured for marking), followed by\n *     - A closing backtick.\n *  This ensures that any text between backticks is formatted as code,\n *  regardless of the surrounding characters (exception being another backtick).\n */\nexport const inputRegex = /(^|[^`])`([^`]+)`(?!`)/\n\n/**\n * Matches inline code while pasting.\n */\nexport const pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g\n\n/**\n * This extension allows you to mark text as inline code.\n * @see https://tiptap.dev/api/marks/code\n */\nexport const Code = Mark.create<CodeOptions>({\n  name: 'code',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  excludes: '_',\n\n  code: true,\n\n  exitable: true,\n\n  parseHTML() {\n    return [\n      { tag: 'code' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['code', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setCode: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleCode: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetCode: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-e': () => this.editor.commands.toggleCode(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;AAmCA;;;;;;;;CAQG,GACI,MAAM,UAAU,GAAG;AAE1B;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,IAAI,wJAAG,OAAI,CAAC,MAAM,CAAc;IAC3C,IAAI,EAAE,MAAM;IAEZ,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,QAAQ,EAAE,GAAG;IAEb,IAAI,EAAE,IAAI;IAEV,QAAQ,EAAE,IAAI;IAEd,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,MAAM;YAAA,CAAE;SAChB;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,MAAM;YAAE,2KAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACjF;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,UAAU,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACjC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;SACjD;KACF;IAED,aAAa,GAAA;QACX,OAAO;aACL,wKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3970, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-code-block/src/code-block.ts"], "sourcesContent": ["import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\nimport {\n  Plugin,\n  Plugin<PERSON>ey,\n  Selection,\n  TextSelection,\n} from '@tiptap/pm/state'\n\nexport interface CodeBlockOptions {\n  /**\n   * Adds a prefix to language classes that are applied to code tags.\n   * @default 'language-'\n   */\n  languageClassPrefix: string\n  /**\n   * Define whether the node should be exited on triple enter.\n   * @default true\n   */\n  exitOnTripleEnter: boolean\n  /**\n   * Define whether the node should be exited on arrow down if there is no node after it.\n   * @default true\n   */\n  exitOnArrowDown: boolean\n  /**\n   * The default language.\n   * @default null\n   * @example 'js'\n   */\n  defaultLanguage: string | null | undefined\n  /**\n   * Custom HTML attributes that should be added to the rendered HTML tag.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    codeBlock: {\n      /**\n       * Set a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.setCodeBlock({ language: 'javascript' })\n       */\n      setCodeBlock: (attributes?: { language: string }) => ReturnType\n      /**\n       * Toggle a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.toggleCodeBlock({ language: 'javascript' })\n       */\n      toggleCodeBlock: (attributes?: { language: string }) => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a code block with backticks.\n */\nexport const backtickInputRegex = /^```([a-z]+)?[\\s\\n]$/\n\n/**\n * Matches a code block with tildes.\n */\nexport const tildeInputRegex = /^~~~([a-z]+)?[\\s\\n]$/\n\n/**\n * This extension allows you to create code blocks.\n * @see https://tiptap.dev/api/nodes/code-block\n */\nexport const CodeBlock = Node.create<CodeBlockOptions>({\n  name: 'codeBlock',\n\n  addOptions() {\n    return {\n      languageClassPrefix: 'language-',\n      exitOnTripleEnter: true,\n      exitOnArrowDown: true,\n      defaultLanguage: null,\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'text*',\n\n  marks: '',\n\n  group: 'block',\n\n  code: true,\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      language: {\n        default: this.options.defaultLanguage,\n        parseHTML: element => {\n          const { languageClassPrefix } = this.options\n          const classNames = [...(element.firstElementChild?.classList || [])]\n          const languages = classNames\n            .filter(className => className.startsWith(languageClassPrefix))\n            .map(className => className.replace(languageClassPrefix, ''))\n          const language = languages[0]\n\n          if (!language) {\n            return null\n          }\n\n          return language\n        },\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'pre',\n        preserveWhitespace: 'full',\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'pre',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),\n      [\n        'code',\n        {\n          class: node.attrs.language\n            ? this.options.languageClassPrefix + node.attrs.language\n            : null,\n        },\n        0,\n      ],\n    ]\n  },\n\n  addCommands() {\n    return {\n      setCodeBlock:\n        attributes => ({ commands }) => {\n          return commands.setNode(this.name, attributes)\n        },\n      toggleCodeBlock:\n        attributes => ({ commands }) => {\n          return commands.toggleNode(this.name, 'paragraph', attributes)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-c': () => this.editor.commands.toggleCodeBlock(),\n\n      // remove code block when at start of document or code block is empty\n      Backspace: () => {\n        const { empty, $anchor } = this.editor.state.selection\n        const isAtStart = $anchor.pos === 1\n\n        if (!empty || $anchor.parent.type.name !== this.name) {\n          return false\n        }\n\n        if (isAtStart || !$anchor.parent.textContent.length) {\n          return this.editor.commands.clearNodes()\n        }\n\n        return false\n      },\n\n      // exit node on triple enter\n      Enter: ({ editor }) => {\n        if (!this.options.exitOnTripleEnter) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n        const endsWithDoubleNewline = $from.parent.textContent.endsWith('\\n\\n')\n\n        if (!isAtEnd || !endsWithDoubleNewline) {\n          return false\n        }\n\n        return editor\n          .chain()\n          .command(({ tr }) => {\n            tr.delete($from.pos - 2, $from.pos)\n\n            return true\n          })\n          .exitCode()\n          .run()\n      },\n\n      // exit node on arrow down\n      ArrowDown: ({ editor }) => {\n        if (!this.options.exitOnArrowDown) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection, doc } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n\n        if (!isAtEnd) {\n          return false\n        }\n\n        const after = $from.after()\n\n        if (after === undefined) {\n          return false\n        }\n\n        const nodeAfter = doc.nodeAt(after)\n\n        if (nodeAfter) {\n          return editor.commands.command(({ tr }) => {\n            tr.setSelection(Selection.near(doc.resolve(after)))\n            return true\n          })\n        }\n\n        return editor.commands.exitCode()\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      textblockTypeInputRule({\n        find: backtickInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n      textblockTypeInputRule({\n        find: tildeInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // this plugin creates a code block for pasted content from VS Code\n      // we can also detect the copied code language\n      new Plugin({\n        key: new PluginKey('codeBlockVSCodeHandler'),\n        props: {\n          handlePaste: (view, event) => {\n            if (!event.clipboardData) {\n              return false\n            }\n\n            // don’t create a new code block within code blocks\n            if (this.editor.isActive(this.type.name)) {\n              return false\n            }\n\n            const text = event.clipboardData.getData('text/plain')\n            const vscode = event.clipboardData.getData('vscode-editor-data')\n            const vscodeData = vscode ? JSON.parse(vscode) : undefined\n            const language = vscodeData?.mode\n\n            if (!text || !language) {\n              return false\n            }\n\n            const { tr, schema } = view.state\n\n            // prepare a text node\n            // strip carriage return chars from text pasted as code\n            // see: https://github.com/ProseMirror/prosemirror-view/commit/a50a6bcceb4ce52ac8fcc6162488d8875613aacd\n            const textNode = schema.text(text.replace(/\\r\\n?/g, '\\n'))\n\n            // create a code block with the text node\n            // replace selection with the code block\n            tr.replaceSelectionWith(this.type.create({ language }, textNode))\n\n            if (tr.selection.$from.parent.type !== this.type) {\n              // put cursor inside the newly created code block\n              tr.setSelection(TextSelection.near(tr.doc.resolve(Math.max(0, tr.selection.from - 2))))\n            }\n\n            // store meta information\n            // this is useful for other plugins that depends on the paste event\n            // like the paste rule plugin\n            tr.setMeta('paste', true)\n\n            view.dispatch(tr)\n\n            return true\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;AAyDA;;CAEG,GACI,MAAM,kBAAkB,GAAG;AAElC;;CAEG,GACI,MAAM,eAAe,GAAG;AAE/B;;;CAGG,GACU,MAAA,SAAS,wJAAG,OAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;YACL,mBAAmB,EAAE,WAAW;YAChC,iBAAiB,EAAE,IAAI;YACvB,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,OAAO,EAAE,OAAO;IAEhB,KAAK,EAAE,EAAE;IAET,KAAK,EAAE,OAAO;IAEd,IAAI,EAAE,IAAI;IAEV,QAAQ,EAAE,IAAI;IAEd,aAAa,GAAA;QACX,OAAO;YACL,QAAQ,EAAE;gBACR,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;gBACrC,SAAS,GAAE,OAAO,IAAG;;oBACnB,MAAM,EAAE,mBAAmB,EAAE,GAAG,IAAI,CAAC,OAAO;oBAC5C,MAAM,UAAU,GAAG,CAAC;2BAAI,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,iBAAiB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,KAAI,EAAE,CAAC;qBAAC;oBACpE,MAAM,SAAS,GAAG,WACf,MAAM,EAAC,SAAS,GAAI,SAAS,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAC7D,GAAG,EAAC,SAAS,GAAI,SAAS,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;oBAC/D,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC;oBAE7B,IAAI,CAAC,QAAQ,EAAE;wBACb,OAAO,IAAI;;oBAGb,OAAO,QAAQ;iBAChB;gBACD,QAAQ,EAAE,KAAK;YAChB,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,KAAK;gBACV,kBAAkB,EAAE,MAAM;YAC3B,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,EAAA;QACjC,OAAO;YACL,KAAK;qKACL,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAC5D;gBACE,MAAM;gBACN;oBACE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,QAAA,GACd,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAA,GAC9C,IAAI;gBACT,CAAA;gBACD,CAAC;aACF;SACF;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,GACV,UAAU,GAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC7B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/C;YACH,eAAe,GACb,UAAU,GAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC7B,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC;iBAC/D;SACJ;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,WAAW,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;;YAGzD,SAAS,EAAE,MAAK;gBACd,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS;gBACtD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC;gBAEnC,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBACpD,OAAO,KAAK;;gBAGd,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE;oBACnD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;;gBAG1C,OAAO,KAAK;aACb;;YAGD,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;gBACpB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBACnC,OAAO,KAAK;;gBAGd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;gBACxB,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;gBAC3B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,SAAS;gBAElC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBAC7C,OAAO,KAAK;;gBAGd,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC;gBAChE,MAAM,qBAAqB,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAEvE,IAAI,CAAC,OAAO,IAAI,CAAC,qBAAqB,EAAE;oBACtC,OAAO,KAAK;;gBAGd,OAAO,OACJ,KAAK,GACL,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;oBAClB,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC;oBAEnC,OAAO,IAAI;gBACb,CAAC,EACA,QAAQ,GACR,GAAG,EAAE;aACT;;YAGD,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;gBACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;oBACjC,OAAO,KAAK;;gBAGd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;gBACxB,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,KAAK;gBAChC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,SAAS;gBAElC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBAC7C,OAAO,KAAK;;gBAGd,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC;gBAEhE,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO,KAAK;;gBAGd,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAE3B,IAAI,KAAK,KAAK,SAAS,EAAE;oBACvB,OAAO,KAAK;;gBAGd,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;gBAEnC,IAAI,SAAS,EAAE;oBACb,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;wBACxC,EAAE,CAAC,YAAY,0JAAC,YAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;wBACnD,OAAO,IAAI;oBACb,CAAC,CAAC;;gBAGJ,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;aAClC;SACF;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,yBAAA,AAAsB,EAAC;gBACrB,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,EAAE,KAAK,IAAA,CAAK;wBACvB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;qBACnB,CAAC;aACH,CAAC;qKACF,yBAAA,AAAsB,EAAC;gBACrB,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,GAAE,KAAK,GAAA,CAAK;wBACvB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;qBACnB,CAAC;aACH,CAAC;SACH;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;;;YAGL,6JAAI,SAAM,CAAC;gBACT,GAAG,EAAE,6JAAI,YAAS,CAAC,wBAAwB,CAAC;gBAC5C,KAAK,EAAE;oBACL,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,KAAI;wBAC3B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;4BACxB,OAAO,KAAK;;;wBAId,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;4BACxC,OAAO,KAAK;;wBAGd,MAAM,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC;wBACtD,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC;wBAChE,MAAM,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,SAAS;wBAC1D,MAAM,QAAQ,GAAG,UAAU,KAAA,IAAA,IAAV,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,IAAI;wBAEjC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;4BACtB,OAAO,KAAK;;wBAGd,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;;;;wBAKjC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;;;wBAI1D,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;4BAAE,QAAQ;wBAAA,CAAE,EAAE,QAAQ,CAAC,CAAC;wBAEjE,IAAI,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;;4BAEhD,EAAE,CAAC,YAAY,0JAAC,gBAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;wBAMzF,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;wBAEzB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAEjB,OAAO,IAAI;qBACZ;gBACF,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4198, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-document/src/document.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n"], "names": [], "mappings": ";;;;;;AAEA;;;CAGG,GACU,MAAA,QAAQ,wJAAG,OAAI,CAAC,MAAM,CAAC;IAClC,IAAI,EAAE,KAAK;IACX,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,QAAQ;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4220, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-dropcursor/src/dropcursor.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { dropCursor } from '@tiptap/pm/dropcursor'\n\nexport interface DropcursorOptions {\n  /**\n   * The color of the drop cursor\n   * @default 'currentColor'\n   * @example 'red'\n   */\n  color: string | undefined,\n\n  /**\n   * The width of the drop cursor\n   * @default 1\n   * @example 2\n  */\n  width: number | undefined,\n\n  /**\n   * The class of the drop cursor\n   * @default undefined\n   * @example 'drop-cursor'\n  */\n  class: string | undefined,\n}\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * inbetween nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nexport const Dropcursor = Extension.create<DropcursorOptions>({\n  name: 'dropCursor',\n\n  addOptions() {\n    return {\n      color: 'currentColor',\n      width: 1,\n      class: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      dropCursor(this.options),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;AA0BA;;;;;CAKG,GACU,MAAA,UAAU,wJAAG,YAAS,CAAC,MAAM,CAAoB;IAC5D,IAAI,EAAE,YAAY;IAElB,UAAU,GAAA;QACR,OAAO;YACL,KAAK,EAAE,cAAc;YACrB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,SAAS;SACjB;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;8KACL,aAAA,AAAU,EAAC,IAAI,CAAC,OAAO,CAAC;SACzB;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4257, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-gapcursor/src/gapcursor.ts"], "sourcesContent": ["import {\n  callOrReturn,\n  Extension,\n  getExtensionField,\n  ParentConfig,\n} from '@tiptap/core'\nimport { gapCursor } from '@tiptap/pm/gapcursor'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A function to determine whether the gap cursor is allowed at the current position. Must return `true` or `false`.\n     * @default null\n     */\n    allowGapCursor?:\n      | boolean\n      | null\n      | ((this: {\n        name: string,\n        options: Options,\n        storage: Storage,\n        parent: ParentConfig<NodeConfig<Options>>['allowGapCursor'],\n      }) => boolean | null),\n  }\n}\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nexport const Gapcursor = Extension.create({\n  name: 'gapCursor',\n\n  addProseMirrorPlugins() {\n    return [\n      gapCursor(),\n    ]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      allowGapCursor: callOrReturn(getExtensionField(extension, 'allowGapCursor', context)) ?? null,\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;AA0BA;;;;;CAKG,GACU,MAAA,SAAS,wJAAG,YAAS,CAAC,MAAM,CAAC;IACxC,IAAI,EAAE,WAAW;IAEjB,qBAAqB,GAAA;QACnB,OAAO;aACL,4KAAA,AAAS,EAAE;SACZ;KACF;IAED,gBAAgB,EAAC,SAAS,EAAA;;QACxB,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;SAC3B;QAED,OAAO;YACL,cAAc,EAAE,CAAA,EAAA,4JAAA,eAAA,AAAY,2JAAC,oBAAA,AAAiB,EAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI;SAC9F;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4298, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-hard-break/src/hard-break.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface HardBreakOptions {\n  /**\n   * Controls if marks should be kept after being split by a hard break.\n   * @default true\n   * @example false\n   */\n  keepMarks: boolean,\n\n  /**\n   * HTML attributes to add to the hard break element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    hardBreak: {\n      /**\n       * Add a hard break\n       * @example editor.commands.setHardBreak()\n       */\n      setHardBreak: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nexport const HardBreak = Node.create<HardBreakOptions>({\n  name: 'hardBreak',\n\n  addOptions() {\n    return {\n      keepMarks: true,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline: true,\n\n  group: 'inline',\n\n  selectable: false,\n\n  linebreakReplacement: true,\n\n  parseHTML() {\n    return [\n      { tag: 'br' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['br', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  renderText() {\n    return '\\n'\n  },\n\n  addCommands() {\n    return {\n      setHardBreak: () => ({\n        commands,\n        chain,\n        state,\n        editor,\n      }) => {\n        return commands.first([\n          () => commands.exitCode(),\n          () => commands.command(() => {\n            const { selection, storedMarks } = state\n\n            if (selection.$from.parent.type.spec.isolating) {\n              return false\n            }\n\n            const { keepMarks } = this.options\n            const { splittableMarks } = editor.extensionManager\n            const marks = storedMarks\n              || (selection.$to.parentOffset && selection.$from.marks())\n\n            return chain()\n              .insertContent({ type: this.name })\n              .command(({ tr, dispatch }) => {\n                if (dispatch && marks && keepMarks) {\n                  const filteredMarks = marks\n                    .filter(mark => splittableMarks.includes(mark.type.name))\n\n                  tr.ensureMarks(filteredMarks)\n                }\n\n                return true\n              })\n              .run()\n          }),\n        ])\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Enter': () => this.editor.commands.setHardBreak(),\n      'Shift-Enter': () => this.editor.commands.setHardBreak(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;AA8BA;;;CAGG,GACU,MAAA,SAAS,wJAAG,OAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;YACL,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,MAAM,EAAE,IAAI;IAEZ,KAAK,EAAE,QAAQ;IAEf,UAAU,EAAE,KAAK;IAEjB,oBAAoB,EAAE,IAAI;IAE1B,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,IAAI;YAAA,CAAE;SACd;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;SAAC;KAC5E;IAED,UAAU,GAAA;QACR,OAAO,IAAI;KACZ;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,EAAE,IAAM,CAAC,EACnB,QAAQ,EACR,KAAK,EACL,KAAK,EACL,MAAM,EACP,KAAI;oBACH,OAAO,QAAQ,CAAC,KAAK,CAAC;wBACpB,IAAM,QAAQ,CAAC,QAAQ,EAAE;wBACzB,IAAM,QAAQ,CAAC,OAAO,CAAC,MAAK;gCAC1B,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;gCAExC,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oCAC9C,OAAO,KAAK;;gCAGd,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO;gCAClC,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB;gCACnD,MAAM,KAAK,GAAG,eACR,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;gCAE5D,OAAO,KAAK,GACT,aAAa,CAAC;oCAAE,IAAI,EAAE,IAAI,CAAC,IAAI;gCAAA,CAAE,EACjC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;oCAC5B,IAAI,QAAQ,IAAI,KAAK,IAAI,SAAS,EAAE;wCAClC,MAAM,aAAa,GAAG,MACnB,MAAM,EAAC,IAAI,GAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wCAE3D,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;oCAG/B,OAAO,IAAI;gCACb,CAAC,EACA,GAAG,EAAE;4BACV,CAAC,CAAC;qBACH,CAAC;iBACH;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,WAAW,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;YACtD,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACzD;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4377, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-heading/src/heading.ts"], "sourcesContent": ["import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\n\n/**\n * The heading level options.\n */\nexport type Level = 1 | 2 | 3 | 4 | 5 | 6\n\nexport interface HeadingOptions {\n  /**\n   * The available heading levels.\n   * @default [1, 2, 3, 4, 5, 6]\n   * @example [1, 2, 3]\n   */\n  levels: Level[],\n\n  /**\n   * The HTML attributes for a heading node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    heading: {\n      /**\n       * Set a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.setHeading({ level: 1 })\n       */\n      setHeading: (attributes: { level: Level }) => ReturnType,\n      /**\n       * Toggle a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.toggleHeading({ level: 1 })\n       */\n      toggleHeading: (attributes: { level: Level }) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nexport const Heading = Node.create<HeadingOptions>({\n  name: 'heading',\n\n  addOptions() {\n    return {\n      levels: [1, 2, 3, 4, 5, 6],\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'inline*',\n\n  group: 'block',\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      level: {\n        default: 1,\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return this.options.levels\n      .map((level: Level) => ({\n        tag: `h${level}`,\n        attrs: { level },\n      }))\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const hasLevel = this.options.levels.includes(node.attrs.level)\n    const level = hasLevel\n      ? node.attrs.level\n      : this.options.levels[0]\n\n    return [`h${level}`, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.setNode(this.name, attributes)\n      },\n      toggleHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.toggleNode(this.name, 'paragraph', attributes)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return this.options.levels.reduce((items, level) => ({\n      ...items,\n      ...{\n        [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n      },\n    }), {})\n  },\n\n  addInputRules() {\n    return this.options.levels.map(level => {\n      return textblockTypeInputRule({\n        find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n        type: this.type,\n        getAttributes: {\n          level,\n        },\n      })\n    })\n  },\n})\n"], "names": [], "mappings": ";;;;;;AA0CA;;;CAGG,GACU,MAAA,OAAO,wJAAG,OAAI,CAAC,MAAM,CAAiB;IACjD,IAAI,EAAE,SAAS;IAEf,UAAU,GAAA;QACR,OAAO;YACL,MAAM,EAAE;gBAAC,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;aAAC;YAC1B,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,OAAO,EAAE,SAAS;IAElB,KAAK,EAAE,OAAO;IAEd,QAAQ,EAAE,IAAI;IAEd,aAAa,GAAA;QACX,OAAO;YACL,KAAK,EAAE;gBACL,OAAO,EAAE,CAAC;gBACV,QAAQ,EAAE,KAAK;YAChB,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,MAAA,CACjB,GAAG,CAAC,CAAC,KAAY,GAAA,CAAM;gBACtB,GAAG,EAAE,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA;gBAChB,KAAK,EAAE;oBAAE,KAAK;gBAAA,CAAE;YACjB,CAAA,CAAC,CAAC;KACN;IAED,UAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,EAAA;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC/D,MAAM,KAAK,GAAG,WACV,IAAI,CAAC,KAAK,CAAC,KAAA,GACX,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAE1B,OAAO;YAAC,CAAI,CAAA,EAAA,KAAK,EAAE;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACtF;IAED,WAAW,GAAA;QACT,OAAO;YACL,UAAU,GAAE,UAAU,GAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;wBACnD,OAAO,KAAK;;oBAGd,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/C;YACD,aAAa,GAAE,UAAU,GAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;wBACnD,OAAO,KAAK;;oBAGd,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC;iBAC/D;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,GAAA,CAAM;gBACnD,GAAG,KAAK;gBACR,GAAG;oBACD,CAAC,CAAA,QAAA,EAAW,KAAK,CAAA,CAAE,CAAA,EAAG,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC;4BAAE,KAAK;wBAAA,CAAE,CAAC;gBAC1E,CAAA;aACF,CAAC,EAAE,CAAA,CAAE,CAAC;KACR;IAED,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAC,KAAK,IAAG;YACrC,gKAAO,yBAAA,AAAsB,EAAC;gBAC5B,IAAI,EAAE,IAAI,MAAM,CAAC,CAAO,IAAA,EAAA,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAI,CAAA,EAAA,KAAK,CAAA,MAAA,CAAQ,CAAC;gBAC1E,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,EAAE;oBACb,KAAK;gBACN,CAAA;YACF,CAAA,CAAC;QACJ,CAAC,CAAC;KACH;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4475, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-history/src/history.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { history, redo, undo } from '@tiptap/pm/history'\n\nexport interface HistoryOptions {\n  /**\n   * The amount of history events that are collected before the oldest events are discarded.\n   * @default 100\n   * @example 50\n   */\n  depth: number,\n\n  /**\n   * The delay (in milliseconds) between changes after which a new group should be started.\n   * @default 500\n   * @example 1000\n   */\n  newGroupDelay: number,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    history: {\n      /**\n       * Undo recent changes\n       * @example editor.commands.undo()\n       */\n      undo: () => ReturnType,\n      /**\n       * Reapply reverted changes\n       * @example editor.commands.redo()\n       */\n      redo: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to undo and redo recent changes.\n * @see https://www.tiptap.dev/api/extensions/history\n *\n * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove\n * the `history` extension, as it is not compatible with the `collaboration` extension.\n *\n * `@tiptap/extension-collaboration` uses its own history implementation.\n */\nexport const History = Extension.create<HistoryOptions>({\n  name: 'history',\n\n  addOptions() {\n    return {\n      depth: 100,\n      newGroupDelay: 500,\n    }\n  },\n\n  addCommands() {\n    return {\n      undo: () => ({ state, dispatch }) => {\n        return undo(state, dispatch)\n      },\n      redo: () => ({ state, dispatch }) => {\n        return redo(state, dispatch)\n      },\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      history(this.options),\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-z': () => this.editor.commands.undo(),\n      'Shift-Mod-z': () => this.editor.commands.redo(),\n      'Mod-y': () => this.editor.commands.redo(),\n\n      // Russian keyboard layouts\n      'Mod-я': () => this.editor.commands.undo(),\n      'Shift-Mod-я': () => this.editor.commands.redo(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;AAoCA;;;;;;;;CAQG,GACU,MAAA,OAAO,wJAAG,YAAS,CAAC,MAAM,CAAiB;IACtD,IAAI,EAAE,SAAS;IAEf,UAAU,GAAA;QACR,OAAO;YACL,KAAK,EAAE,GAAG;YACV,aAAa,EAAE,GAAG;SACnB;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,IAAI,EAAE,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;oBAClC,sKAAO,OAAA,AAAI,EAAC,KAAK,EAAE,QAAQ,CAAC;iBAC7B;YACD,IAAI,EAAE,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;oBAClC,sKAAO,OAAA,AAAI,EAAC,KAAK,EAAE,QAAQ,CAAC;iBAC7B;SACF;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;2KACL,UAAA,AAAO,EAAC,IAAI,CAAC,OAAO,CAAC;SACtB;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1C,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAChD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;;YAG1C,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1C,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;SACjD;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4534, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-italic/src/italic.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface ItalicOptions {\n  /**\n   * HTML attributes to add to the italic element.\n   * @default {}\n   * @example { class: 'foo' }\n  */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    italic: {\n      /**\n       * Set an italic mark\n       * @example editor.commands.setItalic()\n       */\n      setItalic: () => ReturnType,\n      /**\n       * Toggle an italic mark\n       * @example editor.commands.toggleItalic()\n       */\n      toggleItalic: () => ReturnType,\n      /**\n       * Unset an italic mark\n       * @example editor.commands.unsetItalic()\n       */\n      unsetItalic: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an italic to a *italic* on input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))$/\n\n/**\n * Matches an italic to a *italic* on paste.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))/g\n\n/**\n * Matches an italic to a _italic_ on input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))$/\n\n/**\n * Matches an italic to a _italic_ on paste.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))/g\n\n/**\n * This extension allows you to create italic text.\n * @see https://www.tiptap.dev/api/marks/italic\n */\nexport const Italic = Mark.create<ItalicOptions>({\n  name: 'italic',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'em',\n      },\n      {\n        tag: 'i',\n        getAttrs: node => (node as HTMLElement).style.fontStyle !== 'normal' && null,\n      },\n      {\n        style: 'font-style=normal',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-style=italic',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['em', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setItalic: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleItalic: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetItalic: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-i': () => this.editor.commands.toggleItalic(),\n      'Mod-I': () => this.editor.commands.toggleItalic(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAsCA;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;;CAGG,GACU,MAAA,MAAM,uJAAG,QAAI,CAAC,MAAM,CAAgB;IAC/C,IAAI,EAAE,QAAQ;IAEd,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,IAAI;YACV,CAAA;YACD;gBACE,GAAG,EAAE,GAAG;gBACR,QAAQ,GAAE,IAAI,GAAK,IAAoB,CAAC,KAAK,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI;YAC7E,CAAA;YACD;gBACE,KAAK,EAAE,mBAAmB;gBAC1B,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAChD,CAAA;YACD;gBACE,KAAK,EAAE,mBAAmB;YAC3B,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC/E;IAED,WAAW,GAAA;QACT,OAAO;YACL,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,YAAY,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACnC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,WAAW,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;YAClD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACnD;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;aACF,wKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;qKACF,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4643, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-list-item/src/list-item.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ListItemOptions {\n  /**\n   * The HTML attributes for a list item node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * The node type for bulletList nodes\n   * @default 'bulletList'\n   * @example 'myCustomBulletList'\n   */\n  bulletListTypeName: string\n\n  /**\n   * The node type for orderedList nodes\n   * @default 'orderedList'\n   * @example 'myCustomOrderedList'\n   */\n  orderedListTypeName: string\n}\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const ListItem = Node.create<ListItemOptions>({\n  name: 'listItem',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      bulletListTypeName: 'bulletList',\n      orderedListTypeName: 'orderedList',\n    }\n  },\n\n  content: 'paragraph block*',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      {\n        tag: 'li',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;AAyBA;;;CAGG,GACU,MAAA,QAAQ,wJAAG,OAAI,CAAC,MAAM,CAAkB;IACnD,IAAI,EAAE,UAAU;IAEhB,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;YAClB,kBAAkB,EAAE,YAAY;YAChC,mBAAmB,EAAE,aAAa;SACnC;KACF;IAED,OAAO,EAAE,kBAAkB;IAE3B,QAAQ,EAAE,IAAI;IAEd,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,IAAI;YACV,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC/E;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,KAAK,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1D,GAAG,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;YACvD,WAAW,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;SAChE;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4693, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-ordered-list/src/ordered-list.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface OrderedListOptions {\n  /**\n   * The node type name for list items.\n   * @default 'listItem'\n   * @example 'myListItem'\n   */\n  itemTypeName: string,\n\n  /**\n   * The HTML attributes for an ordered list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * Keep the marks when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean,\n\n  /**\n   * Keep the attributes when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    orderedList: {\n      /**\n       * Toggle an ordered list\n       * @example editor.commands.toggleOrderedList()\n       */\n      toggleOrderedList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nexport const inputRegex = /^(\\d+)\\.\\s$/\n\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const OrderedList = Node.create<OrderedListOptions>({\n  name: 'orderedList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  addAttributes() {\n    return {\n      start: {\n        default: 1,\n        parseHTML: element => {\n          return element.hasAttribute('start')\n            ? parseInt(element.getAttribute('start') || '', 10)\n            : 1\n        },\n      },\n      type: {\n        default: null,\n        parseHTML: element => element.getAttribute('type'),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'ol',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const { start, ...attributesWithoutStart } = HTMLAttributes\n\n    return start === 1\n      ? ['ol', mergeAttributes(this.options.HTMLAttributes, attributesWithoutStart), 0]\n      : ['ol', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleOrderedList: () => ({ commands, chain }) => {\n        if (this.options.keepAttributes) {\n          return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run()\n        }\n        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: inputRegex,\n      type: this.type,\n      getAttributes: match => ({ start: +match[1] }),\n      joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n        joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        editor: this.editor,\n      })\n    }\n    return [\n      inputRule,\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,YAAY,GAAG,UAAU;AAC/B,MAAM,aAAa,GAAG,WAAW;AA4CjC;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;;;CAKG,GACU,MAAA,WAAW,wJAAG,OAAI,CAAC,MAAM,CAAqB;IACzD,IAAI,EAAE,aAAa;IAEnB,UAAU,GAAA;QACR,OAAO;YACL,YAAY,EAAE,UAAU;YACxB,cAAc,EAAE,CAAA,CAAE;YAClB,SAAS,EAAE,KAAK;YAChB,cAAc,EAAE,KAAK;SACtB;KACF;IAED,KAAK,EAAE,YAAY;IAEnB,OAAO,GAAA;QACL,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAA,CAAA,CAAG;KACvC;IAED,aAAa,GAAA;QACX,OAAO;YACL,KAAK,EAAE;gBACL,OAAO,EAAE,CAAC;gBACV,SAAS,GAAE,OAAO,IAAG;oBACnB,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO,IAC/B,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,IAChD,CAAC;iBACN;YACF,CAAA;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI;gBACb,SAAS,GAAE,OAAO,GAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;YACnD,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,IAAI;YACV,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,sBAAsB,EAAE,GAAG,cAAc;QAE3D,OAAO,KAAK,KAAK,IACb;YAAC,IAAI;YAAE,2KAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,sBAAsB,CAAC;YAAE,CAAC;SAAA,GAC9E;YAAC,IAAI;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC5E;IAED,WAAW,GAAA;QACT,OAAO;YACL,iBAAiB,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAI;oBAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;wBAC/B,OAAO,KAAK,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE;;oBAExK,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;iBACzF;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE;SAC9D;KACF;IAED,aAAa,GAAA;QACX,IAAI,SAAS,GAAG,6KAAA,AAAiB,EAAC;YAChC,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,GAAE,KAAK,GAAA,CAAK;oBAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;gBAAA,CAAE,CAAC;YAC9C,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,GAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACjF,CAAA,CAAC;QAEF,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YACzD,SAAS,IAAG,4KAAA,AAAiB,EAAC;gBAC5B,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC3C,aAAa,GAAE,KAAK,GAAA,CAAK;wBAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;wBAAE,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC;oBAAA,CAAE,CAAC;gBAC3F,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,GAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBAChF,MAAM,EAAE,IAAI,CAAC,MAAM;YACpB,CAAA,CAAC;;QAEJ,OAAO;YACL,SAAS;SACV;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4808, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-paragraph/src/paragraph.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ParagraphOptions {\n  /**\n   * The HTML attributes for a paragraph node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    paragraph: {\n      /**\n       * Toggle a paragraph\n       * @example editor.commands.toggleParagraph()\n       */\n      setParagraph: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create paragraphs.\n * @see https://www.tiptap.dev/api/nodes/paragraph\n */\nexport const Paragraph = Node.create<ParagraphOptions>({\n  name: 'paragraph',\n\n  priority: 1000,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  content: 'inline*',\n\n  parseHTML() {\n    return [\n      { tag: 'p' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['p', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setParagraph: () => ({ commands }) => {\n        return commands.setNode(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-0': () => this.editor.commands.setParagraph(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;AAuBA;;;CAGG,GACU,MAAA,SAAS,wJAAG,OAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,QAAQ,EAAE,IAAI;IAEd,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,KAAK,EAAE,OAAO;IAEd,OAAO,EAAE,SAAS;IAElB,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,GAAG;YAAA,CAAE;SACb;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,GAAG;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACnC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,WAAW,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACvD;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4862, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-strike/src/strike.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface StrikeOptions {\n  /**\n   * HTML attributes to add to the strike element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    strike: {\n      /**\n       * Set a strike mark\n       * @example editor.commands.setStrike()\n       */\n      setStrike: () => ReturnType,\n      /**\n       * Toggle a strike mark\n       * @example editor.commands.toggleStrike()\n       */\n      toggleStrike: () => ReturnType,\n      /**\n       * Unset a strike mark\n       * @example editor.commands.unsetStrike()\n       */\n      unsetStrike: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a strike to a ~~strike~~ on input.\n */\nexport const inputRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))$/\n\n/**\n * Matches a strike to a ~~strike~~ on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))/g\n\n/**\n * This extension allows you to create strike text.\n * @see https://www.tiptap.dev/api/marks/strike\n */\nexport const Strike = Mark.create<StrikeOptions>({\n  name: 'strike',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 's',\n      },\n      {\n        tag: 'del',\n      },\n      {\n        tag: 'strike',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('line-through') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['s', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setStrike: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleStrike: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetStrike: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-s': () => this.editor.commands.toggleStrike(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;AAsCA;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,MAAM,wJAAG,OAAI,CAAC,MAAM,CAAgB;IAC/C,IAAI,EAAE,QAAQ;IAEd,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,GAAG;YACT,CAAA;YACD;gBACE,GAAG,EAAE,KAAK;YACX,CAAA;YACD;gBACE,GAAG,EAAE,QAAQ;YACd,CAAA;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,KAAK;gBAChB,QAAQ,GAAE,KAAK,GAAM,KAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAA,CAAE,GAAG,KAAK,CAAC;YAC7E,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,GAAG;qKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,YAAY,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACnC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,WAAW,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACzD;KACF;IAED,aAAa,GAAA;QACX,OAAO;aACL,wKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4954, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-text/src/text.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n"], "names": [], "mappings": ";;;;;;AAEA;;;CAGG,GACU,MAAA,IAAI,wJAAG,OAAI,CAAC,MAAM,CAAC;IAC9B,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,QAAQ;AAChB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4975, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/starter-kit/src/starter-kit.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { Blockquote, BlockquoteOptions } from '@tiptap/extension-blockquote'\nimport { Bold, BoldOptions } from '@tiptap/extension-bold'\nimport { BulletList, BulletListOptions } from '@tiptap/extension-bullet-list'\nimport { Code, CodeOptions } from '@tiptap/extension-code'\nimport { CodeBlock, CodeBlockOptions } from '@tiptap/extension-code-block'\nimport { Document } from '@tiptap/extension-document'\nimport { Dropcursor, DropcursorOptions } from '@tiptap/extension-dropcursor'\nimport { Gapcursor } from '@tiptap/extension-gapcursor'\nimport { HardBreak, HardBreakOptions } from '@tiptap/extension-hard-break'\nimport { Heading, HeadingOptions } from '@tiptap/extension-heading'\nimport { History, HistoryOptions } from '@tiptap/extension-history'\nimport { HorizontalRule, HorizontalRuleOptions } from '@tiptap/extension-horizontal-rule'\nimport { Italic, ItalicOptions } from '@tiptap/extension-italic'\nimport { ListItem, ListItemOptions } from '@tiptap/extension-list-item'\nimport { OrderedList, OrderedListOptions } from '@tiptap/extension-ordered-list'\nimport { Paragraph, ParagraphOptions } from '@tiptap/extension-paragraph'\nimport { Strike, StrikeOptions } from '@tiptap/extension-strike'\nimport { Text } from '@tiptap/extension-text'\n\nexport interface StarterKitOptions {\n  /**\n   * If set to false, the blockquote extension will not be registered\n   * @example blockquote: false\n   */\n  blockquote: Partial<BlockquoteOptions> | false,\n\n  /**\n   * If set to false, the bold extension will not be registered\n   * @example bold: false\n   */\n  bold: Partial<BoldOptions> | false,\n\n  /**\n   * If set to false, the bulletList extension will not be registered\n   * @example bulletList: false\n   */\n  bulletList: Partial<BulletListOptions> | false,\n\n  /**\n   * If set to false, the code extension will not be registered\n   * @example code: false\n   */\n  code: Partial<CodeOptions> | false,\n\n  /**\n   * If set to false, the codeBlock extension will not be registered\n   * @example codeBlock: false\n   */\n  codeBlock: Partial<CodeBlockOptions> | false,\n\n  /**\n   * If set to false, the document extension will not be registered\n   * @example document: false\n   */\n  document: false,\n\n  /**\n   * If set to false, the dropcursor extension will not be registered\n   * @example dropcursor: false\n   */\n  dropcursor: Partial<DropcursorOptions> | false,\n\n  /**\n   * If set to false, the gapcursor extension will not be registered\n   * @example gapcursor: false\n   */\n  gapcursor: false,\n\n  /**\n   * If set to false, the hardBreak extension will not be registered\n   * @example hardBreak: false\n   */\n  hardBreak: Partial<HardBreakOptions> | false,\n\n  /**\n   * If set to false, the heading extension will not be registered\n   * @example heading: false\n   */\n  heading: Partial<HeadingOptions> | false,\n\n  /**\n   * If set to false, the history extension will not be registered\n   * @example history: false\n   */\n  history: Partial<HistoryOptions> | false,\n\n  /**\n   * If set to false, the horizontalRule extension will not be registered\n   * @example horizontalRule: false\n   */\n  horizontalRule: Partial<HorizontalRuleOptions> | false,\n\n  /**\n   * If set to false, the italic extension will not be registered\n   * @example italic: false\n   */\n  italic: Partial<ItalicOptions> | false,\n\n  /**\n   * If set to false, the listItem extension will not be registered\n   * @example listItem: false\n   */\n  listItem: Partial<ListItemOptions> | false,\n\n  /**\n   * If set to false, the orderedList extension will not be registered\n   * @example orderedList: false\n   */\n  orderedList: Partial<OrderedListOptions> | false,\n\n  /**\n   * If set to false, the paragraph extension will not be registered\n   * @example paragraph: false\n   */\n  paragraph: Partial<ParagraphOptions> | false,\n\n  /**\n   * If set to false, the strike extension will not be registered\n   * @example strike: false\n   */\n  strike: Partial<StrikeOptions> | false,\n\n  /**\n   * If set to false, the text extension will not be registered\n   * @example text: false\n   */\n  text: false,\n}\n\n/**\n * The starter kit is a collection of essential editor extensions.\n *\n * It’s a good starting point for building your own editor.\n */\nexport const StarterKit = Extension.create<StarterKitOptions>({\n  name: 'starterKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.bold !== false) {\n      extensions.push(Bold.configure(this.options.bold))\n    }\n\n    if (this.options.blockquote !== false) {\n      extensions.push(Blockquote.configure(this.options.blockquote))\n    }\n\n    if (this.options.bulletList !== false) {\n      extensions.push(BulletList.configure(this.options.bulletList))\n    }\n\n    if (this.options.code !== false) {\n      extensions.push(Code.configure(this.options.code))\n    }\n\n    if (this.options.codeBlock !== false) {\n      extensions.push(CodeBlock.configure(this.options.codeBlock))\n    }\n\n    if (this.options.document !== false) {\n      extensions.push(Document.configure(this.options.document))\n    }\n\n    if (this.options.dropcursor !== false) {\n      extensions.push(Dropcursor.configure(this.options.dropcursor))\n    }\n\n    if (this.options.gapcursor !== false) {\n      extensions.push(Gapcursor.configure(this.options.gapcursor))\n    }\n\n    if (this.options.hardBreak !== false) {\n      extensions.push(HardBreak.configure(this.options.hardBreak))\n    }\n\n    if (this.options.heading !== false) {\n      extensions.push(Heading.configure(this.options.heading))\n    }\n\n    if (this.options.history !== false) {\n      extensions.push(History.configure(this.options.history))\n    }\n\n    if (this.options.horizontalRule !== false) {\n      extensions.push(HorizontalRule.configure(this.options.horizontalRule))\n    }\n\n    if (this.options.italic !== false) {\n      extensions.push(Italic.configure(this.options.italic))\n    }\n\n    if (this.options.listItem !== false) {\n      extensions.push(ListItem.configure(this.options.listItem))\n    }\n\n    if (this.options.orderedList !== false) {\n      extensions.push(OrderedList.configure(this.options.orderedList))\n    }\n\n    if (this.options.paragraph !== false) {\n      extensions.push(Paragraph.configure(this.options.paragraph))\n    }\n\n    if (this.options.strike !== false) {\n      extensions.push(Strike.configure(this.options.strike))\n    }\n\n    if (this.options.text !== false) {\n      extensions.push(Text.configure(this.options.text))\n    }\n\n    return extensions\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkIA;;;;CAIG,GACU,MAAA,UAAU,wJAAG,YAAS,CAAC,MAAM,CAAoB;IAC5D,IAAI,EAAE,YAAY;IAElB,aAAa,GAAA;QACX,MAAM,UAAU,GAAG,EAAE;QAErB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE;YAC/B,UAAU,CAAC,IAAI,mKAAC,OAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;QAGpD,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,EAAE;YACrC,UAAU,CAAC,IAAI,yKAAC,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;QAGhE,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,EAAE;YACrC,UAAU,CAAC,IAAI,6KAAC,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;QAGhE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE;YAC/B,UAAU,CAAC,IAAI,mKAAC,OAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;QAGpD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;YACpC,UAAU,CAAC,IAAI,4KAAC,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;QAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;YACnC,UAAU,CAAC,IAAI,uKAAC,WAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;;QAG5D,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,EAAE;YACrC,UAAU,CAAC,IAAI,yKAAC,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;QAGhE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;YACpC,UAAU,CAAC,IAAI,wKAAC,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;QAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;YACpC,UAAU,CAAC,IAAI,4KAAC,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;QAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;YAClC,UAAU,CAAC,IAAI,sKAAC,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;QAG1D,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;YAClC,UAAU,CAAC,IAAI,sKAAC,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;QAG1D,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,KAAK,EAAE;YACzC,UAAU,CAAC,IAAI,iLAAC,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;;QAGxE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;YACjC,UAAU,CAAC,IAAI,qKAAC,SAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;QAGxD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;YACnC,UAAU,CAAC,IAAI,2KAAC,WAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;;QAG5D,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE;YACtC,UAAU,CAAC,IAAI,8KAAC,cAAW,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;;QAGlE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;YACpC,UAAU,CAAC,IAAI,wKAAC,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;QAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;YACjC,UAAU,CAAC,IAAI,qKAAC,SAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;QAGxD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE;YAC/B,UAAU,CAAC,IAAI,mKAAC,OAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;QAGpD,OAAO,UAAU;KAClB;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5100, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-character-count/src/character-count.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { <PERSON>lug<PERSON>, PluginKey } from '@tiptap/pm/state'\n\nexport interface CharacterCountOptions {\n  /**\n   * The maximum number of characters that should be allowed. Defaults to `0`.\n   * @default null\n   * @example 180\n   */\n  limit: number | null | undefined\n  /**\n   * The mode by which the size is calculated. If set to `textSize`, the textContent of the document is used.\n   * If set to `nodeSize`, the nodeSize of the document is used.\n   * @default 'textSize'\n   * @example 'textSize'\n   */\n  mode: 'textSize' | 'nodeSize'\n  /**\n * The text counter function to use. Defaults to a simple character count.\n * @default (text) => text.length\n * @example (text) => [...new Intl.Segmenter().segment(text)].length\n */\n  textCounter: (text: string) => number\n  /**\n   * The word counter function to use. Defaults to a simple word count.\n   * @default (text) => text.split(' ').filter(word => word !== '').length\n   * @example (text) => text.split(/\\s+/).filter(word => word !== '').length\n   */\n  wordCounter: (text: string) => number\n}\n\nexport interface CharacterCountStorage {\n  /**\n   * Get the number of characters for the current document.\n   * @param options The options for the character count. (optional)\n   * @param options.node The node to get the characters from. Defaults to the current document.\n   * @param options.mode The mode by which the size is calculated. If set to `textSize`, the textContent of the document is used.\n   */\n  characters: (options?: { node?: ProseMirrorNode; mode?: 'textSize' | 'nodeSize' }) => number\n\n  /**\n   * Get the number of words for the current document.\n   * @param options The options for the character count. (optional)\n   * @param options.node The node to get the words from. Defaults to the current document.\n   */\n  words: (options?: { node?: ProseMirrorNode }) => number\n}\n\n/**\n * This extension allows you to count the characters and words of your document.\n * @see https://tiptap.dev/api/extensions/character-count\n */\nexport const CharacterCount = Extension.create<CharacterCountOptions, CharacterCountStorage>({\n  name: 'characterCount',\n\n  addOptions() {\n    return {\n      limit: null,\n      mode: 'textSize',\n      textCounter: text => text.length,\n      wordCounter: text => text.split(' ').filter(word => word !== '').length,\n    }\n  },\n\n  addStorage() {\n    return {\n      characters: () => 0,\n      words: () => 0,\n    }\n  },\n\n  onBeforeCreate() {\n    this.storage.characters = options => {\n      const node = options?.node || this.editor.state.doc\n      const mode = options?.mode || this.options.mode\n\n      if (mode === 'textSize') {\n        const text = node.textBetween(0, node.content.size, undefined, ' ')\n\n        return this.options.textCounter(text)\n      }\n\n      return node.nodeSize\n    }\n\n    this.storage.words = options => {\n      const node = options?.node || this.editor.state.doc\n      const text = node.textBetween(0, node.content.size, ' ', ' ')\n\n      return this.options.wordCounter(text)\n    }\n  },\n\n  addProseMirrorPlugins() {\n    let initialEvaluationDone = false\n\n    return [\n      new Plugin({\n        key: new PluginKey('characterCount'),\n        appendTransaction: (transactions, oldState, newState) => {\n          if (initialEvaluationDone) {\n            return\n          }\n\n          const limit = this.options.limit\n\n          if (limit === null || limit === undefined || limit === 0) {\n            initialEvaluationDone = true\n            return\n          }\n\n          const initialContentSize = this.storage.characters({ node: newState.doc })\n\n          if (initialContentSize > limit) {\n            const over = initialContentSize - limit\n            const from = 0\n            const to = over\n\n            console.warn(`[CharacterCount] Initial content exceeded limit of ${limit} characters. Content was automatically trimmed.`)\n            const tr = newState.tr.deleteRange(from, to)\n\n            initialEvaluationDone = true\n            return tr\n          }\n\n          initialEvaluationDone = true\n        },\n        filterTransaction: (transaction, state) => {\n          const limit = this.options.limit\n\n          // Nothing has changed or no limit is defined. Ignore it.\n          if (!transaction.docChanged || limit === 0 || limit === null || limit === undefined) {\n            return true\n          }\n\n          const oldSize = this.storage.characters({ node: state.doc })\n          const newSize = this.storage.characters({ node: transaction.doc })\n\n          // Everything is in the limit. Good.\n          if (newSize <= limit) {\n            return true\n          }\n\n          // The limit has already been exceeded but will be reduced.\n          if (oldSize > limit && newSize > limit && newSize <= oldSize) {\n            return true\n          }\n\n          // The limit has already been exceeded and will be increased further.\n          if (oldSize > limit && newSize > limit && newSize > oldSize) {\n            return false\n          }\n\n          const isPaste = transaction.getMeta('paste')\n\n          // Block all exceeding transactions that were not pasted.\n          if (!isPaste) {\n            return false\n          }\n\n          // For pasted content, we try to remove the exceeding content.\n          const pos = transaction.selection.$head.pos\n          const over = newSize - limit\n          const from = pos - over\n          const to = pos\n\n          // It’s probably a bad idea to mutate transactions within `filterTransaction`\n          // but for now this is working fine.\n          transaction.deleteRange(from, to)\n\n          // In some situations, the limit will continue to be exceeded after trimming.\n          // This happens e.g. when truncating within a complex node (e.g. table)\n          // and ProseMirror has to close this node again.\n          // If this is the case, we prevent the transaction completely.\n          const updatedSize = this.storage.characters({ node: transaction.doc })\n\n          if (updatedSize > limit) {\n            return false\n          }\n\n          return true\n        },\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;AAiDA;;;CAGG,GACU,MAAA,cAAc,wJAAG,YAAS,CAAC,MAAM,CAA+C;IAC3F,IAAI,EAAE,gBAAgB;IAEtB,UAAU,GAAA;QACR,OAAO;YACL,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,UAAU;YAChB,WAAW,GAAE,IAAI,GAAI,IAAI,CAAC,MAAM;YAChC,WAAW,GAAE,IAAI,GAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAC,IAAI,GAAI,IAAI,KAAK,EAAE,CAAC,CAAC,MAAM;SACxE;KACF;IAED,UAAU,GAAA;QACR,OAAO;YACL,UAAU,EAAE,IAAM,CAAC;YACnB,KAAK,EAAE,IAAM,CAAC;SACf;KACF;IAED,cAAc,GAAA;QACZ,IAAI,CAAC,OAAO,CAAC,UAAU,IAAG,OAAO,IAAG;YAClC,MAAM,IAAI,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,IAAI,KAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;YACnD,MAAM,IAAI,GAAG,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,IAAI,KAAI,IAAI,CAAC,OAAO,CAAC,IAAI;YAE/C,IAAI,IAAI,KAAK,UAAU,EAAE;gBACvB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC;gBAEnE,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;;YAGvC,OAAO,IAAI,CAAC,QAAQ;QACtB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,IAAG,OAAO,IAAG;YAC7B,MAAM,IAAI,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,IAAI,KAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;YAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;QACvC,CAAC;KACF;IAED,qBAAqB,GAAA;QACnB,IAAI,qBAAqB,GAAG,KAAK;QAEjC,OAAO;YACL,6JAAI,SAAM,CAAC;gBACT,GAAG,EAAE,6JAAI,YAAS,CAAC,gBAAgB,CAAC;gBACpC,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;oBACtD,IAAI,qBAAqB,EAAE;wBACzB;;oBAGF,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;oBAEhC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,CAAC,EAAE;wBACxD,qBAAqB,GAAG,IAAI;wBAC5B;;oBAGF,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;wBAAE,IAAI,EAAE,QAAQ,CAAC,GAAG;oBAAA,CAAE,CAAC;oBAE1E,IAAI,kBAAkB,GAAG,KAAK,EAAE;wBAC9B,MAAM,IAAI,GAAG,kBAAkB,GAAG,KAAK;wBACvC,MAAM,IAAI,GAAG,CAAC;wBACd,MAAM,EAAE,GAAG,IAAI;wBAEf,OAAO,CAAC,IAAI,CAAC,CAAA,mDAAA,EAAsD,KAAK,CAAA,+CAAA,CAAiD,CAAC;wBAC1H,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;wBAE5C,qBAAqB,GAAG,IAAI;wBAC5B,OAAO,EAAE;;oBAGX,qBAAqB,GAAG,IAAI;iBAC7B;gBACD,iBAAiB,EAAE,CAAC,WAAW,EAAE,KAAK,KAAI;oBACxC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;;oBAGhC,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;wBACnF,OAAO,IAAI;;oBAGb,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;wBAAE,IAAI,EAAE,KAAK,CAAC,GAAG;oBAAA,CAAE,CAAC;oBAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;wBAAE,IAAI,EAAE,WAAW,CAAC,GAAG;oBAAA,CAAE,CAAC;;oBAGlE,IAAI,OAAO,IAAI,KAAK,EAAE;wBACpB,OAAO,IAAI;;;oBAIb,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,IAAI,OAAO,EAAE;wBAC5D,OAAO,IAAI;;;oBAIb,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,OAAO,EAAE;wBAC3D,OAAO,KAAK;;oBAGd,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;;oBAG5C,IAAI,CAAC,OAAO,EAAE;wBACZ,OAAO,KAAK;;;oBAId,MAAM,GAAG,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;oBAC3C,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK;oBAC5B,MAAM,IAAI,GAAG,GAAG,GAAG,IAAI;oBACvB,MAAM,EAAE,GAAG,GAAG;;;oBAId,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;;;;;oBAMjC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;wBAAE,IAAI,EAAE,WAAW,CAAC,GAAG;oBAAA,CAAE,CAAC;oBAEtE,IAAI,WAAW,GAAG,KAAK,EAAE;wBACvB,OAAO,KAAK;;oBAGd,OAAO,IAAI;iBACZ;aACF,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5243, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/node_modules/highlight.js/lib/core.js", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-code-block-lowlight/src/lowlight-plugin.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-code-block-lowlight/src/code-block-lowlight.ts"], "sourcesContent": ["/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear =\n      obj.delete =\n      obj.set =\n        function () {\n          throw new Error('map is read-only');\n        };\n  } else if (obj instanceof Set) {\n    obj.add =\n      obj.clear =\n      obj.delete =\n        function () {\n          throw new Error('set is read-only');\n        };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n\n  Object.getOwnPropertyNames(obj).forEach((name) => {\n    const prop = obj[name];\n    const type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    const className = scopeToCSSClass(node.scope,\n      { prefix: this.classPrefix });\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /** @param {string} scope */\n  startScope(scope) {\n    this.openNode(scope);\n  }\n\n  endScope() {\n    this.closeNode();\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  __addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    if (name) node.scope = `language:${name}`;\n\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    this.closeAllNodes();\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [\n    BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }\n  ]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.10.0\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n\n          const match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n\n          const beforeMatch = codeToHighlight.substring(index, match.index);\n          const processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language });\n\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    const index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// Other names for the variable may break build script\nconst highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = () => HLJS({});\n\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n", "import { findChildren } from '@tiptap/core'\nimport { Node as ProsemirrorNode } from '@tiptap/pm/model'\nimport { <PERSON>lug<PERSON>, PluginKey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n// @ts-ignore\nimport highlight from 'highlight.js/lib/core'\n\nfunction parseNodes(nodes: any[], className: string[] = []): { text: string; classes: string[] }[] {\n  return nodes\n    .map(node => {\n      const classes = [...className, ...(node.properties ? node.properties.className : [])]\n\n      if (node.children) {\n        return parseNodes(node.children, classes)\n      }\n\n      return {\n        text: node.value,\n        classes,\n      }\n    })\n    .flat()\n}\n\nfunction getHighlightNodes(result: any) {\n  // `.value` for lowlight v1, `.children` for lowlight v2\n  return result.value || result.children || []\n}\n\nfunction registered(aliasOrLanguage: string) {\n  return Boolean(highlight.getLanguage(aliasOrLanguage))\n}\n\nfunction getDecorations({\n  doc,\n  name,\n  lowlight,\n  defaultLanguage,\n}: {\n  doc: ProsemirrorNode\n  name: string\n  lowlight: any\n  defaultLanguage: string | null | undefined\n}) {\n  const decorations: Decoration[] = []\n\n  findChildren(doc, node => node.type.name === name).forEach(block => {\n    let from = block.pos + 1\n    const language = block.node.attrs.language || defaultLanguage\n    const languages = lowlight.listLanguages()\n\n    const nodes = language && (languages.includes(language) || registered(language) || lowlight.registered?.(language))\n      ? getHighlightNodes(lowlight.highlight(language, block.node.textContent))\n      : getHighlightNodes(lowlight.highlightAuto(block.node.textContent))\n\n    parseNodes(nodes).forEach(node => {\n      const to = from + node.text.length\n\n      if (node.classes.length) {\n        const decoration = Decoration.inline(from, to, {\n          class: node.classes.join(' '),\n        })\n\n        decorations.push(decoration)\n      }\n\n      from = to\n    })\n  })\n\n  return DecorationSet.create(doc, decorations)\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction isFunction(param: any): param is Function {\n  return typeof param === 'function'\n}\n\nexport function LowlightPlugin({\n  name,\n  lowlight,\n  defaultLanguage,\n}: {\n  name: string\n  lowlight: any\n  defaultLanguage: string | null | undefined\n}) {\n  if (!['highlight', 'highlightAuto', 'listLanguages'].every(api => isFunction(lowlight[api]))) {\n    throw Error(\n      'You should provide an instance of lowlight to use the code-block-lowlight extension',\n    )\n  }\n\n  const lowlightPlugin: Plugin<any> = new Plugin({\n    key: new PluginKey('lowlight'),\n\n    state: {\n      init: (_, { doc }) => getDecorations({\n        doc,\n        name,\n        lowlight,\n        defaultLanguage,\n      }),\n      apply: (transaction, decorationSet, oldState, newState) => {\n        const oldNodeName = oldState.selection.$head.parent.type.name\n        const newNodeName = newState.selection.$head.parent.type.name\n        const oldNodes = findChildren(oldState.doc, node => node.type.name === name)\n        const newNodes = findChildren(newState.doc, node => node.type.name === name)\n\n        if (\n          transaction.docChanged\n          // Apply decorations if:\n          // selection includes named node,\n          && ([oldNodeName, newNodeName].includes(name)\n            // OR transaction adds/removes named node,\n            || newNodes.length !== oldNodes.length\n            // OR transaction has changes that completely encapsulte a node\n            // (for example, a transaction that affects the entire document).\n            // Such transactions can happen during collab syncing via y-prosemirror, for example.\n            || transaction.steps.some(step => {\n              // @ts-ignore\n              return (\n                // @ts-ignore\n                step.from !== undefined\n                // @ts-ignore\n                && step.to !== undefined\n                && oldNodes.some(node => {\n                  // @ts-ignore\n                  return (\n                    // @ts-ignore\n                    node.pos >= step.from\n                    // @ts-ignore\n                    && node.pos + node.node.nodeSize <= step.to\n                  )\n                })\n              )\n            }))\n        ) {\n          return getDecorations({\n            doc: transaction.doc,\n            name,\n            lowlight,\n            defaultLanguage,\n          })\n        }\n\n        return decorationSet.map(transaction.mapping, transaction.doc)\n      },\n    },\n\n    props: {\n      decorations(state) {\n        return lowlightPlugin.getState(state)\n      },\n    },\n  })\n\n  return lowlightPlugin\n}\n", "import CodeBlock, { CodeBlockOptions } from '@tiptap/extension-code-block'\n\nimport { LowlightPlugin } from './lowlight-plugin.js'\n\nexport interface CodeBlockLowlightOptions extends CodeBlockOptions {\n  /**\n   * The lowlight instance.\n   */\n  lowlight: any,\n}\n\n/**\n * This extension allows you to highlight code blocks with lowlight.\n * @see https://tiptap.dev/api/nodes/code-block-lowlight\n */\nexport const CodeBlockLowlight = CodeBlock.extend<CodeBlockLowlightOptions>({\n  addOptions() {\n    return {\n      ...this.parent?.(),\n      lowlight: {},\n      languageClassPrefix: 'language-',\n      exitOnTripleEnter: true,\n      exitOnArrowDown: true,\n      defaultLanguage: null,\n      HTMLAttributes: {},\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      ...this.parent?.() || [],\n      LowlightPlugin({\n        name: this.name,\n        lowlight: this.options.lowlight,\n        defaultLanguage: this.options.defaultLanguage,\n      }),\n    ]\n  },\n})\n"], "names": ["highlight"], "mappings": ";;;;;;;;;;;;;;;;;qCAEA,SAAS,UAAU,CAAC,GAAG,EAAE;IACvB,IAAI,GAAG,YAAY,GAAG,EAAE;QACtB,GAAG,CAAC,KAAK,GACP,GAAG,CAAC,MAAM,GACV,GAAG,CAAC,GAAG,GACL,YAAY;YACV,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;QAC7C,CAAS;IACT,CAAG,MAAM,IAAI,GAAG,YAAY,GAAG,EAAE;QAC7B,GAAG,CAAC,GAAG,GACL,GAAG,CAAC,KAAK,GACT,GAAG,CAAC,MAAM,GACR,YAAY;YACV,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;QAC7C,CAAS;IACT;IAEA,cAAA;IACE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;IAElB,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;QAChD,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,MAAM,IAAI,GAAG,OAAO,IAAI;QAE5B,yEAAA;QACI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACxE,UAAU,CAAC,IAAI,CAAC;QACtB;IACA,CAAG,CAAC;IAEF,OAAO,GAAG;AACZ;AAEA,wEAAA,GACA,gEAAA,GACA,iCAAA,GAEA,MAAM,QAAQ,CAAC;IACf;;GAEA,GACE,WAAW,CAAC,IAAI,CAAE;QACpB,wCAAA;QACI,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,IAAI,CAAC,IAAI,GAAG,CAAA,CAAE;QAE3C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QACrB,IAAI,CAAC,cAAc,GAAG,KAAK;IAC/B;IAEE,WAAW,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG,IAAI;IAC9B;AACA;AAEA;;;CAGA,GACA,SAAS,UAAU,CAAC,KAAK,EAAE;IACzB,OAAO,MACJ,OAAO,CAAC,IAAI,EAAE,OAAO,EACrB,OAAO,CAAC,IAAI,EAAE,MAAM,EACpB,OAAO,CAAC,IAAI,EAAE,MAAM,EACpB,OAAO,CAAC,IAAI,EAAE,QAAQ,EACtB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC5B;AAEA;;;;;;;CAOA,GACA,SAAS,SAAS,CAAC,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzC,6BAAA,GACE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAElC,IAAK,MAAM,GAAG,IAAI,QAAQ,CAAE;QAC1B,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;IAC/B;IACE,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE;QAC5B,IAAK,MAAM,GAAG,IAAI,GAAG,CAAE;YACrB,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC;QAC5B;IACA,CAAG,CAAC;IACF,OAAyB,MAAM;AACjC;AAEA;;;;;;CAMA,GAEA,+EAAA,GACA,kDAAA,GACA,IAAA,GAEA,MAAM,UAAU,GAAG,SAAS;AAE5B;;;sBAGA,GACA,MAAM,iBAAiB,GAAG,CAAC,IAAI,KAAK;IACpC,+DAAA;IACA,uBAAA;IACE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK;AACrB,CAAC;AAED;;;;CAIA,GACA,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK;IAC9C,eAAA;IACE,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC;IACjD;IACA,6BAAA;IACE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9B,OAAO;YACL,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;eACxB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAK,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACrD,CAAC,IAAI,CAAC,GAAG,CAAC;IACf;IACA,eAAA;IACE,OAAO,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;AAC3B,CAAC;AAED,qBAAA,GACA,MAAM,YAAY,CAAC;IACnB;;;;;GAKA,GACE,WAAW,CAAC,SAAS,EAAE,OAAO,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW;QACtC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;IACxB;IAEA;;;0BAGA,GACE,OAAO,CAAC,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC;IACnC;IAEA;;;wBAGA,GACE,QAAQ,CAAC,IAAI,EAAE;QACb,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAE9B,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,EAC1C;YAAE,MAAM,EAAE,IAAI,CAAC,WAAW;QAAA,CAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA;;;wBAGA,GACE,SAAS,CAAC,IAAI,EAAE;QACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAE9B,IAAI,CAAC,MAAM,IAAI,UAAU;IAC7B;IAEA;;EAEA,GACE,KAAK,GAAG;QACN,OAAO,IAAI,CAAC,MAAM;IACtB;IAEA,UAAA;IAEA;;;+BAGA,GACE,IAAI,CAAC,SAAS,EAAE;QACd,IAAI,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,EAAE,CAAC;IAChD;AACA;AAEA,mFAAA,GACA,+EAAA,GACA,sDAAA,GACA,KAAA,GAEA,wBAAA,GACA,MAAM,OAAO,GAAG,CAAC,IAAI,GAAG,CAAA,CAAE,KAAK;IAC/B,mBAAA,GACE,MAAM,MAAM,GAAG;QAAE,QAAQ,EAAE,EAAE;IAAA,CAAE;IAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC;IAC3B,OAAO,MAAM;AACf,CAAC;AAED,MAAM,SAAS,CAAC;IACd,WAAW,EAAG;QAChB,mBAAA,GACI,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE;QACzB,IAAI,CAAC,KAAK,GAAG;YAAC,IAAI,CAAC,QAAQ;SAAC;IAChC;IAEE,IAAI,GAAG,GAAG;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5C;IAEE,IAAI,IAAI,GAAG;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC;IAAA;IAEpC,uBAAA,GACE,GAAG,CAAC,IAAI,EAAE;QACR,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAChC;IAEA,0BAAA,GACE,QAAQ,CAAC,KAAK,EAAE;QAClB,eAAA,GACI,MAAM,IAAI,GAAG,OAAO,CAAC;YAAE,KAAK;QAAA,CAAE,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IACzB;IAEE,SAAS,GAAG;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;QAC7B;QACA,wCAAA;QACI,OAAO,SAAS;IACpB;IAEE,aAAa,GAAG;QACd,MAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC5B;IAEE,MAAM,GAAG;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD;IAEA;;;GAGA,GACE,IAAI,CAAC,OAAO,EAAE;QAChB,gBAAA;QACI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;IACzD,aAAA;IACA,kDAAA;IACA;IAEA;;;GAGA,GACE,OAAO,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE;QAC1B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3B,CAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,GAAK,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;QAC7B;QACI,OAAO,OAAO;IAClB;IAEA;;GAEA,GACE,OAAO,SAAS,CAAC,IAAI,EAAE;QACrB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAC,EAAE,GAAI,OAAO,EAAE,KAAK,QAAQ,CAAC,EAAE;YAC3D,sCAAA;YACA,wBAAA;YACM,IAAI,CAAC,QAAQ,GAAG;gBAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;aAAC;QAC9C,CAAK,MAAM;YACL,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;gBAC/B,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;YAClC,CAAO,CAAC;QACR;IACA;AACA;AAEA;;;;;;;;;;;;;AAaA,GAEA;;CAEA,GACA,MAAM,gBAAgB,SAAS,SAAS,CAAC;IACzC;;GAEA,GACE,WAAW,CAAC,OAAO,CAAE;QACnB,KAAK,EAAE;QACP,IAAI,CAAC,OAAO,GAAG,OAAO;IAC1B;IAEA;;GAEA,GACE,OAAO,CAAC,IAAI,EAAE;QACZ,IAAI,IAAI,KAAK,EAAE,EAAE;YAAE,OAAO;QAAA;QAE1B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IAClB;IAEA,0BAAA,GACE,UAAU,CAAC,KAAK,EAAE;QAChB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;IACxB;IAEE,QAAQ,GAAG;QACT,IAAI,CAAC,SAAS,EAAE;IACpB;IAEA;;;GAGA,GACE,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE;QAClC,mBAAA,GACI,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI;QACzB,IAAI,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAEzC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IAClB;IAEE,MAAM,GAAG;QACP,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC;QACrD,OAAO,QAAQ,CAAC,KAAK,EAAE;IAC3B;IAEE,QAAQ,GAAG;QACT,IAAI,CAAC,aAAa,EAAE;QACpB,OAAO,IAAI;IACf;AACA;AAEA;;;GAGA,GAEA;;;CAGA,GACA,SAAS,MAAM,CAAC,EAAE,EAAE;IAClB,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI;IACpB,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,OAAO,EAAE;IAErC,OAAO,EAAE,CAAC,MAAM;AAClB;AAEA;;;CAGA,GACA,SAAS,SAAS,CAAC,EAAE,EAAE;IACrB,OAAO,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,CAAC;AAC/B;AAEA;;;CAGA,GACA,SAAS,gBAAgB,CAAC,EAAE,EAAE;IAC5B,OAAO,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;AAChC;AAEA;;;CAGA,GACA,SAAS,QAAQ,CAAC,EAAE,EAAE;IACpB,OAAO,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;AAChC;AAEA;;;CAGA,GACA,SAAS,MAAM,CAAC,GAAG,IAAI,EAAE;IACvB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD,OAAO,MAAM;AACf;AAEA;;;CAGA,GACA,SAAS,oBAAoB,CAAC,IAAI,EAAE;IAClC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAElC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,EAAE;QAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/B,OAAO,IAAI;IACf,CAAG,MAAM;QACL,OAAO,CAAA,CAAE;IACb;AACA;AAEA,wDAAA,GAEA;;;;;;CAMA,GACA,SAAS,MAAM,CAAC,GAAG,IAAI,EAAE;IACzB,4CAAA,GACE,MAAM,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC;IACvC,MAAM,MAAM,GAAG,MACjB,CAAO,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,IACzB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;IAC9C,OAAO,MAAM;AACf;AAEA;;;CAGA,GACA,SAAS,gBAAgB,CAAC,EAAE,EAAE;IAC5B,OAAO,AAAC,IAAI,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAE,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;AAC9D;AAEA;;;;CAIA,GACA,SAAS,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;IAC9B,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;IACnC,OAAO,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC;AACnC;AAEA,oEAAA;AACA,6DAAA;AACA,wEAAA;AACA,sEAAA;AACA,yBAAA;AACA,uEAAA;AACA,+BAAA;AACA,MAAM,UAAU,GAAG,gDAAgD;AAEnE,8CAAA;AACA,iEAAA;AACA,4CAAA;AACA,kEAAA;AACA,qEAAA;AACA,+CAAA;AACA;;;;CAIA,GACA,SAAS,sBAAsB,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,EAAE;IACrD,IAAI,WAAW,GAAG,CAAC;IAEnB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;QAC5B,WAAW,IAAI,CAAC;QAChB,MAAM,MAAM,GAAG,WAAW;QAC1B,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;QACtB,IAAI,GAAG,GAAG,EAAE;QAEZ,MAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAE;YACpB,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,EAAE;gBACV,GAAG,IAAI,EAAE;gBACT;YACR;YACM,GAAG,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC;YACnC,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAChD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC5C,4BAAA;gBACQ,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YACvD,CAAO,MAAM;gBACL,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;gBACf,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBACpB,WAAW,EAAE;gBACvB;YACA;QACA;QACI,OAAO,GAAG;IACd,CAAG,CAAC,CAAC,GAAG,EAAC,EAAE,GAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;AACxC;AAEA,gDAAA,GACA,gEAAA,GAEA,iBAAA;AACA,MAAM,gBAAgB,GAAG,MAAM;AAC/B,MAAM,QAAQ,GAAG,cAAc;AAC/B,MAAM,mBAAmB,GAAG,eAAe;AAC3C,MAAM,SAAS,GAAG,mBAAmB;AACrC,MAAM,WAAW,GAAG,wEAAwE,CAAC,CAAA,8BAAA;AAC7F,MAAM,gBAAgB,GAAG,cAAc,CAAC,CAAA,QAAA;AACxC,MAAM,cAAc,GAAG,8IAA8I;AAErK;;AAEA,GACA,MAAM,OAAO,GAAG,CAAC,IAAI,GAAG,CAAA,CAAE,KAAK;IAC7B,MAAM,YAAY,GAAG,WAAW;IAChC,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,IAAI,CAAC,KAAK,GAAG,MAAM,CACjB,YAAY,EACZ,MAAM,EACN,IAAI,CAAC,MAAM,EACX,MAAM,CAAC;IACb;IACE,OAAO,SAAS,CAAC;QACf,KAAK,EAAE,MAAM;QACb,KAAK,EAAE,YAAY;QACnB,GAAG,EAAE,GAAG;QACR,SAAS,EAAE,CAAC;QAChB,yBAAA,GACI,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;YACvB,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE;QAC3C;IACA,CAAG,EAAE,IAAI,CAAC;AACV,CAAC;AAED,eAAA;AACA,MAAM,gBAAgB,GAAG;IACvB,KAAK,EAAE,cAAc;IAAE,SAAS,EAAE;AACpC,CAAC;AACD,MAAM,gBAAgB,GAAG;IACvB,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,IAAI;IACX,GAAG,EAAE,IAAI;IACT,OAAO,EAAE,KAAK;IACd,QAAQ,EAAE;QAAC,gBAAgB;KAAA;AAC7B,CAAC;AACD,MAAM,iBAAiB,GAAG;IACxB,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,GAAG;IACV,GAAG,EAAE,GAAG;IACR,OAAO,EAAE,KAAK;IACd,QAAQ,EAAE;QAAC,gBAAgB;KAAA;AAC7B,CAAC;AACD,MAAM,kBAAkB,GAAG;IACzB,KAAK,EAAE;AACT,CAAC;AACD;;;;;;;CAOA,GACA,MAAM,OAAO,GAAG,SAAS,KAAK,EAAE,GAAG,EAAE,WAAW,GAAG,CAAA,CAAE,EAAE;IACrD,MAAM,IAAI,GAAG,SAAS,CACpB;QACE,KAAK,EAAE,SAAS;QAChB,KAAK;QACL,GAAG;QACH,QAAQ,EAAE,EAAA;IAChB,CAAK,EACD;IAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACjB,KAAK,EAAE,QAAQ;QACnB,yEAAA;QACA,2EAAA;QACI,KAAK,EAAE,kDAAkD;QACzD,GAAG,EAAE,0CAA0C;QAC/C,YAAY,EAAE,IAAI;QAClB,SAAS,EAAE;IACf,CAAG,CAAC;IACF,MAAM,YAAY,GAAG,MAAM,CAC7B,iDAAA;IACI,GAAG,EACH,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACR,0EAAA;IACI,gCAAgC,EAChC,oBAAoB,EACpB,mBAAmB,CAAA,oDAAA;;IAEvB,uDAAA;IACE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB;QACJ,4EAAA;QACA,sBAAA;QACA,0GAAA;QACA,MAAA;QAEA,wEAAA;QACA,sEAAA;QACA,uEAAA;QACA,mEAAA;QACA,wEAAA;QACA,iCAAA;QACA,EAAA;QACA,mCAAA;QACA,0DAAA;QAEM,KAAK,EAAE,MAAM,CACX,MAAM,EACN,GAAG,EACH,YAAY,EACZ,sBAAsB,EACtB,MAAM,CAAC,CAAA,4BAAA;IACf;IAEE,OAAO,IAAI;AACb,CAAC;AACD,MAAM,mBAAmB,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AAC9C,MAAM,oBAAoB,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;AACpD,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAC3C,MAAM,WAAW,GAAG;IAClB,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,SAAS;IAChB,SAAS,EAAE;AACb,CAAC;AACD,MAAM,aAAa,GAAG;IACpB,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,WAAW;IAClB,SAAS,EAAE;AACb,CAAC;AACD,MAAM,kBAAkB,GAAG;IACzB,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,gBAAgB;IACvB,SAAS,EAAE;AACb,CAAC;AACD,MAAM,WAAW,GAAG;IAClB,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,iBAAiB;IACxB,GAAG,EAAE,YAAY;IACjB,QAAQ,EAAE;QACR,gBAAgB;QAChB;YACE,KAAK,EAAE,IAAI;YACX,GAAG,EAAE,IAAI;YACT,SAAS,EAAE,CAAC;YACZ,QAAQ,EAAE;gBAAC,gBAAgB;aAAA;QACjC;KACA;AACA,CAAC;AACD,MAAM,UAAU,GAAG;IACjB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,QAAQ;IACf,SAAS,EAAE;AACb,CAAC;AACD,MAAM,qBAAqB,GAAG;IAC5B,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,mBAAmB;IAC1B,SAAS,EAAE;AACb,CAAC;AACD,MAAM,YAAY,GAAG;IACrB,gDAAA;IACE,KAAK,EAAE,SAAS,GAAG,mBAAmB;IACtC,SAAS,EAAE;AACb,CAAC;AAED;;;;;;CAMA,GACA,MAAM,iBAAiB,GAAG,SAAS,IAAI,EAAE;IACvC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EACvB;QACJ,yBAAA,GACM,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;YAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAE;QAChE,yBAAA,GACM,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;YAAE,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;QAAA;IACtF,CAAK,CAAC;AACN,CAAC;AAED,IAAI,KAAK,GAAA,WAAA,GAAgB,MAAM,CAAC,MAAM,CAAC;IACrC,SAAS,EAAE,IAAI;IACf,gBAAgB,EAAE,gBAAgB;IAClC,gBAAgB,EAAE,gBAAgB;IAClC,kBAAkB,EAAE,kBAAkB;IACtC,gBAAgB,EAAE,gBAAgB;IAClC,OAAO,EAAE,OAAO;IAChB,oBAAoB,EAAE,oBAAoB;IAC1C,mBAAmB,EAAE,mBAAmB;IACxC,aAAa,EAAE,aAAa;IAC5B,WAAW,EAAE,WAAW;IACxB,iBAAiB,EAAE,iBAAiB;IACpC,iBAAiB,EAAE,iBAAiB;IACpC,QAAQ,EAAE,QAAQ;IAClB,gBAAgB,EAAE,gBAAgB;IAClC,YAAY,EAAE,YAAY;IAC1B,WAAW,EAAE,WAAW;IACxB,SAAS,EAAE,SAAS;IACpB,kBAAkB,EAAE,kBAAkB;IACtC,iBAAiB,EAAE,iBAAiB;IACpC,WAAW,EAAE,WAAW;IACxB,cAAc,EAAE,cAAc;IAC9B,OAAO,EAAE,OAAO;IAChB,UAAU,EAAE,UAAU;IACtB,mBAAmB,EAAE,mBAAmB;IACxC,qBAAqB,EAAE;AACzB,CAAC,CAAC;AAEF;;;AAGA,GAEA,+BAAA;AACA,+DAAA;AAEA,8EAAA;AACA,sEAAA;AAEA,2EAAA;AACA,+EAAA;AACA,gFAAA;AACA,8EAAA;AACA,uEAAA;AAEA,SAAA;AAEA,iEAAA;AACA;;;;;;;;CAQA,GACA,SAAS,qBAAqB,CAAC,KAAK,EAAE,QAAQ,EAAE;IAC9C,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;IAC3C,IAAI,MAAM,KAAK,GAAG,EAAE;QAClB,QAAQ,CAAC,WAAW,EAAE;IAC1B;AACA;AAEA;;;CAGA,GACA,SAAS,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;IACvC,wCAAA;IACE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;QAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;QAC3B,OAAO,IAAI,CAAC,SAAS;IACzB;AACA;AAEA;;;CAGA,GACA,SAAS,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE;IACnC,IAAI,CAAC,MAAM,EAAE;IACb,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;IAE3B,4EAAA;IACA,6EAAA;IACA,yEAAA;IACA,+EAAA;IACA,QAAA;IACE,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,qBAAqB;IACrF,IAAI,CAAC,aAAa,GAAG,qBAAqB;IAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa;IACnD,OAAO,IAAI,CAAC,aAAa;IAE3B,6DAAA;IACA,gDAAA;IACA,wCAAA;IACE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC;AACtD;AAEA;;;CAGA,GACA,SAAS,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;IACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;IAElC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACxC;AAEA;;;CAGA,GACA,SAAS,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE;IACnC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;IACjB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC;IAEvF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;IACvB,OAAO,IAAI,CAAC,KAAK;AACnB;AAEA;;;CAGA,GACA,SAAS,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE;IACzC,wCAAA;IACE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC;AACtD;AAEA,0DAAA;AACA,oDAAA;AACA,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK;IACvC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;IACzB,wEAAA;IACA,qCAAA;IACE,IAAI,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;IAE1E,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,IAAI,CAAC;IAC5C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;QAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;IAAA,CAAE,CAAC;IAEzD,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ;IACrC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC5E,IAAI,CAAC,MAAM,GAAG;QACZ,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE;YACR,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE;gBAAE,UAAU,EAAE,IAAI;YAAA,CAAE;SACtD;IACA,CAAG;IACD,IAAI,CAAC,SAAS,GAAG,CAAC;IAElB,OAAO,YAAY,CAAC,WAAW;AACjC,CAAC;AAED,uDAAA;AACA,MAAM,eAAe,GAAG;IACtB,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO,CAAA,uBAAA;CACR;AAED,MAAM,qBAAqB,GAAG,SAAS;AAEvC;;;;;CAKA,GACA,SAAS,eAAe,CAAC,WAAW,EAAE,eAAe,EAAE,SAAS,GAAG,qBAAqB,EAAE;IAC1F,uDAAA,GACE,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAE9C,4EAAA;IACA,gFAAA;IACE,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClD,CAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QACrC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC;IACvC,CAAG,MAAM;QACL,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,SAAS,EAAE;YACzD,uDAAA;YACM,MAAM,CAAC,MAAM,CACX,gBAAgB,EAChB,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,eAAe,EAAE,SAAS;QAE1E,CAAK,CAAC;IACN;IACE,OAAO,gBAAgB;;IAEzB,MAAA;IAEA;;;;;;;GAOA,GACE,SAAS,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE;QAC3C,IAAI,eAAe,EAAE;YACnB,WAAW,GAAG,WAAW,CAAC,GAAG,EAAC,CAAC,GAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QACzD;QACI,WAAW,CAAC,OAAO,CAAC,SAAS,OAAO,EAAE;YACpC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;YAC/B,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG;gBAAC,SAAS;gBAAE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;aAAC;QAChF,CAAK,CAAC;IACN;AACA;AAEA;;;;;;;CAOA,GACA,SAAS,eAAe,CAAC,OAAO,EAAE,aAAa,EAAE;IACjD,gDAAA;IACA,qDAAA;IACE,IAAI,aAAa,EAAE;QACjB,OAAO,MAAM,CAAC,aAAa,CAAC;IAChC;IAEE,OAAO,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACvC;AAEA;;;2BAGA,GACA,SAAS,aAAa,CAAC,OAAO,EAAE;IAC9B,OAAO,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;AACxD;AAEA;;;;;AAKA,GAEA;;CAEA,GACA,MAAM,gBAAgB,GAAG,CAAA,CAAE;AAE3B;;CAEA,GACA,MAAM,KAAK,GAAG,CAAC,OAAO,KAAK;IACzB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;AACxB,CAAC;AAED;;;CAGA,GACA,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,KAAK;IACjC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC;AAC1C,CAAC;AAED;;;CAGA,GACA,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK;IACvC,IAAI,gBAAgB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;IAE/C,OAAO,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;IACtD,gBAAgB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;AAClD,CAAC;AAED,mCAAA,GAEA;;AAEA,GAEA,MAAM,eAAe,GAAG,IAAI,KAAK,EAAE;AAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BA,GACA,SAAS,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IAC/C,IAAI,MAAM,GAAG,CAAC;IACd,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC;IAC9B,iCAAA,GACE,MAAM,IAAI,GAAG,CAAA,CAAE;IACjB,gCAAA,GACE,MAAM,SAAS,GAAG,CAAA,CAAE;IAEpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACxC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;QACvB,MAAM,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9C;IACA,mFAAA;IACA,kCAAA;IACE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS;IACrB,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI;IACtB,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI;AACzB;AAEA;;CAEA,GACA,SAAS,eAAe,CAAC,IAAI,EAAE;IAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAEhC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE;QACtD,KAAK,CAAC,oEAAoE,CAAC;QAC3E,MAAM,eAAe;IACzB;IAEE,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;QACnE,KAAK,CAAC,2BAA2B,CAAC;QAClC,MAAM,eAAe;IACzB;IAEE,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;QAAE,GAAG,EAAE,YAAY;IAAA,CAAE,CAAC;IACxD,IAAI,CAAC,KAAK,GAAG,sBAAsB,CAAC,IAAI,CAAC,KAAK,EAAE;QAAE,QAAQ,EAAE,EAAE;IAAA,CAAE,CAAC;AACnE;AAEA;;CAEA,GACA,SAAS,aAAa,CAAC,IAAI,EAAE;IAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IAE9B,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;QAClD,KAAK,CAAC,8DAA8D,CAAC;QACrE,MAAM,eAAe;IACzB;IAEE,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;QAC/D,KAAK,CAAC,yBAAyB,CAAC;QAChC,MAAM,eAAe;IACzB;IAEE,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE;QAAE,GAAG,EAAE,UAAU;IAAA,CAAE,CAAC;IACpD,IAAI,CAAC,GAAG,GAAG,sBAAsB,CAAC,IAAI,CAAC,GAAG,EAAE;QAAE,QAAQ,EAAE,EAAE;IAAA,CAAE,CAAC;AAC/D;AAEA;;;;;;;;;;CAUA,GACA,SAAS,UAAU,CAAC,IAAI,EAAE;IACxB,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;QACvE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK;QAC5B,OAAO,IAAI,CAAC,KAAK;IACrB;AACA;AAEA;;CAEA,GACA,SAAS,UAAU,CAAC,IAAI,EAAE;IACxB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;QACvC,IAAI,CAAC,UAAU,GAAG;YAAE,KAAK,EAAE,IAAI,CAAC,UAAU;QAAA,CAAE;IAChD;IACE,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACrC,IAAI,CAAC,QAAQ,GAAG;YAAE,KAAK,EAAE,IAAI,CAAC,QAAQ;QAAA,CAAE;IAC5C;IAEE,eAAe,CAAC,IAAI,CAAC;IACrB,aAAa,CAAC,IAAI,CAAC;AACrB;AAEA;;;;;;AAMA,GAEA,cAAA;AAEA;;;;;;;CAOA,GACA,SAAS,eAAe,CAAC,QAAQ,EAAE;IACnC;;;;;GAKA,GACE,SAAS,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;QAC7B,OAAO,IAAI,MAAM,CACf,MAAM,CAAC,KAAK,CAAC,EACb,MACN,CAAS,QAAQ,CAAC,gBAAgB,GAAG,GAAG,GAAG,EAAE,IAC7C,CAAS,QAAQ,CAAC,YAAY,GAAG,GAAG,GAAG,EAAE,IACzC,CAAS,MAAM,GAAG,GAAG,GAAG,EAAE;IAE1B;IAEA;;;;;;;;;;;;EAYA,GACE,MAAM,UAAU,CAAC;QACf,WAAW,EAAG;YACZ,IAAI,CAAC,YAAY,GAAG,CAAA,CAAE;YAC5B,aAAA;YACM,IAAI,CAAC,OAAO,GAAG,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACvB;QAEA,aAAA;QACI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE;YACrC,aAAA;YACM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI;YACtC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAC,IAAI;gBAAE,EAAE;aAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAAG,CAAC;QAC9C;QAEI,OAAO,GAAG;YACR,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrC,4DAAA;gBACA,aAAA;gBACQ,IAAI,CAAC,IAAI,GAAG,IAAM,IAAI;YAC9B;YACM,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAC,EAAE,GAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,sBAAsB,CAAC,WAAW,EAAE;gBAAE,QAAQ,EAAE,GAAG;YAAA,CAAE,CAAC,EAAE,IAAI,CAAC;YACrF,IAAI,CAAC,SAAS,GAAG,CAAC;QACxB;QAEA,sBAAA,GACI,IAAI,CAAC,CAAC,EAAE;YACN,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;YACzC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,KAAK,EAAE;gBAAE,OAAO,IAAI,CAAC;YAAA;YAEhC,wCAAA;YACM,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC;YACrE,aAAA;YACM,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAC5C,sEAAA;YACA,+CAAA;YACM,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YAElB,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC;QAC5C;IACA;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BA,GACE,MAAM,mBAAmB,CAAC;QACxB,WAAW,EAAG;YAClB,aAAA;YACM,IAAI,CAAC,KAAK,GAAG,EAAE;YACrB,aAAA;YACM,IAAI,CAAC,YAAY,GAAG,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG,CAAC;YAEd,IAAI,CAAC,SAAS,GAAG,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,CAAC;QACzB;QAEA,aAAA;QACI,UAAU,CAAC,KAAK,EAAE;YAChB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;YAE7D,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,GAAK,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAC1E,OAAO,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO;YAClC,OAAO,OAAO;QACpB;QAEI,0BAA0B,GAAG;YAC3B,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC;QAClC;QAEI,WAAW,GAAG;YACZ,IAAI,CAAC,UAAU,GAAG,CAAC;QACzB;QAEA,aAAA;QACI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE;YAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAC,EAAE;gBAAE,IAAI;aAAC,CAAC;YAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE;QAC7C;QAEA,sBAAA,GACI,IAAI,CAAC,CAAC,EAAE;YACN,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;YAC1C,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;YAC5B,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAE5B,8EAAA;YACA,0EAAA;YACA,0EAAA;YACA,uCAAA;YAEA,4CAAA;YACA,EAAA;YACA,iBAAA;YAEA,wEAAA;YACA,wEAAA;YACA,uEAAA;YACA,uEAAA;YACA,gDAAA;YAEA,mEAAA;YACA,wEAAA;YAEA,wDAAA;YACA,4DAAA;YACA,SAAA;YACA,iBAAA;YAEA,qEAAA;YACA,0EAAA;YACA,kCAAA;YACA,EAAA;YACA,6EAAA;YACA,kCAAA;YACA,uDAAA;YACA,uDAAA;YACM,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE;gBACrC,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;qBAAM;oBACpD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;oBAC7B,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC;oBACjC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC7B;YACA;YAEM,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,QAAQ,GAAG,CAAC;gBACtC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE;oBAC5C,+CAAA;oBACU,IAAI,CAAC,WAAW,EAAE;gBAC5B;YACA;YAEM,OAAO,MAAM;QACnB;IACA;IAEA;;;;;;GAMA,GACE,SAAS,cAAc,CAAC,IAAI,EAAE;QAC5B,MAAM,EAAE,GAAG,IAAI,mBAAmB,EAAE;QAEpC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAC,IAAI,GAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;gBAAE,IAAI,EAAE,IAAI;gBAAE,IAAI,EAAE,OAAO;YAAA,CAAE,CAAC,CAAC;QAEpF,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;gBAAE,IAAI,EAAE,KAAK;YAAA,CAAE,CAAC;QACrD;QACI,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE;gBAAE,IAAI,EAAE,SAAS;YAAA,CAAE,CAAC;QACnD;QAEI,OAAO,EAAE;IACb;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BA,GAEA;;;;;;;;GAQA,GACE,SAAS,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE;QACjC,MAAM,KAAK,GAA8B,IAAI,CAAC;QAC9C,IAAI,IAAI,CAAC,UAAU,EAAE,OAAO,KAAK;QAEjC;YACE,cAAc;YACpB,2EAAA;YACA,sCAAA;YACM,YAAY;YACZ,UAAU;YACV;SACD,CAAC,OAAO,EAAC,GAAG,GAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEnC,QAAQ,CAAC,kBAAkB,CAAC,OAAO,EAAC,GAAG,GAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEjE,6DAAA;QACI,IAAI,CAAC,aAAa,GAAG,IAAI;QAEzB;YACE,aAAa;YACnB,4EAAA;YACA,0DAAA;YACM,cAAc;YACpB,0CAAA;YACM;SACD,CAAC,OAAO,EAAC,GAAG,GAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEnC,IAAI,CAAC,UAAU,GAAG,IAAI;QAEtB,IAAI,cAAc,GAAG,IAAI;QACzB,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACrE,mEAAA;YACA,kEAAA;YACA,OAAA;YACM,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,IAAI,CAAC,QAAQ,CAAC;YAChD,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACvC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;QACnC;QACI,cAAc,GAAG,cAAc,IAAI,KAAK;QAExC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,gBAAgB,CAAC;QAC/E;QAEI,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC;QAErD,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO;YACrC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;YACzD,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;YAC7C,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;YAC7C,IAAI,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,aAAa,EAAE;gBAC/C,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,MAAM,CAAC,aAAa;YAC3E;QACA;QACI,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,MAAM,CAAiC,IAAI,CAAC,OAAO,EAAE;QACzF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG,EAAE;QAEtC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACzD,OAAO,iBAAiB,CAAC,CAAC,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC;QACvD,CAAK,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAAE,WAAW,CAAoB,CAAC,EAAG,KAAK,CAAC,CAAC;QAAA,CAAE,CAAC;QAEjF,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;QACtC;QAEI,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC;QACrC,OAAO,KAAK;IAChB;IAEE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC,kBAAkB,GAAG,EAAE;IAEpE,qCAAA;IACE,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC3D,MAAM,IAAI,KAAK,CAAC,2FAA2F,CAAC;IAChH;IAEA,sDAAA;IACE,QAAQ,CAAC,gBAAgB,GAAG,SAAS,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAA,CAAE,CAAC;IAEtE,OAAO,WAAW,CAAoB,QAAQ,EAAE;AAClD;AAEA;;;;;;;;;;GAUA,GACA,SAAS,kBAAkB,CAAC,IAAI,EAAE;IAChC,IAAI,CAAC,IAAI,EAAE,OAAO,KAAK;IAEvB,OAAO,IAAI,CAAC,cAAc,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/D;AAEA;;;;;;;;;GASA,GACA,SAAS,iBAAiB,CAAC,IAAI,EAAE;IAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QACzC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE;YACxD,OAAO,SAAS,CAAC,IAAI,EAAE;gBAAE,QAAQ,EAAE,IAAI;YAAA,CAAE,EAAE,OAAO,CAAC;QACzD,CAAK,CAAC;IACN;IAEA,SAAA;IACA,4EAAA;IACA,kEAAA;IACE,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,OAAO,IAAI,CAAC,cAAc;IAC9B;IAEA,QAAA;IACA,2DAAA;IACA,uDAAA;IACA,kCAAA;IACE,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,SAAS,CAAC,IAAI,EAAE;YAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QAAA,CAAE,CAAC;IACnF;IAEE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACzB,OAAO,SAAS,CAAC,IAAI,CAAC;IAC1B;IAEA,sDAAA;IACE,OAAO,IAAI;AACb;AAEA,IAAI,OAAO,GAAG,SAAS;AAEvB,MAAM,kBAAkB,SAAS,KAAK,CAAC;IACrC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAE;QACxB,KAAK,CAAC,MAAM,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,oBAAoB;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI;IACpB;AACA;AAEA;;;AAGA,GAIA;;;;;;;;;;;;;;;;;;;AAmBA,GAGA,MAAM,MAAM,GAAG,UAAU;AACzB,MAAM,OAAO,GAAG,SAAS;AACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAClC,MAAM,gBAAgB,GAAG,CAAC;AAE1B;;;CAGA,GACA,MAAM,IAAI,GAAG,SAAS,IAAI,EAAE;IAC5B,kEAAA;IACA,qCAAA,GACE,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IACvC,mCAAA,GACE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IACrC,yBAAA,GACE,MAAM,OAAO,GAAG,EAAE;IAEpB,qEAAA;IACA,sDAAA;IACE,IAAI,SAAS,GAAG,IAAI;IACpB,MAAM,kBAAkB,GAAG,qFAAqF;IAClH,qBAAA,GACE,MAAM,kBAAkB,GAAG;QAAE,iBAAiB,EAAE,IAAI;QAAE,IAAI,EAAE,YAAY;QAAE,QAAQ,EAAE,EAAE;IAAA,CAAE;IAE1F,uEAAA;IACA,yCAAA;IACA,sBAAA,GACE,IAAI,OAAO,GAAG;QACZ,mBAAmB,EAAE,KAAK;QAC1B,kBAAkB,EAAE,KAAK;QACzB,aAAa,EAAE,oBAAoB;QACnC,gBAAgB,EAAE,6BAA6B;QAC/C,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,UAAU;QACvB,SAAS,EAAE,IAAI;QACnB,oEAAA;QACA,0DAAA;QACI,SAAS,EAAE;IACf,CAAG;IAEH,qBAAA,GAEA;;;GAGA,GACE,SAAS,kBAAkB,CAAC,YAAY,EAAE;QACxC,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC;IACnD;IAEA;;GAEA,GACE,SAAS,aAAa,CAAC,KAAK,EAAE;QAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,SAAS,GAAG,GAAG;QAEnC,OAAO,IAAI,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE;QAEjE,6DAAA;QACI,MAAM,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;QACpD,IAAI,KAAK,EAAE;YACT,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,QAAQ,EAAE;gBACb,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,mDAAmD,EAAE,KAAK,CAAC;YACxE;YACM,OAAO,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc;QACjD;QAEI,OAAO,QACJ,KAAK,CAAC,KAAK,EACX,IAAI,CAAC,CAAC,MAAM,GAAK,kBAAkB,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IAC1E;IAEA;;;;;;;;;;;;;;;;;;;;EAoBA,GACE,SAAS,SAAS,CAAC,kBAAkB,EAAE,aAAa,EAAE,cAAc,EAAE;QACpE,IAAI,IAAI,GAAG,EAAE;QACb,IAAI,YAAY,GAAG,EAAE;QACrB,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,IAAI,GAAG,kBAAkB;YACzB,cAAc,GAAG,aAAa,CAAC,cAAc;YAC7C,YAAY,GAAG,aAAa,CAAC,QAAQ;QAC3C,CAAK,MAAM;YACX,UAAA;YACM,UAAU,CAAC,QAAQ,EAAE,qDAAqD,CAAC;YAC3E,UAAU,CAAC,QAAQ,EAAE,uGAAuG,CAAC;YAC7H,YAAY,GAAG,kBAAkB;YACjC,IAAI,GAAG,aAAa;QAC1B;QAEA,0DAAA;QACA,wCAAA;QACI,IAAI,cAAc,KAAK,SAAS,EAAE;YAAE,cAAc,GAAG,IAAI,CAAC;QAAA;QAE9D,mCAAA,GACI,MAAM,OAAO,GAAG;YACd,IAAI;YACJ,QAAQ,EAAE;QAChB,CAAK;QACL,2EAAA;QACA,4CAAA;QACI,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC;QAErC,wEAAA;QACA,qDAAA;QACI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAA,GACnB,OAAO,CAAC,MAAA,GACR,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC;QAE9D,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;QAC9B,uDAAA;QACI,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC;QAE/B,OAAO,MAAM;IACjB;IAEA;;;;;;;;EAQA,GACE,SAAS,UAAU,CAAC,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE;QAC/E,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QAE3C;;;;;KAKA,GACI,SAAS,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE;YACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QACrC;QAEI,SAAS,eAAe,GAAG;YACzB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACjB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC3B;YACR;YAEM,IAAI,SAAS,GAAG,CAAC;YACjB,GAAG,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC;YAClC,IAAI,KAAK,GAAG,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;YACjD,IAAI,GAAG,GAAG,EAAE;YAEZ,MAAO,KAAK,CAAE;gBACZ,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC;gBACnD,MAAM,IAAI,GAAG,QAAQ,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;gBAC1E,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC;gBACnC,IAAI,IAAI,EAAE;oBACR,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,GAAG,IAAI;oBACrC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;oBACpB,GAAG,GAAG,EAAE;oBAER,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;oBAChD,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,gBAAgB,EAAE,SAAS,IAAI,gBAAgB;oBACxE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;wBACpC,iDAAA;wBACA,2BAAA;wBACY,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;oBAC3B,CAAW,MAAM;wBACL,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,IAAI;wBACxD,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;oBAC3C;gBACA,CAAS,MAAM;oBACL,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;gBACzB;gBACQ,SAAS,GAAG,GAAG,CAAC,gBAAgB,CAAC,SAAS;gBAC1C,KAAK,GAAG,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;YACrD;YACM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC;YACtC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;QAC1B;QAEI,SAAS,kBAAkB,GAAG;YAC5B,IAAI,UAAU,KAAK,EAAE,EAAE;YAC7B,0BAAA,GACM,IAAI,MAAM,GAAG,IAAI;YAEjB,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ,EAAE;gBACvC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;oBAC/B,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;oBAC3B;gBACV;gBACQ,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACtF,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,GAAgC,MAAM,CAAC,IAAI,CAAC;YAClF,CAAO,MAAM;gBACL,MAAM,GAAG,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3F;YAEA,6EAAA;YACA,iFAAA;YACA,mFAAA;YACA,SAAA;YACM,IAAI,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE;gBACrB,SAAS,IAAI,MAAM,CAAC,SAAS;YACrC;YACM,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;QAChE;QAEI,SAAS,aAAa,GAAG;YACvB,IAAI,GAAG,CAAC,WAAW,IAAI,IAAI,EAAE;gBAC3B,kBAAkB,EAAE;YAC5B,CAAO,MAAM;gBACL,eAAe,EAAE;YACzB;YACM,UAAU,GAAG,EAAE;QACrB;QAEA;;;KAGA,GACI,SAAS,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE;YACnC,IAAI,OAAO,KAAK,EAAE,EAAE;YAEpB,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC;YACzB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;YACxB,OAAO,CAAC,QAAQ,EAAE;QACxB;QAEA;;;KAGA,GACI,SAAS,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC;YACT,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;YAC5B,MAAO,CAAC,IAAI,GAAG,CAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBAAE,CAAC,EAAE,CAAC;oBAAC,SAAS;gBAAA;gBACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;gBAC7D,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;gBACrB,IAAI,KAAK,EAAE;oBACT,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC;gBAClC,CAAS,MAAM;oBACL,UAAU,GAAG,IAAI;oBACjB,eAAe,EAAE;oBACjB,UAAU,GAAG,EAAE;gBACzB;gBACQ,CAAC,EAAE;YACX;QACA;QAEA;;;KAGA,GACI,SAAS,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE;YACjC,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;gBAChD,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;YAC7E;YACM,IAAI,IAAI,CAAC,UAAU,EAAE;gBAC3B,0DAAA;gBACQ,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;oBACzB,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;oBAClG,UAAU,GAAG,EAAE;gBACzB,CAAS,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;oBAC3C,oDAAA;oBACU,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;oBACtC,UAAU,GAAG,EAAE;gBACzB;YACA;YAEM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;gBAAE,MAAM,EAAE;oBAAE,KAAK,EAAE,GAAG;gBAAA,CAAE;YAAA,CAAE,CAAC;YACrD,OAAO,GAAG;QAChB;QAEA;;;;;KAKA,GACI,SAAS,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,kBAAkB,EAAE;YAClD,IAAI,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;YAExD,IAAI,OAAO,EAAE;gBACX,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAClB,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC;oBAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC;oBAC3B,IAAI,IAAI,CAAC,cAAc,EAAE,OAAO,GAAG,KAAK;gBAClD;gBAEQ,IAAI,OAAO,EAAE;oBACX,MAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAE;wBACrC,IAAI,GAAG,IAAI,CAAC,MAAM;oBAC9B;oBACU,OAAO,IAAI;gBACrB;YACA;YACA,uDAAA;YACA,8DAAA;YACM,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,kBAAkB,CAAC;YAChE;QACA;QAEA;;;;KAIA,GACI,SAAS,QAAQ,CAAC,MAAM,EAAE;YACxB,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE;gBACxC,+EAAA;gBACA,QAAA;gBACQ,UAAU,IAAI,MAAM,CAAC,CAAC,CAAC;gBACvB,OAAO,CAAC;YAChB,CAAO,MAAM;gBACb,0EAAA;gBACA,0BAAA;gBACQ,wBAAwB,GAAG,IAAI;gBAC/B,OAAO,CAAC;YAChB;QACA;QAEA;;;;;KAKA,GACI,SAAS,YAAY,CAAC,KAAK,EAAE;YAC3B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACvB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI;YAE1B,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC;YACxC,wDAAA;YACM,MAAM,eAAe,GAAG;gBAAC,OAAO,CAAC,aAAa;gBAAE,OAAO,CAAC,UAAU,CAAC;aAAC;YACpE,KAAK,MAAM,EAAE,IAAI,eAAe,CAAE;gBAChC,IAAI,CAAC,EAAE,EAAE;gBACT,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;gBACf,IAAI,IAAI,CAAC,cAAc,EAAE,OAAO,QAAQ,CAAC,MAAM,CAAC;YACxD;YAEM,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,UAAU,IAAI,MAAM;YAC5B,CAAO,MAAM;gBACL,IAAI,OAAO,CAAC,YAAY,EAAE;oBACxB,UAAU,IAAI,MAAM;gBAC9B;gBACQ,aAAa,EAAE;gBACf,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;oBACjD,UAAU,GAAG,MAAM;gBAC7B;YACA;YACM,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;YAC5B,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM;QACpD;QAEA;;;;KAIA,GACI,SAAS,UAAU,CAAC,KAAK,EAAE;YACzB,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACvB,MAAM,kBAAkB,GAAG,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;YAEjE,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,kBAAkB,CAAC;YACzD,IAAI,CAAC,OAAO,EAAE;gBAAE,OAAO,QAAQ,CAAC;YAAA;YAEhC,MAAM,MAAM,GAAG,GAAG;YAClB,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACtC,aAAa,EAAE;gBACf,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC/C,CAAO,MAAM,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAC9C,aAAa,EAAE;gBACf,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC3C,CAAO,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE;gBACtB,UAAU,IAAI,MAAM;YAC5B,CAAO,MAAM;gBACL,IAAI,CAAA,CAAE,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE;oBAC5C,UAAU,IAAI,MAAM;gBAC9B;gBACQ,aAAa,EAAE;gBACf,IAAI,MAAM,CAAC,UAAU,EAAE;oBACrB,UAAU,GAAG,MAAM;gBAC7B;YACA;YACM,GAAG;gBACD,IAAI,GAAG,CAAC,KAAK,EAAE;oBACb,OAAO,CAAC,SAAS,EAAE;gBAC7B;gBACQ,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;oBACjC,SAAS,IAAI,GAAG,CAAC,SAAS;gBACpC;gBACQ,GAAG,GAAG,GAAG,CAAC,MAAM;YACxB,CAAO,OAAQ,GAAG,KAAK,OAAO,CAAC,MAAM,CAAA;YAC/B,IAAI,OAAO,CAAC,MAAM,EAAE;gBAClB,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YAC3C;YACM,OAAO,MAAM,CAAC,SAAS,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM;QACjD;QAEI,SAAS,oBAAoB,GAAG;YAC9B,MAAM,IAAI,GAAG,EAAE;YACf,IAAK,IAAI,OAAO,GAAG,GAAG,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAE;gBACtE,IAAI,OAAO,CAAC,KAAK,EAAE;oBACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;gBACrC;YACA;YACM,IAAI,CAAC,OAAO,EAAC,IAAI,GAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClD;QAEA,6DAAA,GACI,IAAI,SAAS,GAAG,CAAA,CAAE;QAEtB;;;;;KAKA,GACI,SAAS,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE;YAC7C,MAAM,MAAM,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;YAEtC,kDAAA;YACM,UAAU,IAAI,eAAe;YAE7B,IAAI,MAAM,IAAI,IAAI,EAAE;gBAClB,aAAa,EAAE;gBACf,OAAO,CAAC;YAChB;YAEA,qEAAA;YACA,+FAAA;YACA,oDAAA;YACA,+DAAA;YACM,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,IAAI,MAAM,KAAK,EAAE,EAAE;gBAClH,sFAAA;gBACQ,UAAU,IAAI,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;gBACjE,IAAI,CAAC,SAAS,EAAE;oBACxB,2BAAA,GACU,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC9D,GAAG,CAAC,YAAY,GAAG,YAAY;oBAC/B,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,IAAI;oBAC5B,MAAM,GAAG;gBACnB;gBACQ,OAAO,CAAC;YAChB;YACM,SAAS,GAAG,KAAK;YAEjB,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC1B,OAAO,YAAY,CAAC,KAAK,CAAC;YAClC,CAAO,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,cAAc,EAAE;gBAC9D,+CAAA;gBACA,2BAAA,GACQ,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,kBAAkB,GAAG,MAAM,GAAG,cAAc,GAAA,CAAI,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC,GAAG,GAAG,CAAC;gBACtG,GAAG,CAAC,IAAI,GAAG,GAAG;gBACd,MAAM,GAAG;YACjB,CAAO,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE;gBAC/B,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;gBACnC,IAAI,SAAS,KAAK,QAAQ,EAAE;oBAC1B,OAAO,SAAS;gBAC1B;YACA;YAEA,0EAAA;YACA,sEAAA;YACA,8CAAA;YACM,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,KAAK,EAAE,EAAE;gBACrD,iDAAA;gBACQ,OAAO,CAAC;YAChB;YAEA,uEAAA;YACA,oEAAA;YACA,kEAAA;YACA,aAAA;YACM,IAAI,UAAU,GAAG,MAAM,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE;gBACvD,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,2DAA2D,CAAC;gBAClF,MAAM,GAAG;YACjB;YAEA;;;;;;MAMA,GAEM,UAAU,IAAI,MAAM;YACpB,OAAO,MAAM,CAAC,MAAM;QAC1B;QAEI,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,QAAQ,EAAE;YACb,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,YAAY,GAAG,GAAG,CAAC;QACjE;QAEI,MAAM,EAAE,GAAG,eAAe,CAAC,QAAQ,CAAC;QACpC,IAAI,MAAM,GAAG,EAAE;QACnB,yBAAA,GACI,IAAI,GAAG,GAAG,YAAY,IAAI,EAAE;QAChC,sCAAA,GACI,MAAM,aAAa,GAAG,CAAA,CAAE,CAAC,CAAA,uCAAA;QACzB,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC;QAC9C,oBAAoB,EAAE;QACtB,IAAI,UAAU,GAAG,EAAE;QACnB,IAAI,SAAS,GAAG,CAAC;QACjB,IAAI,KAAK,GAAG,CAAC;QACb,IAAI,UAAU,GAAG,CAAC;QAClB,IAAI,wBAAwB,GAAG,KAAK;QAEpC,IAAI;YACF,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBAC1B,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE;gBAEzB,OAAS;oBACP,UAAU,EAAE;oBACZ,IAAI,wBAAwB,EAAE;wBACxC,kDAAA;wBACA,mCAAA;wBACY,wBAAwB,GAAG,KAAK;oBAC5C,CAAW,MAAM;wBACL,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE;oBACrC;oBACU,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK;oBAE7B,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;oBACzD,iEAAA;oBAEU,IAAI,CAAC,KAAK,EAAE;oBAEZ,MAAM,WAAW,GAAG,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;oBACjE,MAAM,cAAc,GAAG,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;oBACxD,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,cAAc;gBAC9C;gBACQ,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACvD,CAAO,MAAM;gBACL,QAAQ,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;YACvD;YAEM,OAAO,CAAC,QAAQ,EAAE;YAClB,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE;YAEzB,OAAO;gBACL,QAAQ,EAAE,YAAY;gBACtB,KAAK,EAAE,MAAM;gBACb,SAAS;gBACT,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,OAAO;gBACjB,IAAI,EAAE;YACd,CAAO;QACP,CAAK,CAAC,OAAO,GAAG,EAAE;YACZ,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;gBAClD,OAAO;oBACL,QAAQ,EAAE,YAAY;oBACtB,KAAK,EAAE,MAAM,CAAC,eAAe,CAAC;oBAC9B,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,CAAC;oBACZ,UAAU,EAAE;wBACV,OAAO,EAAE,GAAG,CAAC,OAAO;wBACpB,KAAK;wBACL,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC;wBACxD,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,WAAW,EAAE;oBACzB,CAAW;oBACD,QAAQ,EAAE;gBACpB,CAAS;YACT,CAAO,MAAM,IAAI,SAAS,EAAE;gBACpB,OAAO;oBACL,QAAQ,EAAE,YAAY;oBACtB,KAAK,EAAE,MAAM,CAAC,eAAe,CAAC;oBAC9B,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,CAAC;oBACZ,WAAW,EAAE,GAAG;oBAChB,QAAQ,EAAE,OAAO;oBACjB,IAAI,EAAE;gBAChB,CAAS;YACT,CAAO,MAAM;gBACL,MAAM,GAAG;YACjB;QACA;IACA;IAEA;;;;;;GAMA,GACE,SAAS,uBAAuB,CAAC,IAAI,EAAE;QACrC,MAAM,MAAM,GAAG;YACb,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,CAAC;YACZ,IAAI,EAAE,kBAAkB;YACxB,QAAQ,EAAE,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO;QAC7C,CAAK;QACD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAC7B,OAAO,MAAM;IACjB;IAEA;;;;;;;;;;;;;EAaA,GACE,SAAS,aAAa,CAAC,IAAI,EAAE,cAAc,EAAE;QAC3C,cAAc,GAAG,cAAc,IAAI,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;QAC9E,MAAM,SAAS,GAAG,uBAAuB,CAAC,IAAI,CAAC;QAE/C,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,IAC/E,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK;QAE9B,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAA,gCAAA;QAE3B,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;YAC1C,yBAAA;YACM,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS;YAEvE,4CAAA;YACA,6DAAA;YACM,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE;gBAC5B,IAAI,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,QAAQ,EAAE;oBACrD,OAAO,CAAC;gBAClB,CAAS,MAAM,IAAI,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,QAAQ,EAAE;oBAC5D,OAAO,CAAC,CAAC;gBACnB;YACA;YAEA,mEAAA;YACA,uEAAA;YACA,0EAAA;YACA,4BAAA;YACM,OAAO,CAAC;QACd,CAAK,CAAC;QAEF,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,MAAM;QAErC,gCAAA,GACI,MAAM,MAAM,GAAG,IAAI;QACnB,MAAM,CAAC,UAAU,GAAG,UAAU;QAE9B,OAAO,MAAM;IACjB;IAEA;;;;;;GAMA,GACE,SAAS,eAAe,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE;QACzD,MAAM,QAAQ,GAAG,AAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,IAAK,UAAU;QAEpE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;QAC7B,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;IACjD;IAEA;;;;EAIA,GACE,SAAS,gBAAgB,CAAC,OAAO,EAAE;QACrC,sBAAA,GACI,IAAI,IAAI,GAAG,IAAI;QACf,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC;QAEvC,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QAElC,IAAI,CAAC,yBAAyB,EAC5B;YAAE,EAAE,EAAE,OAAO;YAAE,QAAQ;QAAA,CAAE,CAAC;QAE5B,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE;YAC/B,OAAO,CAAC,GAAG,CAAC,wFAAwF,EAAE,OAAO,CAAC;YAC9G;QACN;QAEA,4EAAA;QACA,wEAAA;QACA,sEAAA;QACA,2EAAA;QACA,yEAAA;QACI,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBAChC,OAAO,CAAC,IAAI,CAAC,+FAA+F,CAAC;gBAC7G,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC;gBACzE,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC;gBAChD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;YAC7B;YACM,IAAI,OAAO,CAAC,kBAAkB,EAAE;gBAC9B,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAChC,kDAAkD,EAClD,OAAO,CAAC,SAAA;gBAEV,MAAM,GAAG;YACjB;QACA;QAEI,IAAI,GAAG,OAAO;QACd,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW;QAC7B,MAAM,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,IAAI,EAAE;YAAE,QAAQ;YAAE,cAAc,EAAE,IAAI;QAAA,CAAE,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC;QAEnG,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK;QAChC,OAAO,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK;QACnC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;QACnD,OAAO,CAAC,MAAM,GAAG;YACf,QAAQ,EAAE,MAAM,CAAC,QAAQ;YAC/B,iCAAA;YACM,EAAE,EAAE,MAAM,CAAC,SAAS;YACpB,SAAS,EAAE,MAAM,CAAC,SAAA;QACxB,CAAK;QACD,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,OAAO,CAAC,UAAU,GAAG;gBACnB,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ;gBACpC,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,SAAA;YACrC,CAAO;QACP;QAEI,IAAI,CAAC,wBAAwB,EAAE;YAAE,EAAE,EAAE,OAAO;YAAE,MAAM;YAAE,IAAI;QAAA,CAAE,CAAC;IACjE;IAEA;;;;GAIA,GACE,SAAS,SAAS,CAAC,WAAW,EAAE;QAC9B,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC;IAC3C;IAEA,+BAAA;IACE,MAAM,gBAAgB,GAAG,MAAM;QAC7B,YAAY,EAAE;QACd,UAAU,CAAC,QAAQ,EAAE,yDAAyD,CAAC;IACnF,CAAG;IAEH,+BAAA;IACE,SAAS,sBAAsB,GAAG;QAChC,YAAY,EAAE;QACd,UAAU,CAAC,QAAQ,EAAE,+DAA+D,CAAC;IACzF;IAEE,IAAI,cAAc,GAAG,KAAK;IAE5B;;GAEA,GACE,SAAS,YAAY,GAAG;QAC1B,oDAAA;QACI,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;YACrC,cAAc,GAAG,IAAI;YACrB;QACN;QAEI,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC;QAC7D,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC;IACpC;IAEE,SAAS,IAAI,GAAG;QAClB,6DAAA;QACI,IAAI,cAAc,EAAE,YAAY,EAAE;IACtC;IAEA,8CAAA;IACE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,gBAAgB,EAAE;QAC5D,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,EAAE,KAAK,CAAC;IAC5D;IAEA;;;;;GAKA,GACE,SAAS,gBAAgB,CAAC,YAAY,EAAE,kBAAkB,EAAE;QAC1D,IAAI,IAAI,GAAG,IAAI;QACf,IAAI;YACF,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC;QACrC,CAAK,CAAC,OAAO,OAAO,EAAE;YAChB,KAAK,CAAC,uDAAuD,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAChG,qBAAA;YACM,IAAI,CAAC,SAAS,EAAE;gBAAE,MAAM,OAAO,CAAC;YAAA,CAAE,MAAM;gBAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAAA;YAC/D,qEAAA;YACA,qEAAA;YACA,qEAAA;YACA,qBAAA;YACM,IAAI,GAAG,kBAAkB;QAC/B;QACA,mEAAA;QACI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,YAAY;QACxC,SAAS,CAAC,YAAY,CAAC,GAAG,IAAI;QAC9B,IAAI,CAAC,aAAa,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;QAExD,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE;gBAAE,YAAY;YAAA,CAAE,CAAC;QACrD;IACA;IAEA;;;;GAIA,GACE,SAAS,kBAAkB,CAAC,YAAY,EAAE;QACxC,OAAO,SAAS,CAAC,YAAY,CAAC;QAC9B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAE;YACxC,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,YAAY,EAAE;gBACnC,OAAO,OAAO,CAAC,KAAK,CAAC;YAC7B;QACA;IACA;IAEA;;GAEA,GACE,SAAS,aAAa,GAAG;QACvB,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;IACjC;IAEA;;;GAGA,GACE,SAAS,WAAW,CAAC,IAAI,EAAE;QACzB,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;QACjC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACtD;IAEA;;;;GAIA,GACE,SAAS,eAAe,CAAC,SAAS,EAAE,EAAE,YAAY,EAAE,EAAE;QACpD,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,SAAS,GAAG;gBAAC,SAAS;aAAC;QAC7B;QACI,SAAS,CAAC,OAAO,EAAC,KAAK,IAAI;YAAE,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,GAAG,YAAY,CAAC;QAAA,CAAE,CAAC;IAChF;IAEA;;;GAGA,GACE,SAAS,aAAa,CAAC,IAAI,EAAE;QAC3B,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAC9B,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB;IAC1C;IAEA;;;;GAIA,GACE,SAAS,gBAAgB,CAAC,MAAM,EAAE;QACpC,wBAAA;QACI,IAAI,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE;YACzE,MAAM,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,KAAK;gBAC5C,MAAM,CAAC,uBAAuB,CAAC,CAC7B,MAAM,CAAC,MAAM,CAAC;oBAAE,KAAK,EAAE,IAAI,CAAC,EAAE;gBAAA,CAAE,EAAE,IAAI;YAEhD,CAAO;QACP;QACI,IAAI,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,EAAE;YACvE,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,KAAK;gBAC3C,MAAM,CAAC,sBAAsB,CAAC,CAC5B,MAAM,CAAC,MAAM,CAAC;oBAAE,KAAK,EAAE,IAAI,CAAC,EAAE;gBAAA,CAAE,EAAE,IAAI;YAEhD,CAAO;QACP;IACA;IAEA;;GAEA,GACE,SAAS,SAAS,CAAC,MAAM,EAAE;QACzB,gBAAgB,CAAC,MAAM,CAAC;QACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;IACxB;IAEA;;GAEA,GACE,SAAS,YAAY,CAAC,MAAM,EAAE;QAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;QACrC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAC9B;IACA;IAEA;;;;GAIA,GACE,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE;QACzB,MAAM,EAAE,GAAG,KAAK;QAChB,OAAO,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE;YAC/B,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE;gBACd,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;YACxB;QACA,CAAK,CAAC;IACN;IAEA;;;GAGA,GACE,SAAS,uBAAuB,CAAC,EAAE,EAAE;QACnC,UAAU,CAAC,QAAQ,EAAE,kDAAkD,CAAC;QACxE,UAAU,CAAC,QAAQ,EAAE,kCAAkC,CAAC;QAExD,OAAO,gBAAgB,CAAC,EAAE,CAAC;IAC/B;IAEA,wBAAA,GACE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;QAClB,SAAS;QACT,aAAa;QACb,YAAY;QACZ,gBAAgB;QACpB,4BAAA;QACI,cAAc,EAAE,uBAAuB;QACvC,SAAS;QACT,gBAAgB;QAChB,sBAAsB;QACtB,gBAAgB;QAChB,kBAAkB;QAClB,aAAa;QACb,WAAW;QACX,eAAe;QACf,aAAa;QACb,OAAO;QACP,SAAS;QACT;IACJ,CAAG,CAAC;IAEF,IAAI,CAAC,SAAS,GAAG,WAAW;QAAE,SAAS,GAAG,KAAK,CAAC;IAAA,CAAE;IAClD,IAAI,CAAC,QAAQ,GAAG,WAAW;QAAE,SAAS,GAAG,IAAI,CAAC;IAAA,CAAE;IAChD,IAAI,CAAC,aAAa,GAAG,OAAO;IAE5B,IAAI,CAAC,KAAK,GAAG;QACX,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,QAAQ;QAClB,gBAAgB,EAAE;IACtB,CAAG;IAED,IAAK,MAAM,GAAG,IAAI,KAAK,CAAE;QAC3B,aAAA;QACI,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YACxC,aAAA;YACM,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B;IACA;IAEA,mDAAA;IACE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC;IAE1B,OAAO,IAAI;AACb,CAAC;AAED,sDAAA;AACA,MAAM,SAAS,GAAG,IAAI,CAAC,CAAA,CAAE,CAAC;AAE1B,sEAAA;AACA,qDAAA;AACA,SAAS,CAAC,WAAW,GAAG,IAAM,IAAI,CAAC,CAAA,CAAE,CAAC;IAEtC,IAAc,GAAG,SAAS;AAC1B,SAAS,CAAC,WAAW,GAAG,SAAS;AACjC,SAAS,CAAC,OAAO,GAAG,SAAS;;AC7hF7B,SAAS,UAAU,CAAC,KAAY,EAAE,YAAsB,EAAE,EAAA;IACxD,OAAO,MACJ,GAAG,CAAC,IAAI,IAAG;QACV,MAAM,OAAO,GAAG,CAAC;eAAG,SAAS,EAAE;eAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC;SAAC;QAErF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC;;QAG3C,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,OAAO;SACR;IACH,CAAC,EACA,IAAI,EAAE;AACX;AAEA,SAAS,iBAAiB,CAAC,MAAW,EAAA;;IAEpC,OAAO,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE;AAC9C;AAEA,SAAS,UAAU,CAAC,eAAuB,EAAA;IACzC,OAAO,OAAO,CAACA,WAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AACxD;AAEA,SAAS,cAAc,CAAC,EACtB,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,eAAe,EAMhB,EAAA;IACC,MAAM,WAAW,GAAiB,EAAE;IAEpC,wKAAA,AAAY,EAAC,GAAG,GAAE,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,OAAO,EAAC,KAAK,IAAG;;QACjE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;QACxB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,eAAe;QAC7D,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,EAAE;QAE1C,MAAM,KAAK,GAAG,QAAQ,IAAA,CAAK,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAA,CAAI,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAQ,CAAC,CAAA,IAC9G,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IACtE,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAErE,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,EAAC,IAAI,IAAG;YAC/B,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;YAElC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvB,MAAM,UAAU,2JAAG,aAAU,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE;oBAC7C,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC9B,CAAA,CAAC;gBAEF,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;;YAG9B,IAAI,GAAG,EAAE;QACX,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,+JAAO,gBAAa,CAAC,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC;AAC/C;AAEA,sEAAA;AACA,SAAS,UAAU,CAAC,KAAU,EAAA;IAC5B,OAAO,OAAO,KAAK,KAAK,UAAU;AACpC;AAEM,SAAU,cAAc,CAAC,EAC7B,IAAI,EACJ,QAAQ,EACR,eAAe,EAKhB,EAAA;IACC,IAAI,CAAC;QAAC,WAAW;QAAE,eAAe;QAAE,eAAe;KAAC,CAAC,KAAK,EAAC,GAAG,GAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QAC5F,MAAM,KAAK,CACT,qFAAqF,CACtF;;IAGH,MAAM,cAAc,GAAgB,IAAI,kKAAM,CAAC;QAC7C,GAAG,EAAE,6JAAI,YAAS,CAAC,UAAU,CAAC;QAE9B,KAAK,EAAE;YACL,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAK,cAAc,CAAC;oBACnC,GAAG;oBACH,IAAI;oBACJ,QAAQ;oBACR,eAAe;iBAChB,CAAC;YACF,KAAK,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,KAAI;gBACxD,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC7D,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC7D,MAAM,QAAQ,IAAG,uKAAA,AAAY,EAAC,QAAQ,CAAC,GAAG,GAAE,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;gBAC5E,MAAM,QAAQ,2JAAG,gBAAA,AAAY,EAAC,QAAQ,CAAC,GAAG,GAAE,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;gBAE5E,IACE,WAAW,CAAC,UAAA,KAGR;oBAAC,WAAW;oBAAE,WAAW;iBAAC,CAAC,QAAQ,CAAC,IAAI,KAEvC,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAA,IAI7B,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAG;;oBAE/B;oBAEE,IAAI,CAAC,IAAI,KAAK,aAEX,IAAI,CAAC,EAAE,KAAK,aACZ,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAG;;wBAEtB;wBAEE,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAA,IAEd,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE;qBAE9C,CAAC;iBAEL,CAAC,CAAC,EACL;oBACA,OAAO,cAAc,CAAC;wBACpB,GAAG,EAAE,WAAW,CAAC,GAAG;wBACpB,IAAI;wBACJ,QAAQ;wBACR,eAAe;oBAChB,CAAA,CAAC;;gBAGJ,OAAO,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC;aAC/D;QACF,CAAA;QAED,KAAK,EAAE;YACL,WAAW,EAAC,KAAK,EAAA;gBACf,OAAO,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC;aACtC;QACF,CAAA;IACF,CAAA,CAAC;IAEF,OAAO,cAAc;AACvB;ACnJA;;;CAGG,GACU,MAAA,iBAAiB,8KAAG,UAAS,CAAC,MAAM,CAA2B;IAC1E,UAAU,GAAA;;QACR,OAAO;YACL,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA;YAClB,QAAQ,EAAE,CAAA,CAAE;YACZ,mBAAmB,EAAE,WAAW;YAChC,iBAAiB,EAAE,IAAI;YACvB,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,qBAAqB,GAAA;;QACnB,OAAO;eACF,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAI,KAAI,EAAE;YACxB,cAAc,CAAC;gBACb,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAC/B,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;aAC9C,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 7595, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-youtube/src/utils.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/extension-youtube/src/youtube.ts"], "sourcesContent": ["export const YOUTUBE_REGEX = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu\\.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/\nexport const YOUTUBE_REGEX_GLOBAL = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu\\.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/g\n\nexport const isValidYoutubeUrl = (url: string) => {\n  return url.match(YOUTUBE_REGEX)\n}\n\nexport interface GetEmbedUrlOptions {\n  url: string;\n  allowFullscreen?: boolean;\n  autoplay?: boolean;\n  ccLanguage?:string;\n  ccLoadPolicy?:boolean;\n  controls?: boolean;\n  disableKBcontrols?: boolean,\n  enableIFrameApi?: boolean;\n  endTime?: number;\n  interfaceLanguage?: string;\n  ivLoadPolicy?: number;\n  loop?: boolean;\n  modestBranding?: boolean;\n  nocookie?: boolean;\n  origin?: string;\n  playlist?: string;\n  progressBarColor?: string;\n  startAt?: number;\n  rel?: number;\n}\n\nexport const getYoutubeEmbedUrl = (nocookie?: boolean, isPlaylist?:boolean) => {\n  if (isPlaylist) {\n    return 'https://www.youtube-nocookie.com/embed/videoseries?list='\n  }\n  return nocookie ? 'https://www.youtube-nocookie.com/embed/' : 'https://www.youtube.com/embed/'\n}\n\nconst getYoutubeVideoOrPlaylistId = (\n  url: URL,\n): { id: string; isPlaylist?: boolean } | null => {\n  if (url.searchParams.has('v')) {\n    return { id: url.searchParams.get('v')! }\n  }\n\n  if (\n    url.hostname === 'youtu.be'\n    || url.pathname.includes('shorts')\n    || url.pathname.includes('live')\n  ) {\n    return { id: url.pathname.split('/').pop()! }\n  }\n\n  if (url.searchParams.has('list')) {\n    return { id: url.searchParams.get('list')!, isPlaylist: true }\n  }\n\n  return null\n}\n\nexport const getEmbedUrlFromYoutubeUrl = (options: GetEmbedUrlOptions) => {\n  const {\n    url,\n    allowFullscreen,\n    autoplay,\n    ccLanguage,\n    ccLoadPolicy,\n    controls,\n    disableKBcontrols,\n    enableIFrameApi,\n    endTime,\n    interfaceLanguage,\n    ivLoadPolicy,\n    loop,\n    modestBranding,\n    nocookie,\n    origin,\n    playlist,\n    progressBarColor,\n    startAt,\n    rel,\n  } = options\n\n  if (!isValidYoutubeUrl(url)) {\n    return null\n  }\n\n  // if is already an embed url, return it\n  if (url.includes('/embed/')) {\n    return url\n  }\n\n  const urlObject = new URL(url)\n  const { id, isPlaylist } = getYoutubeVideoOrPlaylistId(urlObject) ?? {}\n\n  if (!id) { return null }\n\n  const embedUrl = new URL(`${getYoutubeEmbedUrl(nocookie, isPlaylist)}${id}`)\n\n  if (urlObject.searchParams.has('t')) {\n    embedUrl.searchParams.set('start', urlObject.searchParams.get('t')!.replaceAll('s', ''))\n  }\n\n  if (allowFullscreen === false) {\n    embedUrl.searchParams.set('fs', '0')\n  }\n\n  if (autoplay) {\n    embedUrl.searchParams.set('autoplay', '1')\n  }\n\n  if (ccLanguage) {\n    embedUrl.searchParams.set('cc_lang_pref', ccLanguage)\n  }\n\n  if (ccLoadPolicy) {\n    embedUrl.searchParams.set('cc_load_policy', '1')\n  }\n\n  if (!controls) {\n    embedUrl.searchParams.set('controls', '0')\n  }\n\n  if (disableKBcontrols) {\n    embedUrl.searchParams.set('disablekb', '1')\n  }\n\n  if (enableIFrameApi) {\n    embedUrl.searchParams.set('enablejsapi', '1')\n  }\n\n  if (endTime) {\n    embedUrl.searchParams.set('end', endTime.toString())\n  }\n\n  if (interfaceLanguage) {\n    embedUrl.searchParams.set('hl', interfaceLanguage)\n  }\n\n  if (ivLoadPolicy) {\n    embedUrl.searchParams.set('iv_load_policy', ivLoadPolicy.toString())\n  }\n\n  if (loop) {\n    embedUrl.searchParams.set('loop', '1')\n  }\n\n  if (modestBranding) {\n    embedUrl.searchParams.set('modestbranding', '1')\n  }\n\n  if (origin) {\n    embedUrl.searchParams.set('origin', origin)\n  }\n\n  if (playlist) {\n    embedUrl.searchParams.set('playlist', playlist)\n  }\n\n  if (startAt) {\n    embedUrl.searchParams.set('start', startAt.toString())\n  }\n\n  if (progressBarColor) {\n    embedUrl.searchParams.set('color', progressBarColor)\n  }\n\n  if (rel !== undefined) {\n    embedUrl.searchParams.set('rel', rel.toString())\n  }\n\n  return embedUrl.toString()\n}\n", "import { mergeAttributes, Node, nodePasteRule } from '@tiptap/core'\n\nimport { getEmbedUrlFromYoutubeUrl, isValidYoutubeUrl, YOUTUBE_REGEX_GLOBAL } from './utils.js'\n\nexport interface YoutubeOptions {\n  /**\n   * Controls if the paste handler for youtube videos should be added.\n   * @default true\n   * @example false\n   */\n  addPasteHandler: boolean;\n\n  /**\n   * Controls if the youtube video should be allowed to go fullscreen.\n   * @default true\n   * @example false\n   */\n  allowFullscreen: boolean;\n\n  /**\n   * Controls if the youtube video should autoplay.\n   * @default false\n   * @example true\n   */\n  autoplay: boolean;\n\n  /**\n   * The language of the captions shown in the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  ccLanguage?: string;\n\n  /**\n   * Controls if the captions should be shown in the youtube video.\n   * @default undefined\n   * @example true\n   */\n  ccLoadPolicy?: boolean;\n\n  /**\n   * Controls if the controls should be shown in the youtube video.\n   * @default true\n   * @example false\n   */\n  controls: boolean;\n\n  /**\n   * Controls if the keyboard controls should be disabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  disableKBcontrols: boolean;\n\n  /**\n   * Controls if the iframe api should be enabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  enableIFrameApi: boolean;\n\n  /**\n   * The end time of the youtube video.\n   * @default 0\n   * @example 120\n   */\n  endTime: number;\n\n  /**\n   * The height of the youtube video.\n   * @default 480\n   * @example 720\n   */\n  height: number;\n\n  /**\n   * The language of the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  interfaceLanguage?: string;\n\n  /**\n   * Controls if the video annotations should be shown in the youtube video.\n   * @default 0\n   * @example 1\n   */\n  ivLoadPolicy: number;\n\n  /**\n   * Controls if the youtube video should loop.\n   * @default false\n   * @example true\n   */\n  loop: boolean;\n\n  /**\n   * Controls if the youtube video should show a small youtube logo.\n   * @default false\n   * @example true\n   */\n  modestBranding: boolean;\n\n  /**\n   * The HTML attributes for a youtube video node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * Controls if the youtube node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean;\n\n  /**\n   * Controls if the youtube video should be loaded from youtube-nocookie.com.\n   * @default false\n   * @example true\n   */\n  nocookie: boolean;\n\n  /**\n   * The origin of the youtube video.\n   * @default ''\n   * @example 'https://tiptap.dev'\n   */\n  origin: string;\n\n  /**\n   * The playlist of the youtube video.\n   * @default ''\n   * @example 'PLQg6GaokU5CwiVmsZ0dZm6VeIg0V5z1tK'\n   */\n  playlist: string;\n\n  /**\n   * The color of the youtube video progress bar.\n   * @default undefined\n   * @example 'red'\n   */\n  progressBarColor?: string;\n\n  /**\n   * The width of the youtube video.\n   * @default 640\n   * @example 1280\n   */\n  width: number;\n\n  /**\n   * Controls if the related youtube videos at the end are from the same channel.\n   * @default 1\n   * @example 0\n   */\n  rel: number;\n}\n\n/**\n * The options for setting a youtube video.\n */\ntype SetYoutubeVideoOptions = { src: string, width?: number, height?: number, start?: number }\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    youtube: {\n      /**\n       * Insert a youtube video\n       * @param options The youtube video attributes\n       * @example editor.commands.setYoutubeVideo({ src: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' })\n       */\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension adds support for youtube videos.\n * @see https://www.tiptap.dev/api/nodes/youtube\n */\nexport const Youtube = Node.create<YoutubeOptions>({\n  name: 'youtube',\n\n  addOptions() {\n    return {\n      addPasteHandler: true,\n      allowFullscreen: true,\n      autoplay: false,\n      ccLanguage: undefined,\n      ccLoadPolicy: undefined,\n      controls: true,\n      disableKBcontrols: false,\n      enableIFrameApi: false,\n      endTime: 0,\n      height: 480,\n      interfaceLanguage: undefined,\n      ivLoadPolicy: 0,\n      loop: false,\n      modestBranding: false,\n      HTMLAttributes: {},\n      inline: false,\n      nocookie: false,\n      origin: '',\n      playlist: '',\n      progressBarColor: undefined,\n      width: 640,\n      rel: 1,\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      start: {\n        default: 0,\n      },\n      width: {\n        default: this.options.width,\n      },\n      height: {\n        default: this.options.height,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'div[data-youtube-video] iframe',\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ({ commands }) => {\n        if (!isValidYoutubeUrl(options.src)) {\n          return false\n        }\n\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addPasteRules() {\n    if (!this.options.addPasteHandler) {\n      return []\n    }\n\n    return [\n      nodePasteRule({\n        find: YOUTUBE_REGEX_GLOBAL,\n        type: this.type,\n        getAttributes: match => {\n          return { src: match.input }\n        },\n      }),\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const embedUrl = getEmbedUrlFromYoutubeUrl({\n      url: HTMLAttributes.src,\n      allowFullscreen: this.options.allowFullscreen,\n      autoplay: this.options.autoplay,\n      ccLanguage: this.options.ccLanguage,\n      ccLoadPolicy: this.options.ccLoadPolicy,\n      controls: this.options.controls,\n      disableKBcontrols: this.options.disableKBcontrols,\n      enableIFrameApi: this.options.enableIFrameApi,\n      endTime: this.options.endTime,\n      interfaceLanguage: this.options.interfaceLanguage,\n      ivLoadPolicy: this.options.ivLoadPolicy,\n      loop: this.options.loop,\n      modestBranding: this.options.modestBranding,\n      nocookie: this.options.nocookie,\n      origin: this.options.origin,\n      playlist: this.options.playlist,\n      progressBarColor: this.options.progressBarColor,\n      startAt: HTMLAttributes.start || 0,\n      rel: this.options.rel,\n    })\n\n    HTMLAttributes.src = embedUrl\n\n    return [\n      'div',\n      { 'data-youtube-video': '' },\n      [\n        'iframe',\n        mergeAttributes(\n          this.options.HTMLAttributes,\n          {\n            width: this.options.width,\n            height: this.options.height,\n            allowfullscreen: this.options.allowFullscreen,\n            autoplay: this.options.autoplay,\n            ccLanguage: this.options.ccLanguage,\n            ccLoadPolicy: this.options.ccLoadPolicy,\n            disableKBcontrols: this.options.disableKBcontrols,\n            enableIFrameApi: this.options.enableIFrameApi,\n            endTime: this.options.endTime,\n            interfaceLanguage: this.options.interfaceLanguage,\n            ivLoadPolicy: this.options.ivLoadPolicy,\n            loop: this.options.loop,\n            modestBranding: this.options.modestBranding,\n            origin: this.options.origin,\n            playlist: this.options.playlist,\n            progressBarColor: this.options.progressBarColor,\n            rel: this.options.rel,\n          },\n          HTMLAttributes,\n        ),\n      ],\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,aAAa,GAAG,0IAA0I;AAChK,MAAM,oBAAoB,GAAG,2IAA2I;AAExK,MAAM,iBAAiB,GAAG,CAAC,GAAW,KAAI;IAC/C,OAAO,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;AACjC,CAAC;AAwBM,MAAM,kBAAkB,GAAG,CAAC,QAAkB,EAAE,UAAmB,KAAI;IAC5E,IAAI,UAAU,EAAE;QACd,OAAO,0DAA0D;;IAEnE,OAAO,QAAQ,GAAG,yCAAyC,GAAG,gCAAgC;AAChG,CAAC;AAED,MAAM,2BAA2B,GAAG,CAClC,GAAQ,KACuC;IAC/C,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QAC7B,OAAO;YAAE,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAE;QAAA,CAAE;;IAG3C,IACE,GAAG,CAAC,QAAQ,KAAK,cACd,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,KAC9B,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAChC;QACA,OAAO;YAAE,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG;QAAA,CAAE;;IAG/C,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QAChC,OAAO;YAAE,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE;YAAE,UAAU,EAAE,IAAI;QAAA,CAAE;;IAGhE,OAAO,IAAI;AACb,CAAC;AAEM,MAAM,yBAAyB,GAAG,CAAC,OAA2B,KAAI;;IACvE,MAAM,EACJ,GAAG,EACH,eAAe,EACf,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,iBAAiB,EACjB,YAAY,EACZ,IAAI,EACJ,cAAc,EACd,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,OAAO,EACP,GAAG,EACJ,GAAG,OAAO;IAEX,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;QAC3B,OAAO,IAAI;;;IAIb,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC3B,OAAO,GAAG;;IAGZ,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;IAC9B,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,CAAA,EAAA,GAAA,2BAA2B,CAAC,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,CAAA,CAAE;IAEvE,IAAI,CAAC,EAAE,EAAE;QAAE,OAAO,IAAI;;IAEtB,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA,EAAG,EAAE,CAAA,CAAE,CAAC;IAE5E,IAAI,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QACnC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;;IAG1F,IAAI,eAAe,KAAK,KAAK,EAAE;QAC7B,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;;IAGtC,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;;IAG5C,IAAI,UAAU,EAAE;QACd,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC;;IAGvD,IAAI,YAAY,EAAE;QAChB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC;;IAGlD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;;IAG5C,IAAI,iBAAiB,EAAE;QACrB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC;;IAG7C,IAAI,eAAe,EAAE;QACnB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;;IAG/C,IAAI,OAAO,EAAE;QACX,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;;IAGtD,IAAI,iBAAiB,EAAE;QACrB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC;;IAGpD,IAAI,YAAY,EAAE;QAChB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC;;IAGtE,IAAI,IAAI,EAAE;QACR,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;;IAGxC,IAAI,cAAc,EAAE;QAClB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC;;IAGlD,IAAI,MAAM,EAAE;QACV,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;;IAG7C,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;;IAGjD,IAAI,OAAO,EAAE;QACX,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;;IAGxD,IAAI,gBAAgB,EAAE;QACpB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC;;IAGtD,IAAI,GAAG,KAAK,SAAS,EAAE;QACrB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;;IAGlD,OAAO,QAAQ,CAAC,QAAQ,EAAE;AAC5B,CAAC;ACQD;;;CAGG,GACU,MAAA,OAAO,wJAAG,OAAI,CAAC,MAAM,CAAiB;IACjD,IAAI,EAAE,SAAS;IAEf,UAAU,GAAA;QACR,OAAO;YACL,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,IAAI;YACrB,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,SAAS;YACrB,YAAY,EAAE,SAAS;YACvB,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,KAAK;YACxB,eAAe,EAAE,KAAK;YACtB,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,GAAG;YACX,iBAAiB,EAAE,SAAS;YAC5B,YAAY,EAAE,CAAC;YACf,IAAI,EAAE,KAAK;YACX,cAAc,EAAE,KAAK;YACrB,cAAc,EAAE,CAAA,CAAE;YAClB,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,KAAK;YACf,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,gBAAgB,EAAE,SAAS;YAC3B,KAAK,EAAE,GAAG;YACV,GAAG,EAAE,CAAC;SACP;KACF;IAED,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;KAC3B;IAED,KAAK,GAAA;QACH,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;KAChD;IAED,SAAS,EAAE,IAAI;IAEf,aAAa,GAAA;QACX,OAAO;YACL,GAAG,EAAE;gBACH,OAAO,EAAE,IAAI;YACd,CAAA;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,CAAC;YACX,CAAA;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YAC5B,CAAA;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC7B,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,gCAAgC;YACtC,CAAA;SACF;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,eAAe,EAAE,CAAC,OAA+B,GAAK,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACrE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;wBACnC,OAAO,KAAK;;oBAGd,OAAO,QAAQ,CAAC,aAAa,CAAC;wBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,KAAK,EAAE,OAAO;oBACf,CAAA,CAAC;iBACH;SACF;KACF;IAED,aAAa,GAAA;QACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YACjC,OAAO,EAAE;;QAGX,OAAO;qKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,GAAE,KAAK,IAAG;oBACrB,OAAO;wBAAE,GAAG,EAAE,KAAK,CAAC,KAAK;oBAAA,CAAE;iBAC5B;aACF,CAAC;SACH;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,MAAM,QAAQ,GAAG,yBAAyB,CAAC;YACzC,GAAG,EAAE,cAAc,CAAC,GAAG;YACvB,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YAC7C,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;YACnC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;YACvC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACjD,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YAC7C,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;YAC7B,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACjD,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;YACvC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;YAC3C,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC3B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;YAC/C,OAAO,EAAE,cAAc,CAAC,KAAK,IAAI,CAAC;YAClC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;QACtB,CAAA,CAAC;QAEF,cAAc,CAAC,GAAG,GAAG,QAAQ;QAE7B,OAAO;YACL,KAAK;YACL;gBAAE,oBAAoB,EAAE,EAAE;YAAA,CAAE;YAC5B;gBACE,QAAQ;yKACR,kBAAA,AAAe,EACb,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B;oBACE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;oBACzB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;oBAC3B,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC7C,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;oBAC/B,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;oBACnC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;oBACvC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBACjD,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC7C,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;oBAC7B,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBACjD,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;oBACvC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;oBACvB,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;oBAC3C,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;oBAC3B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;oBAC/B,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;oBAC/C,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;gBACtB,CAAA,EACD,cAAc,CACf;aACF;SACF;KACF;AACF,CAAA", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 7864, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/suggestion/src/findSuggestionMatch.ts", "file:///E:/React-Native/PhysioPrep/physioprep/node_modules/%40tiptap/suggestion/src/suggestion.ts"], "sourcesContent": ["import { escapeForRegEx, Range } from '@tiptap/core'\nimport { ResolvedPos } from '@tiptap/pm/model'\n\nexport interface Trigger {\n  char: string\n  allowSpaces: boolean\n  allowToIncludeChar: boolean\n  allowedPrefixes: string[] | null\n  startOfLine: boolean\n  $position: ResolvedPos\n}\n\nexport type SuggestionMatch = {\n  range: Range\n  query: string\n  text: string\n} | null\n\nexport function findSuggestionMatch(config: Trigger): SuggestionMatch {\n  const {\n    char, allowSpaces: allowSpacesOption, allowToIncludeChar, allowedPrefixes, startOfLine, $position,\n  } = config\n\n  const allowSpaces = allowSpacesOption && !allowToIncludeChar\n\n  const escapedChar = escapeForRegEx(char)\n  const suffix = new RegExp(`\\\\s${escapedChar}$`)\n  const prefix = startOfLine ? '^' : ''\n  const finalEscapedChar = allowToIncludeChar ? '' : escapedChar\n  const regexp = allowSpaces\n    ? new RegExp(`${prefix}${escapedChar}.*?(?=\\\\s${finalEscapedChar}|$)`, 'gm')\n    : new RegExp(`${prefix}(?:^)?${escapedChar}[^\\\\s${finalEscapedChar}]*`, 'gm')\n\n  const text = $position.nodeBefore?.isText && $position.nodeBefore.text\n\n  if (!text) {\n    return null\n  }\n\n  const textFrom = $position.pos - text.length\n  const match = Array.from(text.matchAll(regexp)).pop()\n\n  if (!match || match.input === undefined || match.index === undefined) {\n    return null\n  }\n\n  // JavaScript doesn't have lookbehinds. This hacks a check that first character\n  // is a space or the start of the line\n  const matchPrefix = match.input.slice(Math.max(0, match.index - 1), match.index)\n  const matchPrefixIsAllowed = new RegExp(`^[${allowedPrefixes?.join('')}\\0]?$`).test(matchPrefix)\n\n  if (allowedPrefixes !== null && !matchPrefixIsAllowed) {\n    return null\n  }\n\n  // The absolute position of the match in the document\n  const from = textFrom + match.index\n  let to = from + match[0].length\n\n  // Edge case handling; if spaces are allowed and we're directly in between\n  // two triggers\n  if (allowSpaces && suffix.test(text.slice(to - 1, to + 1))) {\n    match[0] += ' '\n    to += 1\n  }\n\n  // If the $position is located within the matched substring, return that range\n  if (from < $position.pos && to >= $position.pos) {\n    return {\n      range: {\n        from,\n        to,\n      },\n      query: match[0].slice(char.length),\n      text: match[0],\n    }\n  }\n\n  return null\n}\n", "import { Editor, Range } from '@tiptap/core'\nimport { EditorState, Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet, EditorView } from '@tiptap/pm/view'\n\nimport { findSuggestionMatch as defaultFindSuggestionMatch } from './findSuggestionMatch.js'\n\nexport interface SuggestionOptions<I = any, TSelected = any> {\n  /**\n   * The plugin key for the suggestion plugin.\n   * @default 'suggestion'\n   * @example 'mention'\n   */\n  pluginKey?: PluginKey\n\n  /**\n   * The editor instance.\n   * @default null\n   */\n  editor: Editor\n\n  /**\n   * The character that triggers the suggestion.\n   * @default '@'\n   * @example '#'\n   */\n  char?: string\n\n  /**\n   * Allow spaces in the suggestion query. Not compatible with `allowToIncludeChar`. Will be disabled if `allowToIncludeChar` is set to `true`.\n   * @default false\n   * @example true\n  */\n  allowSpaces?: boolean\n\n  /**\n   * Allow the character to be included in the suggestion query. Not compatible with `allowSpaces`.\n   * @default false\n   */\n  allowToIncludeChar?: boolean\n\n  /**\n   * Allow prefixes in the suggestion query.\n   * @default [' ']\n   * @example [' ', '@']\n   */\n  allowedPrefixes?: string[] | null\n\n  /**\n   * Only match suggestions at the start of the line.\n   * @default false\n   * @example true\n   */\n  startOfLine?: boolean\n\n  /**\n   * The tag name of the decoration node.\n   * @default 'span'\n   * @example 'div'\n   */\n  decorationTag?: string\n\n  /**\n   * The class name of the decoration node.\n   * @default 'suggestion'\n   * @example 'mention'\n   */\n  decorationClass?: string\n\n  /**\n   * A function that is called when a suggestion is selected.\n   * @param props The props object.\n   * @param props.editor The editor instance.\n   * @param props.range The range of the suggestion.\n   * @param props.props The props of the selected suggestion.\n   * @returns void\n   * @example ({ editor, range, props }) => { props.command(props.props) }\n   */\n  command?: (props: { editor: Editor; range: Range; props: TSelected }) => void\n\n  /**\n   * A function that returns the suggestion items in form of an array.\n   * @param props The props object.\n   * @param props.editor The editor instance.\n   * @param props.query The current suggestion query.\n   * @returns An array of suggestion items.\n   * @example ({ editor, query }) => [{ id: 1, label: 'John Doe' }]\n   */\n  items?: (props: { query: string; editor: Editor }) => I[] | Promise<I[]>\n\n  /**\n   * The render function for the suggestion.\n   * @returns An object with render functions.\n   */\n  render?: () => {\n    onBeforeStart?: (props: SuggestionProps<I, TSelected>) => void;\n    onStart?: (props: SuggestionProps<I, TSelected>) => void;\n    onBeforeUpdate?: (props: SuggestionProps<I, TSelected>) => void;\n    onUpdate?: (props: SuggestionProps<I, TSelected>) => void;\n    onExit?: (props: SuggestionProps<I, TSelected>) => void;\n    onKeyDown?: (props: SuggestionKeyDownProps) => boolean;\n  }\n\n  /**\n   * A function that returns a boolean to indicate if the suggestion should be active.\n   * @param props The props object.\n   * @returns {boolean}\n   */\n  allow?: (props: { editor: Editor; state: EditorState; range: Range, isActive?: boolean }) => boolean\n  findSuggestionMatch?: typeof defaultFindSuggestionMatch\n}\n\nexport interface SuggestionProps<I = any, TSelected = any> {\n  /**\n   * The editor instance.\n   */\n  editor: Editor\n\n  /**\n   * The range of the suggestion.\n   */\n  range: Range\n\n  /**\n   * The current suggestion query.\n   */\n  query: string\n\n  /**\n   * The current suggestion text.\n   */\n  text: string\n\n  /**\n   * The suggestion items array.\n   */\n  items: I[]\n\n  /**\n   * A function that is called when a suggestion is selected.\n   * @param props The props object.\n   * @returns void\n   */\n  command: (props: TSelected) => void\n\n  /**\n   * The decoration node HTML element\n   * @default null\n   */\n  decorationNode: Element | null\n\n  /**\n   * The function that returns the client rect\n   * @default null\n   * @example () => new DOMRect(0, 0, 0, 0)\n   */\n  clientRect?: (() => DOMRect | null) | null\n}\n\nexport interface SuggestionKeyDownProps {\n  view: EditorView\n  event: KeyboardEvent\n  range: Range\n}\n\nexport const SuggestionPluginKey = new PluginKey('suggestion')\n\n/**\n * This utility allows you to create suggestions.\n * @see https://tiptap.dev/api/utilities/suggestion\n */\nexport function Suggestion<I = any, TSelected = any>({\n  pluginKey = SuggestionPluginKey,\n  editor,\n  char = '@',\n  allowSpaces = false,\n  allowToIncludeChar = false,\n  allowedPrefixes = [' '],\n  startOfLine = false,\n  decorationTag = 'span',\n  decorationClass = 'suggestion',\n  command = () => null,\n  items = () => [],\n  render = () => ({}),\n  allow = () => true,\n  findSuggestionMatch = defaultFindSuggestionMatch,\n}: SuggestionOptions<I, TSelected>) {\n  let props: SuggestionProps<I, TSelected> | undefined\n  const renderer = render?.()\n\n  const plugin: Plugin<any> = new Plugin({\n    key: pluginKey,\n\n    view() {\n      return {\n        update: async (view, prevState) => {\n          const prev = this.key?.getState(prevState)\n          const next = this.key?.getState(view.state)\n\n          // See how the state changed\n          const moved = prev.active && next.active && prev.range.from !== next.range.from\n          const started = !prev.active && next.active\n          const stopped = prev.active && !next.active\n          const changed = !started && !stopped && prev.query !== next.query\n\n          const handleStart = started || (moved && changed)\n          const handleChange = changed || moved\n          const handleExit = stopped || (moved && changed)\n\n          // Cancel when suggestion isn't active\n          if (!handleStart && !handleChange && !handleExit) {\n            return\n          }\n\n          const state = handleExit && !handleStart ? prev : next\n          const decorationNode = view.dom.querySelector(\n            `[data-decoration-id=\"${state.decorationId}\"]`,\n          )\n\n          props = {\n            editor,\n            range: state.range,\n            query: state.query,\n            text: state.text,\n            items: [],\n            command: commandProps => {\n              return command({\n                editor,\n                range: state.range,\n                props: commandProps,\n              })\n            },\n            decorationNode,\n            // virtual node for popper.js or tippy.js\n            // this can be used for building popups without a DOM node\n            clientRect: decorationNode\n              ? () => {\n                // because of `items` can be asynchrounous we’ll search for the current decoration node\n                  const { decorationId } = this.key?.getState(editor.state) // eslint-disable-line\n                const currentDecorationNode = view.dom.querySelector(\n                  `[data-decoration-id=\"${decorationId}\"]`,\n                )\n\n                return currentDecorationNode?.getBoundingClientRect() || null\n              }\n              : null,\n          }\n\n          if (handleStart) {\n            renderer?.onBeforeStart?.(props)\n          }\n\n          if (handleChange) {\n            renderer?.onBeforeUpdate?.(props)\n          }\n\n          if (handleChange || handleStart) {\n            props.items = await items({\n              editor,\n              query: state.query,\n            })\n          }\n\n          if (handleExit) {\n            renderer?.onExit?.(props)\n          }\n\n          if (handleChange) {\n            renderer?.onUpdate?.(props)\n          }\n\n          if (handleStart) {\n            renderer?.onStart?.(props)\n          }\n        },\n\n        destroy: () => {\n          if (!props) {\n            return\n          }\n\n          renderer?.onExit?.(props)\n        },\n      }\n    },\n\n    state: {\n      // Initialize the plugin's internal state.\n      init() {\n        const state: {\n          active: boolean\n          range: Range\n          query: null | string\n          text: null | string\n          composing: boolean\n          decorationId?: string | null\n        } = {\n          active: false,\n          range: {\n            from: 0,\n            to: 0,\n          },\n          query: null,\n          text: null,\n          composing: false,\n        }\n\n        return state\n      },\n\n      // Apply changes to the plugin state from a view transaction.\n      apply(transaction, prev, _oldState, state) {\n        const { isEditable } = editor\n        const { composing } = editor.view\n        const { selection } = transaction\n        const { empty, from } = selection\n        const next = { ...prev }\n\n        next.composing = composing\n\n        // We can only be suggesting if the view is editable, and:\n        //   * there is no selection, or\n        //   * a composition is active (see: https://github.com/ueberdosis/tiptap/issues/1449)\n        if (isEditable && (empty || editor.view.composing)) {\n          // Reset active state if we just left the previous suggestion range\n          if ((from < prev.range.from || from > prev.range.to) && !composing && !prev.composing) {\n            next.active = false\n          }\n\n          // Try to match against where our cursor currently is\n          const match = findSuggestionMatch({\n            char,\n            allowSpaces,\n            allowToIncludeChar,\n            allowedPrefixes,\n            startOfLine,\n            $position: selection.$from,\n          })\n          const decorationId = `id_${Math.floor(Math.random() * 0xffffffff)}`\n\n          // If we found a match, update the current state to show it\n          if (match && allow({\n            editor, state, range: match.range, isActive: prev.active,\n          })) {\n            next.active = true\n            next.decorationId = prev.decorationId ? prev.decorationId : decorationId\n            next.range = match.range\n            next.query = match.query\n            next.text = match.text\n          } else {\n            next.active = false\n          }\n        } else {\n          next.active = false\n        }\n\n        // Make sure to empty the range if suggestion is inactive\n        if (!next.active) {\n          next.decorationId = null\n          next.range = { from: 0, to: 0 }\n          next.query = null\n          next.text = null\n        }\n\n        return next\n      },\n    },\n\n    props: {\n      // Call the keydown hook if suggestion is active.\n      handleKeyDown(view, event) {\n        const { active, range } = plugin.getState(view.state)\n\n        if (!active) {\n          return false\n        }\n\n        return renderer?.onKeyDown?.({ view, event, range }) || false\n      },\n\n      // Setup decorator on the currently active suggestion.\n      decorations(state) {\n        const { active, range, decorationId } = plugin.getState(state)\n\n        if (!active) {\n          return null\n        }\n\n        return DecorationSet.create(state.doc, [\n          Decoration.inline(range.from, range.to, {\n            nodeName: decorationTag,\n            class: decorationClass,\n            'data-decoration-id': decorationId,\n          }),\n        ])\n      },\n    },\n  })\n\n  return plugin\n}\n"], "names": ["findSuggestionMatch", "defaultFindSuggestionMatch"], "mappings": ";;;;;;;;;;;;;;AAkBM,SAAU,mBAAmB,CAAC,MAAe,EAAA;;IACjD,MAAM,EACJ,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAClG,GAAG,MAAM;IAEV,MAAM,WAAW,GAAG,iBAAiB,IAAI,CAAC,kBAAkB;IAE5D,MAAM,WAAW,GAAG,0KAAA,AAAc,EAAC,IAAI,CAAC;IACxC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,CAAM,GAAA,EAAA,WAAW,CAAG,CAAA,CAAA,CAAC;IAC/C,MAAM,MAAM,GAAG,WAAW,GAAG,GAAG,GAAG,EAAE;IACrC,MAAM,gBAAgB,GAAG,kBAAkB,GAAG,EAAE,GAAG,WAAW;IAC9D,MAAM,MAAM,GAAG,cACX,IAAI,MAAM,CAAC,CAAG,EAAA,MAAM,CAAG,EAAA,WAAW,CAAY,SAAA,EAAA,gBAAgB,CAAK,GAAA,CAAA,EAAE,IAAI,IACzE,IAAI,MAAM,CAAC,GAAG,MAAM,CAAA,MAAA,EAAS,WAAW,CAAA,KAAA,EAAQ,gBAAgB,CAAA,EAAA,CAAI,EAAE,IAAI,CAAC;IAE/E,MAAM,IAAI,GAAG,CAAA,CAAA,EAAA,GAAA,SAAS,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,KAAI,SAAS,CAAC,UAAU,CAAC,IAAI;IAEtE,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI;;IAGb,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;IAC5C,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;IAErD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;QACpE,OAAO,IAAI;;;;IAKb,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC;IAChF,MAAM,oBAAoB,GAAG,IAAI,MAAM,CAAC,CAAK,EAAA,EAAA,eAAe,KAAf,IAAA,IAAA,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAE,IAAI,CAAC,EAAE,CAAC,CAAO,KAAA,CAAA,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;IAEhG,IAAI,eAAe,KAAK,IAAI,IAAI,CAAC,oBAAoB,EAAE;QACrD,OAAO,IAAI;;;IAIb,MAAM,IAAI,GAAG,QAAQ,GAAG,KAAK,CAAC,KAAK;IACnC,IAAI,EAAE,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;;;IAI/B,IAAI,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;QAC1D,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;QACf,EAAE,IAAI,CAAC;;;IAIT,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,IAAI,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE;QAC/C,OAAO;YACL,KAAK,EAAE;gBACL,IAAI;gBACJ,EAAE;YACH,CAAA;YACD,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;YAClC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;SACf;;IAGH,OAAO,IAAI;AACb;MCqFa,mBAAmB,GAAG,6JAAI,YAAS,CAAC,YAAY;AAE7D;;;CAGG,GACa,SAAA,UAAU,CAA2B,EACnD,SAAS,GAAG,mBAAmB,EAC/B,MAAM,EACN,IAAI,GAAG,GAAG,EACV,WAAW,GAAG,KAAK,EACnB,kBAAkB,GAAG,KAAK,EAC1B,eAAe,GAAG;IAAC,GAAG;CAAC,EACvB,WAAW,GAAG,KAAK,EACnB,aAAa,GAAG,MAAM,EACtB,eAAe,GAAG,YAAY,EAC9B,OAAO,GAAG,IAAM,IAAI,EACpB,KAAK,GAAG,IAAM,EAAE,EAChB,MAAM,GAAG,IAAA,CAAO,CAAA,CAAE,CAAC,EACnB,KAAK,GAAG,IAAM,IAAI,EAAA,qBAClBA,qBAAmB,GAAGC,mBAA0B,EAChB,EAAA;IAChC,IAAI,KAAgD;IACpD,MAAM,QAAQ,GAAG,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,EAAI;IAE3B,MAAM,MAAM,GAAgB,6JAAI,SAAM,CAAC;QACrC,GAAG,EAAE,SAAS;QAEd,IAAI,GAAA;YACF,OAAO;gBACL,MAAM,EAAE,OAAO,IAAI,EAAE,SAAS,KAAI;;oBAChC,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,SAAS,CAAC;oBAC1C,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;oBAG3C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;oBAC/E,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;oBAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM;oBAC3C,MAAM,OAAO,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK;oBAEjE,MAAM,WAAW,GAAG,OAAO,IAAK,KAAK,IAAI,OAAO,CAAC;oBACjD,MAAM,YAAY,GAAG,OAAO,IAAI,KAAK;oBACrC,MAAM,UAAU,GAAG,OAAO,IAAK,KAAK,IAAI,OAAO,CAAC;;oBAGhD,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,EAAE;wBAChD;;oBAGF,MAAM,KAAK,GAAG,UAAU,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI;oBACtD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAC3C,CAAA,qBAAA,EAAwB,KAAK,CAAC,YAAY,CAAA,EAAA,CAAI,CAC/C;oBAED,KAAK,GAAG;wBACN,MAAM;wBACN,KAAK,EAAE,KAAK,CAAC,KAAK;wBAClB,KAAK,EAAE,KAAK,CAAC,KAAK;wBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,KAAK,EAAE,EAAE;wBACT,OAAO,GAAE,YAAY,IAAG;4BACtB,OAAO,OAAO,CAAC;gCACb,MAAM;gCACN,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,KAAK,EAAE,YAAY;4BACpB,CAAA,CAAC;yBACH;wBACD,cAAc;;;wBAGd,UAAU,EAAE,iBACR,MAAK;;;4BAEH,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA,CAAA,sBAAA;4BAC3D,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAClD,CAAwB,qBAAA,EAAA,YAAY,CAAI,EAAA,CAAA,CACzC;4BAED,OAAO,CAAA,qBAAqB,KAAA,IAAA,IAArB,qBAAqB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAArB,qBAAqB,CAAE,qBAAqB,EAAE,KAAI,IAAI;4BAE7D,IAAI;qBACT;oBAED,IAAI,WAAW,EAAE;wBACf,CAAA,EAAA,GAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAG,KAAK,CAAC;;oBAGlC,IAAI,YAAY,EAAE;wBAChB,CAAA,EAAA,GAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAG,KAAK,CAAC;;oBAGnC,IAAI,YAAY,IAAI,WAAW,EAAE;wBAC/B,KAAK,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC;4BACxB,MAAM;4BACN,KAAK,EAAE,KAAK,CAAC,KAAK;wBACnB,CAAA,CAAC;;oBAGJ,IAAI,UAAU,EAAE;wBACd,CAAA,EAAA,GAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAG,KAAK,CAAC;;oBAG3B,IAAI,YAAY,EAAE;wBAChB,CAAA,EAAA,GAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAG,KAAK,CAAC;;oBAG7B,IAAI,WAAW,EAAE;wBACf,CAAA,EAAA,GAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAG,KAAK,CAAC;;iBAE7B;gBAED,OAAO,EAAE,MAAK;;oBACZ,IAAI,CAAC,KAAK,EAAE;wBACV;;oBAGF,CAAA,EAAA,GAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAG,KAAK,CAAC;iBAC1B;aACF;SACF;QAED,KAAK,EAAE;;YAEL,IAAI,GAAA;gBACF,MAAM,KAAK,GAOP;oBACF,MAAM,EAAE,KAAK;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC;wBACP,EAAE,EAAE,CAAC;oBACN,CAAA;oBACD,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,IAAI;oBACV,SAAS,EAAE,KAAK;iBACjB;gBAED,OAAO,KAAK;aACb;;YAGD,KAAK,EAAC,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAA;gBACvC,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;gBAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,IAAI;gBACjC,MAAM,EAAE,SAAS,EAAE,GAAG,WAAW;gBACjC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,SAAS;gBACjC,MAAM,IAAI,GAAG;oBAAE,GAAG,IAAI;gBAAA,CAAE;gBAExB,IAAI,CAAC,SAAS,GAAG,SAAS;;;;gBAK1B,IAAI,UAAU,IAAA,CAAK,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;oBAElD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBACrF,IAAI,CAAC,MAAM,GAAG,KAAK;;;oBAIrB,MAAM,KAAK,GAAGD,qBAAmB,CAAC;wBAChC,IAAI;wBACJ,WAAW;wBACX,kBAAkB;wBAClB,eAAe;wBACf,WAAW;wBACX,SAAS,EAAE,SAAS,CAAC,KAAK;oBAC3B,CAAA,CAAC;oBACF,MAAM,YAAY,GAAG,CAAM,GAAA,EAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE;;oBAGnE,IAAI,KAAK,IAAI,KAAK,CAAC;wBACjB,MAAM;wBAAE,KAAK;wBAAE,KAAK,EAAE,KAAK,CAAC,KAAK;wBAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;oBACzD,CAAA,CAAC,EAAE;wBACF,IAAI,CAAC,MAAM,GAAG,IAAI;wBAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,YAAY;wBACxE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;wBACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;wBACxB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;2BACjB;wBACL,IAAI,CAAC,MAAM,GAAG,KAAK;;uBAEhB;oBACL,IAAI,CAAC,MAAM,GAAG,KAAK;;;gBAIrB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAChB,IAAI,CAAC,YAAY,GAAG,IAAI;oBACxB,IAAI,CAAC,KAAK,GAAG;wBAAE,IAAI,EAAE,CAAC;wBAAE,EAAE,EAAE,CAAC;oBAAA,CAAE;oBAC/B,IAAI,CAAC,KAAK,GAAG,IAAI;oBACjB,IAAI,CAAC,IAAI,GAAG,IAAI;;gBAGlB,OAAO,IAAI;aACZ;QACF,CAAA;QAED,KAAK,EAAE;;YAEL,aAAa,EAAC,IAAI,EAAE,KAAK,EAAA;;gBACvB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;gBAErD,IAAI,CAAC,MAAM,EAAE;oBACX,OAAO,KAAK;;gBAGd,OAAO,CAAA,CAAA,KAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAG;oBAAE,IAAI;oBAAE,KAAK;oBAAE,KAAK;gBAAA,CAAE,CAAC,KAAI,KAAK;aAC9D;;YAGD,WAAW,EAAC,KAAK,EAAA;gBACf,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAE9D,IAAI,CAAC,MAAM,EAAE;oBACX,OAAO,IAAI;;gBAGb,+JAAO,gBAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;4KACrC,aAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;wBACtC,QAAQ,EAAE,aAAa;wBACvB,KAAK,EAAE,eAAe;wBACtB,oBAAoB,EAAE,YAAY;qBACnC,CAAC;iBACH,CAAC;aACH;QACF,CAAA;IACF,CAAA,CAAC;IAEF,OAAO,MAAM;AACf", "ignoreList": [0, 1], "debugId": null}}]}