import { Suspense } from "react";
import { TopicDetailsClient } from "./TopicDetailsClient";
import { getTopicById } from "../actions";
import { notFound } from "next/navigation";

interface TopicDetailsPageProps {
  params: Promise<{ id: string }>;
}

export default async function TopicDetailsPage({ params }: TopicDetailsPageProps) {
  // Await params before using
  const { id } = await params;

  // Fetch initial data on server side
  const result = await getTopicById(id);

  if (!result.success) {
    notFound();
  }


  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TopicDetailsClient topicId={id} />
    </Suspense>
  );
}
