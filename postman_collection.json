{"info": {"name": "PhysioPrep API", "description": "API collection for PhysioPrep application (updated for hierarchical topics/questions)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test User\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Test123!\"\n}"}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Test123!\"\n}"}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Name\",\n    \"email\": \"<EMAIL>\"\n}"}}}]}, {"name": "Users", "item": [{"name": "Get Leaderboard", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/leaderboard", "host": ["{{baseUrl}}"], "path": ["api", "users", "leaderboard"]}}}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/me/stats", "host": ["{{baseUrl}}"], "path": ["api", "users", "me", "stats"]}}}, {"name": "Update User Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/users/me/preferences", "host": ["{{baseUrl}}"], "path": ["api", "users", "me", "preferences"]}, "body": {"mode": "raw", "raw": "{\n    \"emailNotifications\": true,\n    \"pushNotifications\": true,\n    \"theme\": \"dark\"\n}"}}}]}, {"name": "Questions", "item": [{"name": "Get All Questions", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/questions", "host": ["{{baseUrl}}"], "path": ["api", "questions"]}}}, {"name": "Get Question by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/questions/:id", "host": ["{{baseUrl}}"], "path": ["api", "questions", ":id"], "variable": [{"key": "id", "value": "question_id_here"}]}}}, {"name": "Create Question", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/questions", "host": ["{{baseUrl}}"], "path": ["api", "questions"]}, "body": {"mode": "raw", "raw": "{\n  \"text\": \"What is the capital of France?\",\n  \"options\": [\n    { \"text\": \"Paris\", \"isCorrect\": true },\n    { \"text\": \"London\", \"isCorrect\": false },\n    { \"text\": \"Berlin\", \"isCorrect\": false },\n    { \"text\": \"Madrid\", \"isCorrect\": false }\n  ],\n  \"explanation\": \"Paris is the capital of France.\",\n  \"topic\": \"topic_id_here\",\n  \"difficulty\": \"easy\",\n  \"tier\": \"free\"\n}"}}}, {"name": "Update Question", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/questions/:id", "host": ["{{baseUrl}}"], "path": ["api", "questions", ":id"], "variable": [{"key": "id", "value": "question_id_here"}]}, "body": {"mode": "raw", "raw": "{\n  \"text\": \"Updated question text\",\n  \"topic\": \"topic_id_here\"\n}"}}}, {"name": "Delete Question", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/questions/:id", "host": ["{{baseUrl}}"], "path": ["api", "questions", ":id"], "variable": [{"key": "id", "value": "question_id_here"}]}}}, {"name": "Get Questions by Topic", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/questions/topic/:topicId", "host": ["{{baseUrl}}"], "path": ["api", "questions", "topic", ":topicId"], "variable": [{"key": "topicId", "value": "topic_id_here"}]}}}]}, {"name": "Topics", "item": [{"name": "Get All Topics", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/subjects/topics", "host": ["{{baseUrl}}"], "path": ["api", "subjects", "topics"]}}}, {"name": "Get Topic by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/subjects/topics/:id", "host": ["{{baseUrl}}"], "path": ["api", "subjects", "topics", ":id"], "variable": [{"key": "id", "value": "topic_id_here"}]}}}, {"name": "Create Topic", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/subjects/topics", "host": ["{{baseUrl}}"], "path": ["api", "subjects", "topics"]}, "body": {"mode": "raw", "raw": "{\n  \"topicName\": \"New Topic\",\n  \"description\": \"Description here\",\n  \"subject\": \"subject_id_here\"\n}"}}}, {"name": "Update Topic", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/subjects/topics/:id", "host": ["{{baseUrl}}"], "path": ["api", "subjects", "topics", ":id"], "variable": [{"key": "id", "value": "topic_id_here"}]}, "body": {"mode": "raw", "raw": "{\n  \"topicName\": \"Updated Topic\",\n  \"description\": \"Updated description\"\n}"}}}, {"name": "Delete Topic", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/subjects/topics/:id", "host": ["{{baseUrl}}"], "path": ["api", "subjects", "topics", ":id"], "variable": [{"key": "id", "value": "topic_id_here"}]}}}]}, {"name": "Quizzes", "item": [{"name": "Get All Quizzes", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/quizzes", "host": ["{{baseUrl}}"], "path": ["api", "quizzes"]}}}, {"name": "Get Quiz by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/quizzes/:id", "host": ["{{baseUrl}}"], "path": ["api", "quizzes", ":id"], "variable": [{"key": "id", "value": "quiz_id_here"}]}}}, {"name": "Submit Quiz", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/quizzes/:id/submit", "host": ["{{baseUrl}}"], "path": ["api", "quizzes", ":id", "submit"], "variable": [{"key": "id", "value": "quiz_id_here"}]}, "body": {"mode": "raw", "raw": "{\n    \"answers\": [\n        {\n            \"questionId\": \"question_id_here\",\n            \"selectedOptionId\": \"option_id_here\"\n        }\n    ],\n    \"timeSpent\": 300\n}"}}}]}, {"name": "Subjects", "item": [{"name": "Get All Subjects", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/subjects", "host": ["{{baseUrl}}"], "path": ["api", "subjects"]}}}, {"name": "Get Subject by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/subjects/:id", "host": ["{{baseUrl}}"], "path": ["api", "subjects", ":id"], "variable": [{"key": "id", "value": "subject_id_here"}]}}}, {"name": "Get Subject Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/subjects/:id/progress", "host": ["{{baseUrl}}"], "path": ["api", "subjects", ":id", "progress"], "variable": [{"key": "id", "value": "subject_id_here"}]}}}]}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/health", "host": ["{{baseUrl}}"], "path": ["api", "health"]}}}], "variable": [{"key": "baseUrl", "value": "http://***************:8081"}, {"key": "token", "value": "your_jwt_token_here"}]}