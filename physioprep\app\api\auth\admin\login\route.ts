import { NextResponse } from "next/server";
import axios from "axios";
import { handleApiError } from "@/lib/errorHandling";
import { cookies } from "next/headers";

const API_URL = process.env.API_URL || "http://localhost:5000/api";

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Call the backend API
    const response = await axios.post(`${API_URL}/auth/admin/login`, {
      email,
      password
    });

    if (response.data.success) {
      // Set admin cookies
      const cookieStore = await cookies();
      cookieStore.set("adminToken", response.data.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60, // 24 hours
      });
      
      if (response.data.data.refreshToken) {
        cookieStore.set("adminRefreshToken", response.data.data.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "strict",
          maxAge: 7 * 24 * 60 * 60, // 7 days
        });
      }
      
      cookieStore.set("isLoggedIn", "true", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60,
      });

      return NextResponse.json({
        success: true,
        data: response.data.data
      });
    }

    return NextResponse.json(response.data);
  } catch (error) {
    return handleApiError(error, { defaultMessage: "Admin login failed" });
  }
}
