{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/app/dashboard/topics/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport axios from \"axios\";\nimport { revalidatePath } from \"next/cache\";\nimport {\n  getApiUrl,\n  checkAuth,\n  handleServerActionError,\n  handleRedirectError,\n} from \"@/services/api\";\nimport type { Topic, Subject, ApiResponse } from \"@/types/types\";\n\n// Define the state types for form actions\nexport type TopicFormState = {\n  error: string | null;\n  success: boolean;\n  message?: string;\n  data?: Topic;\n};\n\nexport type TopicCreateData = {\n  topicName: string;\n  description: string;\n  subject: string;\n  isActive?: boolean;\n  isPremium?: boolean;\n};\n\nexport type TopicUpdateData = TopicCreateData;\n\n// Define admin stats type\nexport type TopicAdminStats = {\n  totalTopics: number;\n  activeTopics: number;\n  inactiveTopics: number;\n  premiumTopics: number;\n  freeTopics: number;\n  totalQuestions: number;\n  freeQuestions: number;\n  premiumQuestions: number;\n  totalAttempts: number;\n  averagePopularity: number;\n};\n\n// Define topics response type with admin stats\nexport type TopicsResponse = {\n  topics: Topic[];\n  adminStats?: TopicAdminStats;\n};\n\n// Get all topics\nexport async function getTopics(): Promise<ApiResponse<TopicsResponse>> {\n  try {\n    const authCheck = await checkAuth(true); // Require admin\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/topics`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: {\n        topics: response.data.data.topics || response.data,\n        adminStats: response.data.data.stats,\n      },\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch topics\"\n    );\n  }\n}\n\n// Get topic by ID\nexport async function getTopicById(id: string): Promise<ApiResponse<Topic>> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/topics/${id}`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: response.data.data || response.data,\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch topic\"\n    );\n  }\n}\n\n// Get all subjects for dropdown\nexport async function getSubjectsForDropdown(): Promise<\n  ApiResponse<Subject[]>\n> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/subjects`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: response.data.data || response.data,\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch subjects\"\n    );\n  }\n}\n\n// Create new topic\nexport async function createTopic(\n  prevState: TopicFormState,\n  formData: FormData\n): Promise<TopicFormState> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        error: \"Admin authentication required\",\n        success: false,\n      };\n    }\n\n    // Extract form data\n    const topicName = formData.get(\"topicName\") as string;\n    const descriptionString = formData.get(\"description\") as string;\n    const subject = formData.get(\"subject\") as string;\n    const isActive = formData.get(\"isActive\") === \"on\";\n    const isPremium = formData.get(\"isPremium\") === \"on\";\n\n    // Validate required fields\n    if (!topicName || !subject) {\n      return {\n        error: \"Topic name and subject are required\",\n        success: false,\n      };\n    }\n\n    const topicData: TopicCreateData = {\n      topicName: topicName.trim(),\n      description: descriptionString,\n      subject: subject.trim(),\n      isActive,\n      isPremium,\n    };\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.post(`${apiUrl}/topics`, topicData, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n        \"Content-Type\": \"application/json\",\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      return {\n        error: null,\n        success: true,\n        message: \"Topic created successfully\",\n        data: response.data.data,\n      };\n    }\n\n    return {\n      error: response.data.errors?.[0]?.msg || \"Failed to create topic\",\n      success: false,\n    };\n  } catch (error) {\n    await handleRedirectError(error as Error);\n\n    console.log(error);\n    const errorResult = await handleServerActionError(\n      error as Error,\n      \"Failed to create topic\"\n    );\n    return {\n      error: errorResult.errors[0]?.msg || \"Failed to create topic\",\n      success: false,\n    };\n  }\n}\n\n// Update existing topic\nexport async function updateTopic(\n  id: string,\n  prevState: TopicFormState,\n  formData: FormData\n): Promise<TopicFormState> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        error: \"Admin authentication required\",\n        success: false,\n      };\n    }\n\n    // Extract form data\n    const topicName = formData.get(\"topicName\") as string;\n    const descriptionString = formData.get(\"description\") as string;\n    const subject = formData.get(\"subject\") as string;\n    const isActive = formData.get(\"isActive\") === \"on\";\n    const isPremium = formData.get(\"isPremium\") === \"on\";\n\n    // Validate required fields\n    if (!topicName || !subject) {\n      return {\n        error: \"Topic name and subject are required\",\n        success: false,\n      };\n    }\n\n    const topicData: TopicUpdateData = {\n      topicName: topicName.trim(),\n      description: descriptionString,\n      subject: subject.trim(),\n      isActive,\n      isPremium,\n    };\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.put(`${apiUrl}/topics/${id}`, topicData, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n        \"Content-Type\": \"application/json\",\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      revalidatePath(`/dashboard/topics/${id}`);\n      return {\n        error: null,\n        success: true,\n        message: \"Topic updated successfully\",\n        data: response.data.data,\n      };\n    }\n\n    return {\n      error: response.data.errors?.[0]?.msg || \"Failed to update topic\",\n      success: false,\n    };\n  } catch (error) {\n    await handleRedirectError(error as Error);\n\n    const errorResult = await handleServerActionError(\n      error as Error,\n      \"Failed to update topic\"\n    );\n    return {\n      error: errorResult.errors[0]?.msg || \"Failed to update topic\",\n      success: false,\n    };\n  }\n}\n\n// Delete topic\nexport async function deleteTopic(\n  id: string\n): Promise<ApiResponse<{ message: string }>> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.delete(`${apiUrl}/topics/${id}`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      return {\n        success: true,\n        data: { message: \"Topic deleted successfully\" },\n      };\n    }\n\n    return {\n      success: false,\n      errors: response.data.errors || [{ msg: \"Failed to delete topic\" }],\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to delete topic\"\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAkJsB,cAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/app/dashboard/topics/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport axios from \"axios\";\nimport { revalidatePath } from \"next/cache\";\nimport {\n  getApiUrl,\n  checkAuth,\n  handleServerActionError,\n  handleRedirectError,\n} from \"@/services/api\";\nimport type { Topic, Subject, ApiResponse } from \"@/types/types\";\n\n// Define the state types for form actions\nexport type TopicFormState = {\n  error: string | null;\n  success: boolean;\n  message?: string;\n  data?: Topic;\n};\n\nexport type TopicCreateData = {\n  topicName: string;\n  description: string;\n  subject: string;\n  isActive?: boolean;\n  isPremium?: boolean;\n};\n\nexport type TopicUpdateData = TopicCreateData;\n\n// Define admin stats type\nexport type TopicAdminStats = {\n  totalTopics: number;\n  activeTopics: number;\n  inactiveTopics: number;\n  premiumTopics: number;\n  freeTopics: number;\n  totalQuestions: number;\n  freeQuestions: number;\n  premiumQuestions: number;\n  totalAttempts: number;\n  averagePopularity: number;\n};\n\n// Define topics response type with admin stats\nexport type TopicsResponse = {\n  topics: Topic[];\n  adminStats?: TopicAdminStats;\n};\n\n// Get all topics\nexport async function getTopics(): Promise<ApiResponse<TopicsResponse>> {\n  try {\n    const authCheck = await checkAuth(true); // Require admin\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/topics`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: {\n        topics: response.data.data.topics || response.data,\n        adminStats: response.data.data.stats,\n      },\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch topics\"\n    );\n  }\n}\n\n// Get topic by ID\nexport async function getTopicById(id: string): Promise<ApiResponse<Topic>> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/topics/${id}`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: response.data.data || response.data,\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch topic\"\n    );\n  }\n}\n\n// Get all subjects for dropdown\nexport async function getSubjectsForDropdown(): Promise<\n  ApiResponse<Subject[]>\n> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/subjects`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: response.data.data || response.data,\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch subjects\"\n    );\n  }\n}\n\n// Create new topic\nexport async function createTopic(\n  prevState: TopicFormState,\n  formData: FormData\n): Promise<TopicFormState> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        error: \"Admin authentication required\",\n        success: false,\n      };\n    }\n\n    // Extract form data\n    const topicName = formData.get(\"topicName\") as string;\n    const descriptionString = formData.get(\"description\") as string;\n    const subject = formData.get(\"subject\") as string;\n    const isActive = formData.get(\"isActive\") === \"on\";\n    const isPremium = formData.get(\"isPremium\") === \"on\";\n\n    // Validate required fields\n    if (!topicName || !subject) {\n      return {\n        error: \"Topic name and subject are required\",\n        success: false,\n      };\n    }\n\n    const topicData: TopicCreateData = {\n      topicName: topicName.trim(),\n      description: descriptionString,\n      subject: subject.trim(),\n      isActive,\n      isPremium,\n    };\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.post(`${apiUrl}/topics`, topicData, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n        \"Content-Type\": \"application/json\",\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      return {\n        error: null,\n        success: true,\n        message: \"Topic created successfully\",\n        data: response.data.data,\n      };\n    }\n\n    return {\n      error: response.data.errors?.[0]?.msg || \"Failed to create topic\",\n      success: false,\n    };\n  } catch (error) {\n    await handleRedirectError(error as Error);\n\n    console.log(error);\n    const errorResult = await handleServerActionError(\n      error as Error,\n      \"Failed to create topic\"\n    );\n    return {\n      error: errorResult.errors[0]?.msg || \"Failed to create topic\",\n      success: false,\n    };\n  }\n}\n\n// Update existing topic\nexport async function updateTopic(\n  id: string,\n  prevState: TopicFormState,\n  formData: FormData\n): Promise<TopicFormState> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        error: \"Admin authentication required\",\n        success: false,\n      };\n    }\n\n    // Extract form data\n    const topicName = formData.get(\"topicName\") as string;\n    const descriptionString = formData.get(\"description\") as string;\n    const subject = formData.get(\"subject\") as string;\n    const isActive = formData.get(\"isActive\") === \"on\";\n    const isPremium = formData.get(\"isPremium\") === \"on\";\n\n    // Validate required fields\n    if (!topicName || !subject) {\n      return {\n        error: \"Topic name and subject are required\",\n        success: false,\n      };\n    }\n\n    const topicData: TopicUpdateData = {\n      topicName: topicName.trim(),\n      description: descriptionString,\n      subject: subject.trim(),\n      isActive,\n      isPremium,\n    };\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.put(`${apiUrl}/topics/${id}`, topicData, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n        \"Content-Type\": \"application/json\",\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      revalidatePath(`/dashboard/topics/${id}`);\n      return {\n        error: null,\n        success: true,\n        message: \"Topic updated successfully\",\n        data: response.data.data,\n      };\n    }\n\n    return {\n      error: response.data.errors?.[0]?.msg || \"Failed to update topic\",\n      success: false,\n    };\n  } catch (error) {\n    await handleRedirectError(error as Error);\n\n    const errorResult = await handleServerActionError(\n      error as Error,\n      \"Failed to update topic\"\n    );\n    return {\n      error: errorResult.errors[0]?.msg || \"Failed to update topic\",\n      success: false,\n    };\n  }\n}\n\n// Delete topic\nexport async function deleteTopic(\n  id: string\n): Promise<ApiResponse<{ message: string }>> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.delete(`${apiUrl}/topics/${id}`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      return {\n        success: true,\n        data: { message: \"Topic deleted successfully\" },\n      };\n    }\n\n    return {\n      success: false,\n      errors: response.data.errors || [{ msg: \"Failed to delete topic\" }],\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to delete topic\"\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA4NsB,cAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/app/dashboard/topics/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport axios from \"axios\";\nimport { revalidatePath } from \"next/cache\";\nimport {\n  getApiUrl,\n  checkAuth,\n  handleServerActionError,\n  handleRedirectError,\n} from \"@/services/api\";\nimport type { Topic, Subject, ApiResponse } from \"@/types/types\";\n\n// Define the state types for form actions\nexport type TopicFormState = {\n  error: string | null;\n  success: boolean;\n  message?: string;\n  data?: Topic;\n};\n\nexport type TopicCreateData = {\n  topicName: string;\n  description: string;\n  subject: string;\n  isActive?: boolean;\n  isPremium?: boolean;\n};\n\nexport type TopicUpdateData = TopicCreateData;\n\n// Define admin stats type\nexport type TopicAdminStats = {\n  totalTopics: number;\n  activeTopics: number;\n  inactiveTopics: number;\n  premiumTopics: number;\n  freeTopics: number;\n  totalQuestions: number;\n  freeQuestions: number;\n  premiumQuestions: number;\n  totalAttempts: number;\n  averagePopularity: number;\n};\n\n// Define topics response type with admin stats\nexport type TopicsResponse = {\n  topics: Topic[];\n  adminStats?: TopicAdminStats;\n};\n\n// Get all topics\nexport async function getTopics(): Promise<ApiResponse<TopicsResponse>> {\n  try {\n    const authCheck = await checkAuth(true); // Require admin\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/topics`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: {\n        topics: response.data.data.topics || response.data,\n        adminStats: response.data.data.stats,\n      },\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch topics\"\n    );\n  }\n}\n\n// Get topic by ID\nexport async function getTopicById(id: string): Promise<ApiResponse<Topic>> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/topics/${id}`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: response.data.data || response.data,\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch topic\"\n    );\n  }\n}\n\n// Get all subjects for dropdown\nexport async function getSubjectsForDropdown(): Promise<\n  ApiResponse<Subject[]>\n> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.get(`${apiUrl}/subjects`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    return {\n      success: true,\n      data: response.data.data || response.data,\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to fetch subjects\"\n    );\n  }\n}\n\n// Create new topic\nexport async function createTopic(\n  prevState: TopicFormState,\n  formData: FormData\n): Promise<TopicFormState> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        error: \"Admin authentication required\",\n        success: false,\n      };\n    }\n\n    // Extract form data\n    const topicName = formData.get(\"topicName\") as string;\n    const descriptionString = formData.get(\"description\") as string;\n    const subject = formData.get(\"subject\") as string;\n    const isActive = formData.get(\"isActive\") === \"on\";\n    const isPremium = formData.get(\"isPremium\") === \"on\";\n\n    // Validate required fields\n    if (!topicName || !subject) {\n      return {\n        error: \"Topic name and subject are required\",\n        success: false,\n      };\n    }\n\n    const topicData: TopicCreateData = {\n      topicName: topicName.trim(),\n      description: descriptionString,\n      subject: subject.trim(),\n      isActive,\n      isPremium,\n    };\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.post(`${apiUrl}/topics`, topicData, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n        \"Content-Type\": \"application/json\",\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      return {\n        error: null,\n        success: true,\n        message: \"Topic created successfully\",\n        data: response.data.data,\n      };\n    }\n\n    return {\n      error: response.data.errors?.[0]?.msg || \"Failed to create topic\",\n      success: false,\n    };\n  } catch (error) {\n    await handleRedirectError(error as Error);\n\n    console.log(error);\n    const errorResult = await handleServerActionError(\n      error as Error,\n      \"Failed to create topic\"\n    );\n    return {\n      error: errorResult.errors[0]?.msg || \"Failed to create topic\",\n      success: false,\n    };\n  }\n}\n\n// Update existing topic\nexport async function updateTopic(\n  id: string,\n  prevState: TopicFormState,\n  formData: FormData\n): Promise<TopicFormState> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        error: \"Admin authentication required\",\n        success: false,\n      };\n    }\n\n    // Extract form data\n    const topicName = formData.get(\"topicName\") as string;\n    const descriptionString = formData.get(\"description\") as string;\n    const subject = formData.get(\"subject\") as string;\n    const isActive = formData.get(\"isActive\") === \"on\";\n    const isPremium = formData.get(\"isPremium\") === \"on\";\n\n    // Validate required fields\n    if (!topicName || !subject) {\n      return {\n        error: \"Topic name and subject are required\",\n        success: false,\n      };\n    }\n\n    const topicData: TopicUpdateData = {\n      topicName: topicName.trim(),\n      description: descriptionString,\n      subject: subject.trim(),\n      isActive,\n      isPremium,\n    };\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.put(`${apiUrl}/topics/${id}`, topicData, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n        \"Content-Type\": \"application/json\",\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      revalidatePath(`/dashboard/topics/${id}`);\n      return {\n        error: null,\n        success: true,\n        message: \"Topic updated successfully\",\n        data: response.data.data,\n      };\n    }\n\n    return {\n      error: response.data.errors?.[0]?.msg || \"Failed to update topic\",\n      success: false,\n    };\n  } catch (error) {\n    await handleRedirectError(error as Error);\n\n    const errorResult = await handleServerActionError(\n      error as Error,\n      \"Failed to update topic\"\n    );\n    return {\n      error: errorResult.errors[0]?.msg || \"Failed to update topic\",\n      success: false,\n    };\n  }\n}\n\n// Delete topic\nexport async function deleteTopic(\n  id: string\n): Promise<ApiResponse<{ message: string }>> {\n  try {\n    const authCheck = await checkAuth(true);\n    if (!authCheck.isAuthenticated) {\n      return {\n        success: false,\n        errors: [{ msg: \"Admin authentication required\" }],\n      };\n    }\n\n    const apiUrl = await getApiUrl();\n    const response = await axios.delete(`${apiUrl}/topics/${id}`, {\n      headers: {\n        Authorization: `Bearer ${authCheck.token}`,\n      },\n    });\n\n    if (response.data.success) {\n      revalidatePath(\"/dashboard/topics\");\n      return {\n        success: true,\n        data: { message: \"Topic deleted successfully\" },\n      };\n    }\n\n    return {\n      success: false,\n      errors: response.data.errors || [{ msg: \"Failed to delete topic\" }],\n    };\n  } catch (error) {\n    return await handleServerActionError(\n      error as Error,\n      \"Failed to delete topic\"\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAkHsB,yBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/editor/extentions.ts"], "sourcesContent": ["import {\r\n  TiptapImage,\r\n  TiptapLink,\r\n  UpdatedImage,\r\n  TaskList,\r\n  TaskItem,\r\n  HorizontalRule,\r\n  StarterKit,\r\n  Placeholder,\r\n  CharacterCount\r\n} from \"novel\";\r\n\r\nimport { cx } from \"class-variance-authority\";\r\n\r\n// TODO I am using cx here to get tailwind autocomplete working, idk if someone else can write a regex to just capture the class key in objects\r\n\r\n// You can overwrite the placeholder with your own configuration\r\nconst placeholder = Placeholder;\r\nconst tiptapLink = TiptapLink.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\r\n      \"text-muted-foreground underline underline-offset-[3px] hover:text-primary transition-colors cursor-pointer\",\r\n    ),\r\n  },\r\n});\r\n\r\nconst taskList = TaskList.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"not-prose pl-2\"),\r\n  },\r\n});\r\nconst taskItem = TaskItem.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"flex items-start my-4\"),\r\n  },\r\n  nested: true,\r\n});\r\n\r\nconst horizontalRule = HorizontalRule.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"mt-4 mb-6 border-t border-muted-foreground\"),\r\n  },\r\n});\r\n\r\nconst characterCount = CharacterCount.configure();\r\n\r\nconst starterKit = StarterKit.configure({\r\n  bulletList: {\r\n    HTMLAttributes: {\r\n      class: cx(\"list-disc list-outside leading-3 -mt-2\"),\r\n    },\r\n  },\r\n  orderedList: {\r\n    HTMLAttributes: {\r\n      class: cx(\"list-decimal list-outside leading-3 -mt-2\"),\r\n    },\r\n  },\r\n  listItem: {\r\n    HTMLAttributes: {\r\n      class: cx(\"leading-normal -mb-2\"),\r\n    },\r\n  },\r\n  blockquote: {\r\n    HTMLAttributes: {\r\n      class: cx(\"border-l-4 border-primary\"),\r\n    },\r\n  },\r\n  codeBlock: {\r\n    HTMLAttributes: {\r\n      class: cx(\"rounded-sm bg-muted border p-5 font-mono font-medium\"),\r\n    },\r\n  },\r\n  code: {\r\n    HTMLAttributes: {\r\n      class: cx(\"rounded-md bg-muted  px-1.5 py-1 font-mono font-medium\"),\r\n      spellcheck: \"false\",\r\n    },\r\n  },\r\n  horizontalRule: false,\r\n  dropcursor: {\r\n    color: \"#DBEAFE\",\r\n    width: 4,\r\n  },\r\n  gapcursor: false,\r\n});\r\n\r\nexport const defaultExtensions = [\r\n  starterKit,\r\n  placeholder,\r\n  TiptapLink,\r\n  TiptapImage,\r\n  UpdatedImage,\r\n  taskList,\r\n  taskItem,\r\n  horizontalRule,\r\n  tiptapLink,\r\n  characterCount,\r\n];"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAEA,+IAA+I;AAE/I,gEAAgE;AAChE,MAAM,cAAc,yJAAA,CAAA,cAAW;AAC/B,MAAM,aAAa,0MAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IACtC,gBAAgB;QACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EACN;IAEJ;AACF;AAEA,MAAM,WAAW,yKAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAClC,gBAAgB;QACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AACA,MAAM,WAAW,yKAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAClC,gBAAgB;QACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,QAAQ;AACV;AAEA,MAAM,iBAAiB,yJAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;IAC9C,gBAAgB;QACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AAEA,MAAM,iBAAiB,4NAAA,CAAA,iBAAc,CAAC,SAAS;AAE/C,MAAM,aAAa,uMAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IACtC,YAAY;QACV,gBAAgB;YACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,aAAa;QACX,gBAAgB;YACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,UAAU;QACR,gBAAgB;YACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,YAAY;QACV,gBAAgB;YACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,WAAW;QACT,gBAAgB;YACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,MAAM;QACJ,gBAAgB;YACd,OAAO,CAAA,GAAA,mKAAA,CAAA,KAAE,AAAD,EAAE;YACV,YAAY;QACd;IACF;IACA,gBAAgB;IAChB,YAAY;QACV,OAAO;QACP,OAAO;IACT;IACA,WAAW;AACb;AAEO,MAAM,oBAAoB;IAC/B;IACA;IACA,0MAAA,CAAA,aAAU;IACV,4MAAA,CAAA,cAAW;IACX,yJAAA,CAAA,eAAY;IACZ;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/editor/slash-command.tsx"], "sourcesContent": ["import {\r\n  CheckSquare,\r\n  Code,\r\n  Heading1,\r\n  Heading2,\r\n  Heading3,\r\n  ImageIcon,\r\n  List,\r\n  ListOrdered,\r\n  MessageSquarePlus,\r\n  Text,\r\n  TextQuote,\r\n  Youtube,\r\n} from \"lucide-react\";\r\n// import { startImageUpload } from \"novel/plugins\";\r\nimport { Command, renderItems,createSuggestionItems } from \"novel\";\r\n\r\nexport const suggestionItems = createSuggestionItems([\r\n  {\r\n    title: \"Send Feedback\",\r\n    description: \"Let us know how we can improve.\",\r\n    icon: <MessageSquarePlus size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).run();\r\n      window.open(\"/feedback\", \"_blank\");\r\n    },\r\n  },\r\n  {\r\n    title: \"Text\",\r\n    description: \"Just start typing with plain text.\",\r\n    searchTerms: [\"p\", \"paragraph\"],\r\n    icon: <Text size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .toggleNode(\"paragraph\", \"paragraph\")\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"To-do List\",\r\n    description: \"Track tasks with a to-do list.\",\r\n    searchTerms: [\"todo\", \"task\", \"list\", \"check\", \"checkbox\"],\r\n    icon: <CheckSquare size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleTaskList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 1\",\r\n    description: \"Big section heading.\",\r\n    searchTerms: [\"title\", \"big\", \"large\"],\r\n    icon: <Heading1 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 1 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 2\",\r\n    description: \"Medium section heading.\",\r\n    searchTerms: [\"subtitle\", \"medium\"],\r\n    icon: <Heading2 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 2 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 3\",\r\n    description: \"Small section heading.\",\r\n    searchTerms: [\"subtitle\", \"small\"],\r\n    icon: <Heading3 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 3 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Bullet List\",\r\n    description: \"Create a simple bullet list.\",\r\n    searchTerms: [\"unordered\", \"point\"],\r\n    icon: <List size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleBulletList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Numbered List\",\r\n    description: \"Create a list with numbering.\",\r\n    searchTerms: [\"ordered\"],\r\n    icon: <ListOrdered size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleOrderedList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Quote\",\r\n    description: \"Capture a quote.\",\r\n    searchTerms: [\"blockquote\"],\r\n    icon: <TextQuote size={18} />,\r\n    command: ({ editor, range }) =>\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .toggleNode(\"paragraph\", \"paragraph\")\r\n        .toggleBlockquote()\r\n        .run(),\r\n  },\r\n  {\r\n    title: \"Code\",\r\n    description: \"Capture a code snippet.\",\r\n    searchTerms: [\"codeblock\"],\r\n    icon: <Code size={18} />,\r\n    command: ({ editor, range }) =>\r\n      editor.chain().focus().deleteRange(range).toggleCodeBlock().run(),\r\n  },\r\n   {\r\n    title: \"Image\",\r\n    description: \"Upload an image from your computer.\",\r\n    searchTerms: [\"photo\", \"picture\", \"media\"],\r\n    icon: <ImageIcon size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).run();\r\n      // upload image\r\n      const input = document.createElement(\"input\");\r\n      input.type = \"file\";\r\n      input.accept = \"image/*\";\r\n      input.onchange = async () => {\r\n        if (input.files?.length) {\r\n          const file = input.files[0];\r\n          const pos = editor.view.state.selection.from;\r\n          // uploadFn(file, editor.view, pos);\r\n        }\r\n      };\r\n      input.click();\r\n    },\r\n  },\r\n  {\r\n    title: \"Youtube\",\r\n    description: \"Embed a Youtube video.\",\r\n    searchTerms: [\"video\", \"youtube\", \"embed\"],\r\n    icon: <Youtube size={18} />,\r\n    command: ({ editor, range }) => {\r\n      const videoLink = prompt(\"Please enter Youtube Video Link\");\r\n      //From https://regexr.com/3dj5t\r\n      const ytregex = new RegExp(\r\n        /^((?:https?:)?\\/\\/)?((?:www|m)\\.)?((?:youtube\\.com|youtu.be))(\\/(?:[\\w\\-]+\\?v=|embed\\/|v\\/)?)([\\w\\-]+)(\\S+)?$/,\r\n      );\r\n\r\n      if (ytregex.test(videoLink)) {\r\n        editor\r\n          .chain()\r\n          .focus()\r\n          .deleteRange(range)\r\n          .setYoutubeVideo({\r\n            src: videoLink,\r\n          })\r\n          .run();\r\n      } else {\r\n        if (videoLink !== null) {\r\n          alert(\"Please enter a correct Youtube Video Link\");\r\n        }\r\n      }\r\n    },\r\n  },\r\n]);\r\n\r\nexport const slashCommand = Command.configure({\r\n  suggestion: {\r\n    items: () => suggestionItems,\r\n    render: renderItems,\r\n  },\r\n});"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,oDAAoD;AACpD;;;;AAEO,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,wBAAqB,AAAD,EAAE;IACnD;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,uOAAA,CAAA,oBAAiB;YAAC,MAAM;;;;;;QAC/B,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG;YAC7C,OAAO,IAAI,CAAC,aAAa;QAC3B;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAK;SAAY;QAC/B,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,UAAU,CAAC,aAAa,aACxB,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAQ;YAAQ;YAAQ;YAAS;SAAW;QAC1D,oBAAM,6LAAC,8NAAA,CAAA,cAAW;YAAC,MAAM;;;;;;QACzB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,cAAc,GAAG,GAAG;QAChE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAS;YAAO;SAAQ;QACtC,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAY;SAAS;QACnC,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAY;SAAQ;QAClC,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAa;SAAQ;QACnC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,gBAAgB,GAAG,GAAG;QAClE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAU;QACxB,oBAAM,6LAAC,uNAAA,CAAA,cAAW;YAAC,MAAM;;;;;;QACzB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,iBAAiB,GAAG,GAAG;QACnE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAa;QAC3B,oBAAM,6LAAC,mNAAA,CAAA,YAAS;YAAC,MAAM;;;;;;QACvB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,UAAU,CAAC,aAAa,aACxB,gBAAgB,GAChB,GAAG;IACV;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAY;QAC1B,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,eAAe,GAAG,GAAG;IACnE;IACC;QACC,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAS;YAAW;SAAQ;QAC1C,oBAAM,6LAAC,2MAAA,CAAA,YAAS;YAAC,MAAM;;;;;;QACvB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG;YAC7C,eAAe;YACf,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,IAAI,GAAG;YACb,MAAM,MAAM,GAAG;YACf,MAAM,QAAQ,GAAG;gBACf,IAAI,MAAM,KAAK,EAAE,QAAQ;oBACvB,MAAM,OAAO,MAAM,KAAK,CAAC,EAAE;oBAC3B,MAAM,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI;gBAC5C,oCAAoC;gBACtC;YACF;YACA,MAAM,KAAK;QACb;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAS;YAAW;SAAQ;QAC1C,oBAAM,6LAAC,2MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;QACrB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,MAAM,YAAY,OAAO;YACzB,+BAA+B;YAC/B,MAAM,UAAU,IAAI,OAClB;YAGF,IAAI,QAAQ,IAAI,CAAC,YAAY;gBAC3B,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,eAAe,CAAC;oBACf,KAAK;gBACP,GACC,GAAG;YACR,OAAO;gBACL,IAAI,cAAc,MAAM;oBACtB,MAAM;gBACR;YACF;QACF;IACF;CACD;AAEM,MAAM,eAAe,yJAAA,CAAA,UAAO,CAAC,SAAS,CAAC;IAC5C,YAAY;QACV,OAAO,IAAM;QACb,QAAQ,yJAAA,CAAA,cAAW;IACrB;AACF", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/editor/selectors/node-selector.tsx"], "sourcesContent": ["import {\n  Check,\n  CheckSquare,\n  Ch<PERSON>ronDown,\n  Code,\n  Heading1,\n  Heading2,\n  Heading3,\n  ListOrdered,\n  type LucideIcon,\n  TextIcon,\n  TextQuote,\n} from \"lucide-react\";\nimport { EditorBubbleItem, useEditor } from \"novel\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\nimport { Popover } from \"@radix-ui/react-popover\";\n\nexport type SelectorItem = {\n  name: string;\n  icon: LucideIcon;\n  command: (editor: ReturnType<typeof useEditor>[\"editor\"]) => void;\n  isActive: (editor: ReturnType<typeof useEditor>[\"editor\"]) => boolean;\n};\n\nconst items: SelectorItem[] = [\n  {\n    name: \"Text\",\n    icon: TextIcon,\n    command: (editor) =>\n      editor ? editor.chain().focus().clearNodes().run() : undefined,\n    // I feel like there has to be a more efficient way to do this – feel free to PR if you know how!\n    isActive: (editor) =>\n      !!editor &&\n      editor.isActive(\"paragraph\") &&\n      !editor.isActive(\"bulletList\") &&\n      !editor.isActive(\"orderedList\"),\n  },\n  {\n    name: \"Heading 1\",\n    icon: Heading1,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleHeading({ level: 1 }).run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"heading\", { level: 1 }) ?? false,\n  },\n  {\n    name: \"Heading 2\",\n    icon: Heading2,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleHeading({ level: 2 }).run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"heading\", { level: 2 }) ?? false,\n  },\n  {\n    name: \"Heading 3\",\n    icon: Heading3,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleHeading({ level: 3 }).run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"heading\", { level: 3 }) ?? false,\n  },\n  {\n    name: \"To-do List\",\n    icon: CheckSquare,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleTaskList().run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"taskItem\") ?? false,\n  },\n  {\n    name: \"Bullet List\",\n    icon: ListOrdered,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleBulletList().run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"bulletList\") ?? false,\n  },\n  {\n    name: \"Numbered List\",\n    icon: ListOrdered,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleOrderedList().run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"orderedList\") ?? false,\n  },\n  {\n    name: \"Quote\",\n    icon: TextQuote,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleBlockquote().run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"blockquote\") ?? false,\n  },\n  {\n    name: \"Code\",\n    icon: Code,\n    command: (editor) =>\n      editor\n        ? editor.chain().focus().clearNodes().toggleCodeBlock().run()\n        : undefined,\n    isActive: (editor) => editor?.isActive(\"codeBlock\") ?? false,\n  },\n];\ninterface NodeSelectorProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport const NodeSelector = ({ open, onOpenChange }: NodeSelectorProps) => {\n  const { editor } = useEditor();\n  if (!editor) return null;\n  const activeItem = items.filter((item) => item.isActive(editor)).pop() ?? {\n    name: \"Multiple\",\n  };\n\n  return (\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\n      <PopoverTrigger\n        asChild\n        className=\"gap-2 rounded-none border-none hover:bg-accent focus:ring-0\"\n      >\n        <Button size=\"sm\" variant=\"ghost\" className=\"gap-2\">\n          <span className=\"whitespace-nowrap text-sm\">{activeItem.name}</span>\n          <ChevronDown className=\"h-4 w-4\" />\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent sideOffset={5} align=\"start\" className=\"w-48 p-1\">\n        {items.map((item) => (\n          <EditorBubbleItem\n            key={item.name}\n            onSelect={(editor) => {\n              item.command(editor);\n              onOpenChange(false);\n            }}\n            className=\"flex cursor-pointer items-center justify-between rounded-sm px-2 py-1 text-sm hover:bg-accent\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"rounded-sm border p-1\">\n                <item.icon className=\"h-3 w-3\" />\n              </div>\n              <span>{item.name}</span>\n            </div>\n            {activeItem.name === item.name && <Check className=\"h-4 w-4\" />}\n          </EditorBubbleItem>\n        ))}\n      </PopoverContent>\n    </Popover>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAAA;AAEA;AACA;AACA;;;;;;;;AASA,MAAM,QAAwB;IAC5B;QACE,MAAM;QACN,MAAM,yMAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,SAAS,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG,KAAK;QACvD,iGAAiG;QACjG,UAAU,CAAC,SACT,CAAC,CAAC,UACF,OAAO,QAAQ,CAAC,gBAChB,CAAC,OAAO,QAAQ,CAAC,iBACjB,CAAC,OAAO,QAAQ,CAAC;IACrB;IACA;QACE,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG,KACnE;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,WAAW;gBAAE,OAAO;YAAE,MAAM;IACrE;IACA;QACE,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG,KACnE;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,WAAW;gBAAE,OAAO;YAAE,MAAM;IACrE;IACA;QACE,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG,KACnE;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,WAAW;gBAAE,OAAO;YAAE,MAAM;IACrE;IACA;QACE,MAAM;QACN,MAAM,8NAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,cAAc,GAAG,GAAG,KACxD;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,eAAe;IACxD;IACA;QACE,MAAM;QACN,MAAM,uNAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,gBAAgB,GAAG,GAAG,KAC1D;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,iBAAiB;IAC1D;IACA;QACE,MAAM;QACN,MAAM,uNAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,iBAAiB,GAAG,GAAG,KAC3D;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,kBAAkB;IAC3D;IACA;QACE,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;QACf,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,gBAAgB,GAAG,GAAG,KAC1D;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,iBAAiB;IAC1D;IACA;QACE,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,SAAS,CAAC,SACR,SACI,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,eAAe,GAAG,GAAG,KACzD;QACN,UAAU,CAAC,SAAW,QAAQ,SAAS,gBAAgB;IACzD;CACD;AAMM,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,YAAY,EAAqB;;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,CAAC,SAAS,GAAG,MAAM;QACxE,MAAM;IACR;IAEA,qBACE,6LAAC,sKAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,6LAAC,+HAAA,CAAA,iBAAc;gBACb,OAAO;gBACP,WAAU;0BAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,SAAQ;oBAAQ,WAAU;;sCAC1C,6LAAC;4BAAK,WAAU;sCAA6B,WAAW,IAAI;;;;;;sCAC5D,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG3B,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,YAAY;gBAAG,OAAM;gBAAQ,WAAU;0BACpD,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,yJAAA,CAAA,mBAAgB;wBAEf,UAAU,CAAC;4BACT,KAAK,OAAO,CAAC;4BACb,aAAa;wBACf;wBACA,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;;;;;;4BAEjB,WAAW,IAAI,KAAK,KAAK,IAAI,kBAAI,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;uBAb9C,KAAK,IAAI;;;;;;;;;;;;;;;;AAmB1B;GAxCa;;QACQ,sNAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/editor/selectors/link-selector.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\nimport { useEditor } from \"novel\";\r\nimport { Check, Trash } from \"lucide-react\";\r\nimport { type Dispatch, type SetStateAction, useEffect, useRef } from \"react\";\r\nimport { Popover, PopoverTrigger } from \"@radix-ui/react-popover\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { PopoverContent } from \"@/components/ui/popover\";\r\n\r\nexport function isValidUrl(url: string) {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (e) {\r\n    return false;\r\n  }\r\n}\r\nexport function getUrlFromString(str: string) {\r\n  if (isValidUrl(str)) return str;\r\n  try {\r\n    if (str.includes(\".\") && !str.includes(\" \")) {\r\n      return new URL(`https://${str}`).toString();\r\n    }\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n}\r\ninterface LinkSelectorProps {\r\n  open: boolean;\r\n  onOpenChange: Dispatch<SetStateAction<boolean>>;\r\n}\r\n\r\nexport const LinkSelector = ({ open, onOpenChange }: LinkSelectorProps) => {\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const { editor } = useEditor();\r\n\r\n  // Autofocus on input by default\r\n  useEffect(() => {\r\n    inputRef.current?.focus();\r\n  });\r\n  if (!editor) return null;\r\n\r\n  return (\r\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button variant=\"ghost\" className=\"gap-2 rounded-none border-none\">\r\n          <p className=\"text-base\">↗</p>\r\n          <p\r\n            className={cn(\"underline decoration-stone-400 underline-offset-4\", {\r\n              \"text-blue-500\": editor.isActive(\"link\"),\r\n            })}\r\n          >\r\n            Link\r\n          </p>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent align=\"start\" className=\"w-60 p-0\" sideOffset={10}>\r\n        <form\r\n          onSubmit={(e) => {\r\n            const target = e.currentTarget as HTMLFormElement;\r\n            e.preventDefault();\r\n            const input = target[0] as HTMLInputElement;\r\n            const url = getUrlFromString(input.value);\r\n            if (url) {\r\n              editor.chain().focus().setLink({ href: url }).run();\r\n            }\r\n          }}\r\n          className=\"flex  p-1 \"\r\n        >\r\n          <input\r\n            ref={inputRef}\r\n            type=\"text\"\r\n            placeholder=\"Paste a link\"\r\n            className=\"flex-1 bg-background p-1 text-sm outline-none\"\r\n            defaultValue={editor.getAttributes(\"link\").href || \"\"}\r\n          />\r\n          {editor.getAttributes(\"link\").href ? (\r\n            <Button\r\n              size=\"icon\"\r\n              variant=\"outline\"\r\n              type=\"button\"\r\n              className=\"flex h-8 items-center rounded-sm p-1 text-red-600 transition-all hover:bg-red-100 dark:hover:bg-red-800\"\r\n              onClick={() => {\r\n                editor.chain().focus().unsetLink().run();\r\n              }}\r\n            >\r\n              <Trash className=\"h-4 w-4\" />\r\n            </Button>\r\n          ) : (\r\n            <Button size=\"icon\" className=\"h-8\">\r\n              <Check className=\"h-4 w-4\" />\r\n            </Button>\r\n          )}\r\n        </form>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF;AACO,SAAS,iBAAiB,GAAW;IAC1C,IAAI,WAAW,MAAM,OAAO;IAC5B,IAAI;QACF,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM;YAC3C,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ;QAC3C;IACF,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF;AAMO,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,YAAY,EAAqB;;IACpE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAE3B,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,SAAS,OAAO,EAAE;QACpB;;IACA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,sKAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,6LAAC,sKAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;;sCAChC,6LAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,6LAAC;4BACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;gCACjE,iBAAiB,OAAO,QAAQ,CAAC;4BACnC;sCACD;;;;;;;;;;;;;;;;;0BAKL,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAM;gBAAQ,WAAU;gBAAW,YAAY;0BAC7D,cAAA,6LAAC;oBACC,UAAU,CAAC;wBACT,MAAM,SAAS,EAAE,aAAa;wBAC9B,EAAE,cAAc;wBAChB,MAAM,QAAQ,MAAM,CAAC,EAAE;wBACvB,MAAM,MAAM,iBAAiB,MAAM,KAAK;wBACxC,IAAI,KAAK;4BACP,OAAO,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC;gCAAE,MAAM;4BAAI,GAAG,GAAG;wBACnD;oBACF;oBACA,WAAU;;sCAEV,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,cAAc,OAAO,aAAa,CAAC,QAAQ,IAAI,IAAI;;;;;;wBAEpD,OAAO,aAAa,CAAC,QAAQ,IAAI,iBAChC,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,OAAO,KAAK,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG;4BACxC;sCAEA,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;iDAGnB,6LAAC,8HAAA,CAAA,SAAM;4BAAC,MAAK;4BAAO,WAAU;sCAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;GAjEa;;QAEQ,sNAAA,CAAA,YAAS;;;KAFjB", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/editor/selectors/text-buttons.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport { EditorBubbleItem, useEditor } from \"novel\";\nimport { BoldIcon, ItalicIcon, UnderlineIcon, StrikethroughIcon, CodeIcon } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { SelectorItem } from \"./node-selector\";\n\nexport const TextButtons = () => {\n  const { editor } = useEditor();\n  if (!editor) return null;\n  const items: SelectorItem[] = [\n    {\n      name: \"bold\",\n      isActive: (editor) => editor?.isActive(\"bold\") ?? false,\n      command: (editor) => editor?.chain().focus().toggleBold().run(),\n      icon: BoldIcon,\n    },\n    {\n      name: \"italic\",\n      isActive: (editor) => editor?.isActive(\"italic\") ?? false,\n      command: (editor) => editor?.chain().focus().toggleItalic().run(),\n      icon: ItalicIcon,\n    },\n    {\n      name: \"underline\",\n      isActive: (editor) => editor?.isActive(\"underline\") ?? false,\n      command: (editor) => editor?.chain().focus().toggleUnderline().run(),\n      icon: UnderlineIcon,\n    },\n    {\n      name: \"strike\",\n      isActive: (editor) => editor?.isActive(\"strike\") ?? false,\n      command: (editor) => editor?.chain().focus().toggleStrike().run(),\n      icon: StrikethroughIcon,\n    },\n    {\n      name: \"code\",\n      isActive: (editor) => editor?.isActive(\"code\") ?? false,\n      command: (editor) => editor?.chain().focus().toggleCode().run(),\n      icon: CodeIcon,\n    },\n  ];\n  return (\n    <div className='flex'>\n      {items.map((item, index) => (\n        <EditorBubbleItem\n          key={index}\n          onSelect={(editor) => {\n            item.command(editor);\n          }}>\n          <Button size='icon' className='rounded-none' variant='ghost'>\n            <item.icon\n              className={cn(\"h-4 w-4\", {\n                \"text-blue-500\": item.isActive(editor),\n              })}\n            />\n          </Button>\n        </EditorBubbleItem>\n      ))}\n    </div>\n  );\n};"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;AAGO,MAAM,cAAc;;IACzB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,QAAwB;QAC5B;YACE,MAAM;YACN,UAAU,CAAC,SAAW,QAAQ,SAAS,WAAW;YAClD,SAAS,CAAC,SAAW,QAAQ,QAAQ,QAAQ,aAAa;YAC1D,MAAM,yMAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,QAAQ,SAAS,aAAa;YACpD,SAAS,CAAC,SAAW,QAAQ,QAAQ,QAAQ,eAAe;YAC5D,MAAM,6MAAA,CAAA,aAAU;QAClB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,QAAQ,SAAS,gBAAgB;YACvD,SAAS,CAAC,SAAW,QAAQ,QAAQ,QAAQ,kBAAkB;YAC/D,MAAM,mNAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,QAAQ,SAAS,aAAa;YACpD,SAAS,CAAC,SAAW,QAAQ,QAAQ,QAAQ,eAAe;YAC5D,MAAM,2NAAA,CAAA,oBAAiB;QACzB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,QAAQ,SAAS,WAAW;YAClD,SAAS,CAAC,SAAW,QAAQ,QAAQ,QAAQ,aAAa;YAC1D,MAAM,yMAAA,CAAA,WAAQ;QAChB;KACD;IACD,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,yJAAA,CAAA,mBAAgB;gBAEf,UAAU,CAAC;oBACT,KAAK,OAAO,CAAC;gBACf;0BACA,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,MAAK;oBAAO,WAAU;oBAAe,SAAQ;8BACnD,cAAA,6LAAC,KAAK,IAAI;wBACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,WAAW;4BACvB,iBAAiB,KAAK,QAAQ,CAAC;wBACjC;;;;;;;;;;;eARC;;;;;;;;;;AAef;GAtDa;;QACQ,sNAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/editor/selectors/color-selector.tsx"], "sourcesContent": ["import { Check, ChevronDown } from \"lucide-react\";\nimport type { Dispatch, SetStateAction } from \"react\";\nimport { EditorBubbleItem, useEditor } from \"novel\";\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\nimport { But<PERSON> } from \"@/components/ui/button\";\n\nexport interface BubbleColorMenuItem {\n  name: string;\n  color: string;\n}\n\ninterface ColorSelectorProps {\n  open: boolean;\n  onOpenChange: Dispatch<SetStateAction<boolean>>;\n}\n\nconst TEXT_COLORS: BubbleColorMenuItem[] = [\n  {\n    name: \"Default\",\n    color: \"var(--novel-black)\",\n  },\n  {\n    name: \"Purple\",\n    color: \"#9333EA\",\n  },\n  {\n    name: \"Red\",\n    color: \"#E00000\",\n  },\n  {\n    name: \"Yellow\",\n    color: \"#EAB308\",\n  },\n  {\n    name: \"Blue\",\n    color: \"#2563EB\",\n  },\n  {\n    name: \"Green\",\n    color: \"#008A00\",\n  },\n  {\n    name: \"Orange\",\n    color: \"#FFA500\",\n  },\n  {\n    name: \"Pink\",\n    color: \"#BA4081\",\n  },\n  {\n    name: \"<PERSON>\",\n    color: \"#A8A29E\",\n  },\n];\n\nconst HIGHLIGHT_COLORS: BubbleColorMenuItem[] = [\n  {\n    name: \"Default\",\n    color: \"var(--novel-highlight-default)\",\n  },\n  {\n    name: \"Purple\",\n    color: \"var(--novel-highlight-purple)\",\n  },\n  {\n    name: \"Red\",\n    color: \"var(--novel-highlight-red)\",\n  },\n  {\n    name: \"Yellow\",\n    color: \"var(--novel-highlight-yellow)\",\n  },\n  {\n    name: \"Blue\",\n    color: \"var(--novel-highlight-blue)\",\n  },\n  {\n    name: \"Green\",\n    color: \"var(--novel-highlight-green)\",\n  },\n  {\n    name: \"Orange\",\n    color: \"var(--novel-highlight-orange)\",\n  },\n  {\n    name: \"Pink\",\n    color: \"var(--novel-highlight-pink)\",\n  },\n  {\n    name: \"Gray\",\n    color: \"var(--novel-highlight-gray)\",\n  },\n];\n\nexport const ColorSelector = ({ open, onOpenChange }: ColorSelectorProps) => {\n  const { editor } = useEditor();\n\n  if (!editor) return null;\n  const activeColorItem = TEXT_COLORS.find(({ color }) => editor.isActive(\"textStyle\", { color }));\n\n  const activeHighlightItem = HIGHLIGHT_COLORS.find(({ color }) =>\n    editor.isActive(\"highlight\", { color })\n  );\n\n  return (\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\n      <PopoverTrigger asChild>\n        <Button className='gap-2 rounded-none' variant='ghost'>\n          <span\n            className='rounded-sm px-1'\n            style={{\n              color: activeColorItem?.color,\n              backgroundColor: activeHighlightItem?.color,\n            }}>\n            A\n          </span>\n          <ChevronDown className='h-4 w-4' />\n        </Button>\n      </PopoverTrigger>\n\n      <PopoverContent\n        sideOffset={5}\n        className='my-1 flex max-h-80 w-48 flex-col overflow-hidden overflow-y-auto rounded border p-1 shadow-xl '\n        align='start'>\n        <div className='flex flex-col'>\n          <div className='my-1 px-2 text-sm font-semibold text-muted-foreground'>Color</div>\n          {TEXT_COLORS.map(({ name, color }, index) => (\n            <EditorBubbleItem\n              key={index}\n              onSelect={() => {\n                editor.commands.unsetColor();\n                if (name !== \"Default\") {\n                  editor\n                    .chain()\n                    .focus()\n                    .setColor(color || \"\")\n                    .run();\n                }\n              }}\n              className='flex cursor-pointer items-center justify-between px-2 py-1 text-sm hover:bg-accent'>\n              <div className='flex items-center gap-2'>\n                <div className='rounded-sm border px-2 py-px font-medium' style={{ color }}>\n                  A\n                </div>\n                <span>{name}</span>\n              </div>\n            </EditorBubbleItem>\n          ))}\n        </div>\n        <div>\n          <div className='my-1 px-2 text-sm font-semibold text-muted-foreground'>Background</div>\n          {HIGHLIGHT_COLORS.map(({ name, color }, index) => (\n            <EditorBubbleItem\n              key={index}\n              onSelect={() => {\n                editor.commands.unsetHighlight();\n                if (name !== \"Default\") {\n                  editor.commands.setHighlight({ color });\n                }\n              }}\n              className='flex cursor-pointer items-center justify-between px-2 py-1 text-sm hover:bg-accent'>\n              <div className='flex items-center gap-2'>\n                <div\n                  className='rounded-sm border px-2 py-px font-medium'\n                  style={{ backgroundColor: color }}>\n                  A\n                </div>\n                <span>{name}</span>\n              </div>\n              {editor.isActive(\"highlight\", { color }) && <Check className='h-4 w-4' />}\n            </EditorBubbleItem>\n          ))}\n        </div>\n      </PopoverContent>\n    </Popover>\n  );\n};"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;AAAA;AACA;AACA;;;;;;;AAYA,MAAM,cAAqC;IACzC;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,mBAA0C;IAC9C;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAEM,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAsB;;IACtE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAE3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,kBAAkB,YAAY,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,OAAO,QAAQ,CAAC,aAAa;YAAE;QAAM;IAE7F,MAAM,sBAAsB,iBAAiB,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAC1D,OAAO,QAAQ,CAAC,aAAa;YAAE;QAAM;IAGvC,qBACE,6LAAC,+HAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,WAAU;oBAAqB,SAAQ;;sCAC7C,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,iBAAiB;gCACxB,iBAAiB,qBAAqB;4BACxC;sCAAG;;;;;;sCAGL,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI3B,6LAAC,+HAAA,CAAA,iBAAc;gBACb,YAAY;gBACZ,WAAU;gBACV,OAAM;;kCACN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAwD;;;;;;4BACtE,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,sBACjC,6LAAC,yJAAA,CAAA,mBAAgB;oCAEf,UAAU;wCACR,OAAO,QAAQ,CAAC,UAAU;wCAC1B,IAAI,SAAS,WAAW;4CACtB,OACG,KAAK,GACL,KAAK,GACL,QAAQ,CAAC,SAAS,IAClB,GAAG;wCACR;oCACF;oCACA,WAAU;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAA2C,OAAO;oDAAE;gDAAM;0DAAG;;;;;;0DAG5E,6LAAC;0DAAM;;;;;;;;;;;;mCAhBJ;;;;;;;;;;;kCAqBX,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;0CAAwD;;;;;;4BACtE,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,sBACtC,6LAAC,yJAAA,CAAA,mBAAgB;oCAEf,UAAU;wCACR,OAAO,QAAQ,CAAC,cAAc;wCAC9B,IAAI,SAAS,WAAW;4CACtB,OAAO,QAAQ,CAAC,YAAY,CAAC;gDAAE;4CAAM;wCACvC;oCACF;oCACA,WAAU;;sDACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAM;8DAAG;;;;;;8DAGrC,6LAAC;8DAAM;;;;;;;;;;;;wCAER,OAAO,QAAQ,CAAC,aAAa;4CAAE;wCAAM,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;mCAhBxD;;;;;;;;;;;;;;;;;;;;;;;AAuBnB;GAlFa;;QACQ,sNAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/components/editor/NovelEditor.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  Color,\r\n  EditorB<PERSON><PERSON>,\r\n  Editor<PERSON><PERSON>nt,\r\n  Editor<PERSON><PERSON>ance,\r\n  EditorRoot,\r\n  handleCommandNavigation,\r\n  HighlightExtension,\r\n  JSONContent,\r\n  TextStyle,\r\n  TiptapImage,\r\n  TiptapUnderline,\r\n  Youtube,\r\n} from \"novel\";\r\nimport React from \"react\";\r\n\r\nimport { defaultExtensions } from \"./extentions\";\r\nimport { slashCommand, suggestionItems } from \"./slash-command\";\r\nimport {\r\n  EditorCommand,\r\n  EditorCommandEmpty,\r\n  EditorCommandItem,\r\n  EditorCommandList,\r\n} from \"novel\";\r\nimport { EditorView } from \"prosemirror-view\";\r\nimport { NodeSelector } from \"./selectors/node-selector\";\r\nimport { LinkSelector } from \"./selectors/link-selector\";\r\nimport { TextButtons } from \"./selectors/text-buttons\";\r\nimport { ColorSelector } from \"./selectors/color-selector\";\r\n\r\nconst extensions = [\r\n  ...defaultExtensions,\r\n  slashCommand,\r\n  Color,\r\n  TextStyle,\r\n  HighlightExtension,\r\n  TiptapUnderline,\r\n  TiptapImage,\r\n  Youtube,\r\n];\r\n\r\ninterface NovelEditorProps {\r\n  data: JSONContent;\r\n  setData: (json: JSONContent) => void;\r\n}\r\n\r\nconst NovelEditor: React.FC<NovelEditorProps> = ({ data, setData }) => {\r\n  const [openNode, setOpenNode] = React.useState(false);\r\n  const [openLink, setOpenLink] = React.useState(false);\r\n  const [openColor, setOpenColor] = React.useState(false);\r\n  const [openAI, setOpenAI] = React.useState(false);\r\n\r\n  return (\r\n    <EditorRoot>\r\n      <EditorContent\r\n        initialContent={data}\r\n        onUpdate={({ editor }: { editor: EditorInstance }) => {\r\n          const json = editor.getJSON();\r\n          setData(json);\r\n        }}\r\n        extensions={extensions}\r\n        className=\"p-4 min-h-[200px] prose max-w-none focus:outline-none [&_.ProseMirror]:min-h-[150px] [&_.ProseMirror]:focus:outline-none\"\r\n        editorProps={{\r\n          handleDOMEvents: {\r\n            keydown: (_view: EditorView, event: KeyboardEvent) =>\r\n              handleCommandNavigation(event),\r\n          },\r\n          attributes: {\r\n            class:\r\n              \"prose prose-lg dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full\",\r\n          },\r\n        }}\r\n      >\r\n        <EditorBubble\r\n          tippyOptions={{\r\n            placement: openAI ? \"bottom-start\" : \"top\",\r\n          }}\r\n          className=\"flex w-fit max-w-[90vw] overflow-hidden rounded border border-muted bg-background shadow-xl\"\r\n        >\r\n          <NodeSelector open={openNode} onOpenChange={setOpenNode} />\r\n          <LinkSelector open={openLink} onOpenChange={setOpenLink} />\r\n          <TextButtons />\r\n          <ColorSelector open={openColor} onOpenChange={setOpenColor} />\r\n        </EditorBubble>\r\n        <EditorCommand className=\"z-50 h-auto max-h-[330px]  w-72 overflow-y-auto rounded-md border border-muted bg-background px-1 py-2 shadow-md transition-all\">\r\n          <EditorCommandEmpty className=\"px-2 text-muted-foreground\">\r\n            No results\r\n          </EditorCommandEmpty>\r\n          <EditorCommandList>\r\n            {suggestionItems.map((item) => (\r\n              <EditorCommandItem\r\n                value={item.title}\r\n                onCommand={({\r\n                  editor,\r\n                  range,\r\n                }: {\r\n                  editor: EditorInstance;\r\n                  range: { from: number; to: number };\r\n                }) => {\r\n                  if (item.command) {\r\n                    item.command({ editor, range });\r\n                  }\r\n                }}\r\n                className=\"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-accent aria-selected:bg-accent\"\r\n                key={item.title}\r\n              >\r\n                <div className=\"flex h-10 w-10 items-center justify-center rounded-md border border-muted bg-background\">\r\n                  {item.icon}\r\n                </div>\r\n                <div>\r\n                  <p className=\"font-medium\">{item.title}</p>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    {item.description}\r\n                  </p>\r\n                </div>\r\n              </EditorCommandItem>\r\n            ))}\r\n          </EditorCommandList>\r\n        </EditorCommand>\r\n      </EditorContent>\r\n    </EditorRoot>\r\n  );\r\n};\r\n\r\nexport default NovelEditor;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;AA9BA;;;;;;;;;;AAgCA,MAAM,aAAa;OACd,qIAAA,CAAA,oBAAiB;IACpB,4IAAA,CAAA,eAAY;IACZ,kKAAA,CAAA,QAAK;IACL,kNAAA,CAAA,YAAS;IACT,yJAAA,CAAA,qBAAkB;IAClB,oNAAA,CAAA,kBAAe;IACf,4MAAA,CAAA,cAAW;IACX,0MAAA,CAAA,UAAO;CACR;AAOD,MAAM,cAA0C,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE3C,qBACE,6LAAC,yJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC,yJAAA,CAAA,gBAAa;YACZ,gBAAgB;YAChB,UAAU,CAAC,EAAE,MAAM,EAA8B;gBAC/C,MAAM,OAAO,OAAO,OAAO;gBAC3B,QAAQ;YACV;YACA,YAAY;YACZ,WAAU;YACV,aAAa;gBACX,iBAAiB;oBACf,SAAS,CAAC,OAAmB,QAC3B,CAAA,GAAA,yJAAA,CAAA,0BAAuB,AAAD,EAAE;gBAC5B;gBACA,YAAY;oBACV,OACE;gBACJ;YACF;;8BAEA,6LAAC,yJAAA,CAAA,eAAY;oBACX,cAAc;wBACZ,WAAW,SAAS,iBAAiB;oBACvC;oBACA,WAAU;;sCAEV,6LAAC,yJAAA,CAAA,eAAY;4BAAC,MAAM;4BAAU,cAAc;;;;;;sCAC5C,6LAAC,yJAAA,CAAA,eAAY;4BAAC,MAAM;4BAAU,cAAc;;;;;;sCAC5C,6LAAC,wJAAA,CAAA,cAAW;;;;;sCACZ,6LAAC,0JAAA,CAAA,gBAAa;4BAAC,MAAM;4BAAW,cAAc;;;;;;;;;;;;8BAEhD,6LAAC,yJAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,yJAAA,CAAA,qBAAkB;4BAAC,WAAU;sCAA6B;;;;;;sCAG3D,6LAAC,yJAAA,CAAA,oBAAiB;sCACf,4IAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC,yJAAA,CAAA,oBAAiB;oCAChB,OAAO,KAAK,KAAK;oCACjB,WAAW,CAAC,EACV,MAAM,EACN,KAAK,EAIN;wCACC,IAAI,KAAK,OAAO,EAAE;4CAChB,KAAK,OAAO,CAAC;gDAAE;gDAAQ;4CAAM;wCAC/B;oCACF;oCACA,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;sDAEZ,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAe,KAAK,KAAK;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;;mCARhB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB/B;GA5EM;KAAA;uCA8ES", "debugId": null}}, {"offset": {"line": 1971, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/React-Native/PhysioPrep/physioprep/app/dashboard/topics/components/TopicForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useActionState, useEffect, useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Switch } from \"@/components/ui/switch\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\n\nimport {\n  createTopic,\n  updateTopic,\n  TopicFormState,\n  getSubjectsForDropdown,\n} from \"../actions\";\nimport { Topic, Subject } from \"@/types/types\";\nimport { ArrowLeft } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { toast } from \"sonner\";\n\nimport NovelEditor from \"@/components/editor/NovelEditor\";\n\ninterface TopicFormProps {\n  topic?: Topic;\n  mode: \"create\" | \"edit\";\n}\n\nexport function TopicForm({ topic, mode }: TopicFormProps) {\n  const router = useRouter();\n  const isEdit = mode === \"edit\";\n  const [subjects, setSubjects] = useState<Subject[]>([]);\n  const [description, setDescription] = useState<JSONContent>(\n    topic?.description\n      ? typeof topic.description === \"string\"\n        ? JSON.parse(topic.description)\n        : topic.description\n      : {\n          type: \"doc\",\n          content: [\n            {\n              type: \"paragraph\",\n              content: [],\n            },\n          ],\n        }\n  );\n  const [loadingSubjects, setLoadingSubjects] = useState(true);\n  const [isActive, setIsActive] = useState(topic?.isActive ?? true);\n  const [isPremium, setIsPremium] = useState(topic?.isPremium ?? false);\n\n  const action =\n    isEdit && topic ? updateTopic.bind(null, topic._id) : createTopic;\n\n  const [state, formAction, isPending] = useActionState<\n    TopicFormState,\n    FormData\n  >(action, { error: null, success: false });\n\n  // Fetch subjects for dropdown\n  useEffect(() => {\n    async function fetchSubjects() {\n      try {\n        const result = await getSubjectsForDropdown();\n        console.log(result);\n        if (result.success) {\n          setSubjects(result.data);\n        } else {\n          toast.error(result.errors[0]?.msg);\n        }\n      } catch {\n        toast.error(\"Failed to load subjects\");\n      } finally {\n        setLoadingSubjects(false);\n      }\n    }\n\n    fetchSubjects();\n  }, []);\n\n  // Handle form success\n  useEffect(() => {\n    if (state?.success) {\n      toast.success(\n        isEdit ? \"Topic updated successfully!\" : \"Topic created successfully!\"\n      );\n\n      // Navigate back to topics list\n      router.push(\"/dashboard/topics\");\n    }\n  }, [state?.success, state?.data, isEdit, router]);\n\n  const handleSubmit = (formData: FormData) => {\n    // Convert the editor state to JSON string\n    const descriptionJson = JSON.stringify(description);\n    formData.set(\"description\", descriptionJson);\n\n    // Add switch states to form data\n    formData.set(\"isActive\", isActive ? \"on\" : \"off\");\n    formData.set(\"isPremium\", isPremium ? \"on\" : \"off\");\n\n    formAction(formData);\n  };\n\n  return (\n    <div className=\"flex-1 space-y-4 p-4 md:p-8 pt-6\">\n      <div className=\"flex items-center space-x-2\">\n        <Button variant=\"ghost\" size=\"sm\" asChild>\n          <Link href=\"/dashboard/topics\">\n            <ArrowLeft className=\"mr-2 h-4 w-4\" />\n            Back to Topics\n          </Link>\n        </Button>\n      </div>\n\n      <div>\n        <h2 className=\"text-3xl font-bold tracking-tight\">\n          {isEdit ? \"Edit Topic\" : \"Create Topic\"}\n        </h2>\n        <p className=\"text-muted-foreground\">\n          {isEdit\n            ? \"Update the topic information below.\"\n            : \"Add a new topic to the system.\"}\n        </p>\n      </div>\n\n      <Card className=\"max-w-6xl\">\n        <CardHeader>\n          <CardTitle>{isEdit ? \"Edit Topic\" : \"Create New Topic\"}</CardTitle>\n          <CardDescription>\n            {isEdit\n              ? \"Make changes to the topic information.\"\n              : \"Fill in the details to create a new topic.\"}\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {state?.error && (\n            <div className=\"mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md\">\n              {state.error}\n            </div>\n          )}\n\n          <form action={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"topicName\">Topic Name *</Label>\n                <Input\n                  id=\"topicName\"\n                  name=\"topicName\"\n                  placeholder=\"Enter topic name\"\n                  defaultValue={topic?.topicName || \"\"}\n                  required\n                  disabled={isPending}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"subject\">Subject *</Label>\n                <Select\n                  name=\"subject\"\n                  defaultValue={\n                    typeof topic?.subject === \"object\"\n                      ? topic.subject._id\n                      : topic?.subject || \"\"\n                  }\n                  disabled={loadingSubjects}\n                  required\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a subject\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {subjects.map((subject) => (\n                      <SelectItem key={subject._id} value={subject._id}>\n                        {subject.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Description</Label>\n              <div className=\"border rounded-md min-h-[200px]\">\n                <NovelEditor data={description} setData={setDescription} />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"isActive\"\n                  checked={isActive}\n                  onCheckedChange={setIsActive}\n                  disabled={isPending}\n                />\n                <Label htmlFor=\"isActive\">Active</Label>\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"isPremium\"\n                  checked={isPremium}\n                  onCheckedChange={setIsPremium}\n                  disabled={isPending}\n                />\n                <Label htmlFor=\"isPremium\">Premium Tier</Label>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2 pt-4\">\n              <Button type=\"submit\" disabled={isPending || loadingSubjects}>\n                {isPending\n                  ? isEdit\n                    ? \"Updating...\"\n                    : \"Creating...\"\n                  : isEdit\n                  ? \"Update Topic\"\n                  : \"Create Topic\"}\n              </Button>\n              <Button variant=\"outline\" asChild disabled={isPending}>\n                <Link href=\"/dashboard/topics\">Cancel</Link>\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAOA;AACA;AACA;AAEA;;;AAlCA;;;;;;;;;;;;;;AAyCO,SAAS,UAAU,EAAE,KAAK,EAAE,IAAI,EAAkB;;IACvD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,SAAS;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3C,OAAO,cACH,OAAO,MAAM,WAAW,KAAK,WAC3B,KAAK,KAAK,CAAC,MAAM,WAAW,IAC5B,MAAM,WAAW,GACnB;QACE,MAAM;QACN,SAAS;YACP;gBACE,MAAM;gBACN,SAAS,EAAE;YACb;SACD;IACH;IAEN,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,YAAY;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,aAAa;IAE/D,MAAM,SACJ,UAAU,QAAQ,qKAAA,CAAA,cAAW,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,qKAAA,CAAA,cAAW;IAEnE,MAAM,CAAC,OAAO,YAAY,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAGlD,QAAQ;QAAE,OAAO;QAAM,SAAS;IAAM;IAExC,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,eAAe;gBACb,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;oBAC1C,QAAQ,GAAG,CAAC;oBACZ,IAAI,OAAO,OAAO,EAAE;wBAClB,YAAY,OAAO,IAAI;oBACzB,OAAO;wBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,CAAC,EAAE,EAAE;oBAChC;gBACF,EAAE,OAAM;oBACN,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,SAAU;oBACR,mBAAmB;gBACrB;YACF;YAEA;QACF;8BAAG,EAAE;IAEL,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO,SAAS;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CACX,SAAS,gCAAgC;gBAG3C,+BAA+B;gBAC/B,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC,OAAO;QAAS,OAAO;QAAM;QAAQ;KAAO;IAEhD,MAAM,eAAe,CAAC;QACpB,0CAA0C;QAC1C,MAAM,kBAAkB,KAAK,SAAS,CAAC;QACvC,SAAS,GAAG,CAAC,eAAe;QAE5B,iCAAiC;QACjC,SAAS,GAAG,CAAC,YAAY,WAAW,OAAO;QAC3C,SAAS,GAAG,CAAC,aAAa,YAAY,OAAO;QAE7C,WAAW;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,OAAO;8BACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;;0CACT,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;0BAM5C,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCACX,SAAS,eAAe;;;;;;kCAE3B,6LAAC;wBAAE,WAAU;kCACV,SACG,wCACA;;;;;;;;;;;;0BAIR,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC,4HAAA,CAAA,YAAS;0CAAE,SAAS,eAAe;;;;;;0CACpC,6LAAC,4HAAA,CAAA,kBAAe;0CACb,SACG,2CACA;;;;;;;;;;;;kCAGR,6LAAC,4HAAA,CAAA,cAAW;;4BACT,OAAO,uBACN,6LAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK;;;;;;0CAIhB,6LAAC;gCAAK,QAAQ;gCAAc,WAAU;;kDACpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,cAAc,OAAO,aAAa;wDAClC,QAAQ;wDACR,UAAU;;;;;;;;;;;;0DAId,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,6LAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,cACE,OAAO,OAAO,YAAY,WACtB,MAAM,OAAO,CAAC,GAAG,GACjB,OAAO,WAAW;wDAExB,UAAU;wDACV,QAAQ;;0EAER,6LAAC,8HAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,8HAAA,CAAA,gBAAa;0EACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,8HAAA,CAAA,aAAU;wEAAmB,OAAO,QAAQ,GAAG;kFAC7C,QAAQ,IAAI;uEADE,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAStC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uIAAA,CAAA,UAAW;oDAAC,MAAM;oDAAa,SAAS;;;;;;;;;;;;;;;;;kDAI7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS;wDACT,iBAAiB;wDACjB,UAAU;;;;;;kEAEZ,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;;;;;;;0DAG5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS;wDACT,iBAAiB;wDACjB,UAAU;;;;;;kEAEZ,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;;;;;;;;;;;;;kDAI/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,UAAU,aAAa;0DAC1C,YACG,SACE,gBACA,gBACF,SACA,iBACA;;;;;;0DAEN,6LAAC,8HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,OAAO;gDAAC,UAAU;0DAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GA1MgB;;QACC,qIAAA,CAAA,YAAS;QAyBe,6JAAA,CAAA,iBAAc;;;KA1BvC", "debugId": null}}]}