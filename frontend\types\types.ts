export interface Category {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
}

export interface SubjectStats {
  totalQuestions: number;
  freeQuestions: number;
  premiumQuestions: number;
  totalQuizzes: number;
  totalTests: number;
}

export interface QuestionDistribution {
  free: number;
  premium: number;
}

export interface Topic {
  _id: string;
  topicName: string;
  description: string;
  subject: Subject; // subject _id
  isActive: boolean;
  isPremium: boolean;
  createdAt: string;
  updatedAt: string;
  questionCount?: number; // for UI
}

export interface Subject {
  id: string;
  _id: string;
  name: string;
  description: string;
  slug: string;
  icon: string;
  color: string;
  isActive: boolean;
  order: number;
  categories: Category[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  stats: SubjectStats;
  questionDistribution: QuestionDistribution;
  topics: Topic[]; // populated topics
  __v: number;
}

export interface Quiz {
  _id: string;
  user: string;
  subject: string;
  topic?: string;
  mode: 'subject-quiz' | 'topic-quiz';
  totalQuestions: number;
  questions: QuizQuestion[];
  status: 'pending' | 'in_progress' | 'completed' | 'abandoned';
  score: number;
  startedAt?: string;
  completedAt?: string;
}

export interface QuizQuestion {
  question: Question;
  userAnswer?: number;
  isCorrect?: boolean;
  timeSpent?: number;
  answeredAt?: string;
}

export interface Test {
  _id: string;
  user: string;
  subject?: string;
  topic?: string;
  mode: 'subject-test' | 'topic-test' | 'mixed-test';
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  totalQuestions: number;
  questions: TestQuestion[];
  status: 'pending' | 'in_progress' | 'completed' | 'abandoned';
  score: number;
  startedAt?: string;
  completedAt?: string;
  timeLimit: number;
}

export interface TestQuestion {
  question: Question;
  userAnswer?: number;
  isCorrect?: boolean;
  timeSpent?: number;
}

export interface Question {
  _id: string;
  text: string;
  options: { text: string; isCorrect: boolean }[];
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tier: 'free' | 'premium';
  topic: Topic; // topic _id
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export type TopicFormValues = {
  topicName: string;
  description: string;
  isActive?: boolean;
};

export interface QuestionFormValues {
  text: string;
  options: {
    text: string;
    isCorrect: boolean;
  }[];
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tier: 'free' | 'premium';
}
