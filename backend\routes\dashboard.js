const express = require('express');
const router = express.Router();

// Import controllers
const {
  getDashboardStats,
  getRecentActivity,
  getUpcomingQuizzes,
  getProgressReport,
  getPerformanceMetrics,
  getLearningPath
} = require('../controllers/dashboardController');

// Import middleware
const { auth } = require('../middleware/auth');

// @route   GET /api/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private
router.get('/stats', auth, getDashboardStats);

// @route   GET /api/dashboard/activity
// @desc    Get recent activity
// @access  Private
router.get('/activity', auth, getRecentActivity);

// @route   GET /api/dashboard/upcoming-quizzes
// @desc    Get upcoming quizzes
// @access  Private
router.get('/upcoming-quizzes', auth, getUpcomingQuizzes);

// @route   GET /api/dashboard/progress
// @desc    Get progress report
// @access  Private
router.get('/progress', auth, getProgressReport);

// @route   GET /api/dashboard/performance
// @desc    Get performance metrics
// @access  Private
router.get('/performance', auth, getPerformanceMetrics);

// @route   GET /api/dashboard/learning-path
// @desc    Get personalized learning path
// @access  Private
router.get('/learning-path', auth, getLearningPath);

module.exports = router; 