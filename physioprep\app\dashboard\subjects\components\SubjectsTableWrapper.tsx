import { getSubjects } from "../actions";
import { SubjectsTable } from "./SubjectsTable";

export async function SubjectsTableWrapper() {
  const subjectsResult = await getSubjects();
  const subjects = subjectsResult.success ? subjectsResult.data.subjects : [];

  if (!subjectsResult.success) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-muted-foreground">
            {subjectsResult.errors[0]?.msg || "Failed to load subjects"}
          </p>
        </div>
      </div>
    );
  }

  return <SubjectsTable subjects={subjects} />;
}
