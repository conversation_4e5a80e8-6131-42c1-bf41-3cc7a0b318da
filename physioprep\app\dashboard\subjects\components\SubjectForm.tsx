"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { createSubject, updateSubject, SubjectFormState } from "../actions";
import { Subject } from "@/types/types";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface SubjectFormProps {
  subject?: Subject;
  mode: "create" | "edit";
}

export function SubjectForm({ subject, mode }: SubjectFormProps) {
  const isEdit = mode === "edit";
  const action = isEdit && subject 
    ? updateSubject.bind(null, subject._id)
    : createSubject;

  const [state, formAction, isPending] = useActionState<SubjectFormState, FormData>(
    action,
    { error: null, success: false }
  );

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/subjects">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Subjects
          </Link>
        </Button>
      </div>

      <div>
        <h2 className="text-3xl font-bold tracking-tight">
          {isEdit ? "Edit Subject" : "Create Subject"}
        </h2>
        <p className="text-muted-foreground">
          {isEdit 
            ? "Update the subject information below." 
            : "Add a new subject to the system."
          }
        </p>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>{isEdit ? "Edit Subject" : "Create New Subject"}</CardTitle>
          <CardDescription>
            {isEdit 
              ? "Make changes to the subject information." 
              : "Fill in the details to create a new subject."
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {state?.error && (
            <div className="mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {state.error}
            </div>
          )}

          <form action={formAction} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter subject name"
                  defaultValue={subject?.name || ""}
                  required
                  disabled={isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="order">Order</Label>
                <Input
                  id="order"
                  name="order"
                  type="number"
                  placeholder="0"
                  defaultValue={subject?.order?.toString() || ""}
                  disabled={isPending}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter subject description"
                defaultValue={subject?.description || ""}
                required
                disabled={isPending}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="icon">Icon</Label>
                <Input
                  id="icon"
                  name="icon"
                  placeholder="Icon name or URL"
                  defaultValue={subject?.icon || ""}
                  disabled={isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="color">Color</Label>
                <div className="flex space-x-2">
                  <Input
                    id="color"
                    name="color"
                    type="color"
                    defaultValue={subject?.color || "#3b82f6"}
                    disabled={isPending}
                    className="w-16"
                  />
                  <Input
                    placeholder="#3b82f6"
                    defaultValue={subject?.color || ""}
                    disabled={isPending}
                    className="flex-1"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                name="isActive"
                defaultChecked={subject?.isActive ?? true}
                disabled={isPending}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>

            <div className="flex items-center space-x-2 pt-4">
              <Button type="submit" disabled={isPending}>
                {isPending 
                  ? (isEdit ? "Updating..." : "Creating...") 
                  : (isEdit ? "Update Subject" : "Create Subject")
                }
              </Button>
              <Button variant="outline" asChild disabled={isPending}>
                <Link href="/dashboard/subjects">Cancel</Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
