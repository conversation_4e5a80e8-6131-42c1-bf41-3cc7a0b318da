const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const bodyParser = require("body-parser");
const dotenv = require("dotenv");
const cron = require("node-cron");

// Load environment variables
dotenv.config();

// Check if running in production
if (process.env.NODE_ENV === "production") {
  console.log("🔒 Running in production mode");
} else {
  console.log("🔧 Running in development mode");
}
// Ensure required environment variables are set
if (!process.env.JWT_SECRET) {
  console.error("❌ JWT_SECRET environment variable is not set");
  process.exit(1);
}
if (!process.env.MONGODB_URI) {
  console.error("❌ MONGODB_URI environment variable is not set");
  process.exit(1);
}
// if (!process.env.FRONTEND_URL) {
//   console.error("❌ FRONTEND_URL environment variable is not set");
//   process.exit(1);
// }
// if (!process.env.DAILY_QUESTION_CRON) {
//   console.error("❌ DAILY_QUESTION_CRON environment variable is not set");
//   process.exit(1);
// }

// Import routes
const authRoutes = require("./routes/auth");
const adminAuthRoutes = require("./routes/adminAuth");
const userRoutes = require("./routes/users");
const subjectRoutes = require("./routes/subjects");
const questionRoutes = require("./routes/questions");
const quizRoutes = require("./routes/quizzes");
const testRoutes = require("./routes/tests");
const dailyQuestionRoutes = require("./routes/dailyQuestions");
const topicsRoutes = require("./routes/topics");

// Import middleware
const { errorHandler, notFound } = require("./middleware/errorHandler");

// Import utilities
const { updateDailyQuestion } = require("./utils/dailyQuestionManager");

const app = express();

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log("\n=== New Request ===");
  console.log(`Timestamp: ${timestamp}`);
  console.log(`Method: ${req.method}`);
  console.log(`URL: ${req.originalUrl}`);
  console.log("Headers:", JSON.stringify(req.headers, null, 2));
  console.log("Body:", JSON.stringify(req.body, null, 2));
  console.log("==================\n");
  next();
});

// Middleware
app.use(
  cors({
    // For production, your FRONTEND_URL should be the deployed URL of your Expo app's web build
    // (e.g., "https://your-app-name.vercel.app" or your custom domain).
    // For Expo Go, preview APKs, or development on physical devices, allow all origins (origin: true)
    // so devices with changing IPs can access the backend.
    // WARNING: Allowing all origins is insecure for production!

    origin: process.env.NODE_ENV === "production"
      ? [process.env.FRONTEND_URL] // e.g., "https://your-app-name.vercel.app"
      : true,
    credentials: true,
  })
);

app.use(bodyParser.json({ limit: "10mb" }));
app.use(bodyParser.urlencoded({ extended: true, limit: "10mb" }));

// Static files for uploads
app.use("/uploads", express.static("uploads"));

// Database connection
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/physioprep")
  .then(() => {
    console.log("✅ Connected to MongoDB");
  })
  .catch((error) => {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  });

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/auth/admin", adminAuthRoutes);
app.use("/api/users", userRoutes);
app.use("/api/subjects", subjectRoutes);
app.use("/api/questions", questionRoutes);
app.use("/api/quizzes", quizRoutes);
app.use("/api/tests", testRoutes);
app.use("/api/daily-questions", dailyQuestionRoutes);
app.use("/api/topics", topicsRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.status(200).json({
    success: true,
    message: "PhysioPrep API is running",
    timestamp: new Date().toISOString(),
  });
});

// Error handling middleware (should be last)
app.use(errorHandler);

// 404 handler
app.use(notFound);

// Daily question cron job
const cronExpression = process.env.DAILY_QUESTION_CRON || "0 0 * * *"; // Daily at midnight
cron.schedule(cronExpression, () => {
  console.log("🔄 Running daily question update...");
  updateDailyQuestion();
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 PhysioPrep API Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(
    `🌐 CORS enabled for: ${
      process.env.FRONTEND_URL || "http://localhost:8081"
    }`
  );
});

module.exports = app;
