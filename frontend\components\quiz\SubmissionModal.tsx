import { View, Text, Modal } from 'react-native';
import { But<PERSON> } from 'components/ui/button';
import { Ionicons } from '@expo/vector-icons';

export default function SubmissionModal({
  visible,
  onClose,
  score,
  total,
  time,
}: {
  visible: boolean;
  onClose: () => void;
  score: number;
  total: number;
  time: number;
}) {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View className="flex-1 items-center justify-center bg-black/40">
        <View className="w-11/12 max-w-md items-center rounded-2xl bg-white p-6">
          <Ionicons name="trophy-outline" size={48} color="#f59e42" className="mb-2" />
          <Text className="mb-2 text-2xl font-bold text-primary">Quiz Complete!</Text>
          <Text className="mb-1 text-lg">
            Score: <Text className="font-bold">{score}/{total}</Text>
          </Text>
          <Text className="mb-1 text-lg">
            Time: <Text className="font-bold">{Math.floor(time / 60)}:{(time % 60).toString().padStart(2, '0')}</Text>
          </Text>
          <Button title="Review Answers" onPress={onClose} className="mt-4 w-full" />
        </View>
      </View>
    </Modal>
  );
} 