const { validationResult } = require("express-validator");
const Test = require("../models/Test");
const Question = require("../models/Question");
const User = require("../models/User");
const Subject = require("../models/Subject");
const Topic = require("../models/Topic");

// Create new test
exports.createTest = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { subject, title, description, timeLimit, tier, testType } = req.body;

    // Get random questions based on tier
    const questions = await Question.aggregate([
      {
        $match: {
          subject: subject,
          isActive: true,
          tier: req.user.isPremiumActive()
            ? { $in: ["free", "premium"] }
            : "free",
        },
      },
      { $sample: { size: 50 } },
    ]);

    if (questions.length < 50) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Not enough questions available for this subject" }],
      });
    }

    const test = new Test({
      title,
      description,
      subject,
      user: req.user._id,
      timeLimit,
      tier,
      testType,
      totalQuestions: questions.length,
      questions: questions.map((q) => ({
        question: q._id,
        userAnswer: null,
        isCorrect: null,
        timeSpent: 0,
      })),
      questionsAnswered: 0,
    });

    await test.save();

    res.status(201).json({
      success: true,
      data: test,
    });
  } catch (error) {
    console.error("Create test error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while creating test" }],
    });
  }
};

// Get user's tests
exports.getUserTests = async (req, res) => {
  try {
    const tests = await Test.find({ user: req.user._id })
      .populate("subject", "name")
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: tests,
    });
  } catch (error) {
    console.error("Get user tests error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching tests" }],
    });
  }
};

// Get single test
exports.getTest = async (req, res) => {
  try {
    const test = await Test.findById(req.params.id)
      .populate("subject", "name")
      .populate("topic", "topicName")
      .populate("questions.question");

    if (!test) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Test not found" }],
      });
    }

    // Check if user owns the test
    if (test.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        errors: [{ msg: "Not authorized to access this test" }],
      });
    }

    res.json({
      success: true,
      data: test,
    });
  } catch (error) {
    console.error("Get test error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching test" }],
    });
  }
};

// Start test
exports.startTest = async (req, res) => {
  try {
    const test = await Test.findById(req.params.id);

    if (!test) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Test not found" }],
      });
    }

    if (test.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        errors: [{ msg: "Not authorized to access this test" }],
      });
    }

    if (test.status !== "pending") {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Test has already been started or completed" }],
      });
    }

    await test.start();

    res.json({
      success: true,
      data: test,
    });
  } catch (error) {
    console.error("Start test error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while starting test" }],
    });
  }
};

// Answer question
exports.answerQuestion = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { questionId, answerIndex, timeSpent } = req.body;

    const test = await Test.findById(req.params.id);

    if (!test) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Test not found" }],
      });
    }

    if (test.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        errors: [{ msg: "Not authorized to access this test" }],
      });
    }

    if (test.status !== "in_progress") {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Test is not in progress" }],
      });
    }

    await test.answerQuestion(questionId, answerIndex, timeSpent);

    res.json({
      success: true,
      data: test,
    });
  } catch (error) {
    console.error("Answer question error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while submitting answer" }],
    });
  }
};

// Complete test
exports.completeTest = async (req, res) => {
  try {
    const test = await Test.findById(req.params.id);

    if (!test) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Test not found" }],
      });
    }

    if (test.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        errors: [{ msg: "Not authorized to access this test" }],
      });
    }

    if (test.status !== "in_progress") {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Test is not in progress" }],
      });
    }

    await test.complete();

    // Update user's test history
    await User.findByIdAndUpdate(req.user._id, {
      $push: {
        testHistory: {
          testId: test._id,
          score: test.score,
          completedAt: test.completedAt,
        },
      },
    });

    res.json({
      success: true,
      data: test,
    });
  } catch (error) {
    console.error("Complete test error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while completing test" }],
    });
  }
};

// Start a subject-wise test (at least one question per topic, up to 30, with timeLimit and difficulty)
exports.startSubjectTest = async (req, res) => {
  try {
    const subjectId = req.params.subjectId;
    const userId = req.user._id;
    const { timeLimit, difficulty = "mixed" } = req.body;
    const subject = await Subject.findById(subjectId).populate("topics");
    if (!subject)
      return res
        .status(404)
        .json({ success: false, errors: [{ msg: "Subject not found" }] });
    let selectedQuestions = [];
    // At least one per topic
    for (const topicId of subject.topics) {
      const q = await Question.findOne({
        topic: topicId,
        isActive: true,
        ...(difficulty !== "mixed" ? { difficulty } : {}),
      });
      if (q) selectedQuestions.push(q);
    }
    // Fill up to 30 with randoms from all topics
    if (selectedQuestions.length < 30) {
      const allQs = await Question.find({
        subject: subjectId,
        isActive: true,
        ...(difficulty !== "mixed" ? { difficulty } : {}),
      });
      const extra = allQs.filter(
        (q) => !selectedQuestions.some((sel) => sel._id.equals(q._id))
      );
      extra.sort(() => 0.5 - Math.random());
      selectedQuestions = selectedQuestions.concat(
        extra.slice(0, 30 - selectedQuestions.length)
      );
    }
    if (selectedQuestions.length < 1)
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Not enough questions available for this subject" }],
      });
    selectedQuestions = selectedQuestions.slice(0, 30);
    const test = await Test.create({
      user: userId,
      subject: subjectId,
      mode: "subject-test",
      difficulty,
      totalQuestions: selectedQuestions.length,
      questions: selectedQuestions.map((q) => ({ question: q._id })),
      timeLimit,
      status: "pending",
    });

    const createdTest = await Test.findById(test._id)
      .populate("questions.question")
      .select(
        "-__v -createdAt -updatedAt -questions.question.options.isCorrect"
      );

    res.status(201).json({ success: true, data: createdTest });
  } catch (error) {
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while starting subject test" }],
    });
  }
};

// Start a topic-wise test (30 random from topic, with timeLimit and difficulty)
exports.startTopicTest = async (req, res) => {
  try {
    const topicId = req.params.topicId;
    const userId = req.user._id;
    const { timeLimit, difficulty = "mixed" } = req.body;
    const allQuestions = await Question.find({
      topic: topicId,
      isActive: true,
      ...(difficulty !== "mixed" ? { difficulty } : {}),
    });
    allQuestions.sort(() => 0.5 - Math.random());
    const selectedQuestions = allQuestions.slice(0, 30);
    if (selectedQuestions.length < 1)
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Not enough questions available for this topic" }],
      });
    const test = await Test.create({
      user: userId,
      subject: selectedQuestions[0]?.subject,
      topic: topicId,
      mode: "topic-test",
      difficulty,
      totalQuestions: selectedQuestions.length,
      questions: selectedQuestions.map((q) => ({ question: q._id })),
      timeLimit,
      status: "pending",
    });
    res.status(201).json({ success: true, data: test });
  } catch (error) {
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while starting topic test" }],
    });
  }
};

// Start a mixed test (50 random from all, with timeLimit and difficulty)
exports.startMixedTest = async (req, res) => {
  try {
    const userId = req.user._id;
    const { timeLimit, difficulty = "mixed" } = req.body;
    const allQuestions = await Question.find({
      isActive: true,
      ...(difficulty !== "mixed" ? { difficulty } : {}),
    });
    allQuestions.sort(() => 0.5 - Math.random());
    const selectedQuestions = allQuestions.slice(0, 50);
    if (selectedQuestions.length < 1)
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Not enough questions available for mixed test" }],
      });
    const test = await Test.create({
      user: userId,
      mode: "mixed-test",
      difficulty,
      totalQuestions: selectedQuestions.length,
      questions: selectedQuestions.map((q) => ({ question: q._id })),
      timeLimit,
      status: "pending",
    });
    res.status(201).json({ success: true, data: test });
  } catch (error) {
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while starting mixed test" }],
    });
  }
};
