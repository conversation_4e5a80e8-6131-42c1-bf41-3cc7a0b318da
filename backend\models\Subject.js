const mongoose = require('mongoose');

const subjectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Subject name is required'],
    trim: true,
    maxlength: [100, 'Subject name cannot exceed 100 characters'],
    unique: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  icon: {
    type: String,
    default: 'book' // Default icon name
  },
  color: {
    type: String,
    default: '#FF6B6B' // Default to primary color
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0 // For sorting subjects
  },
  categories: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  stats: {
    // Topic Statistics
    totalTopics: {
      type: Number,
      default: 0
    },
    activeTopics: {
      type: Number,
      default: 0
    },
    premiumTopics: {
      type: Number,
      default: 0
    },

    // Question Statistics
    totalQuestions: {
      type: Number,
      default: 0
    },
    freeQuestions: {
      type: Number,
      default: 0
    },
    premiumQuestions: {
      type: Number,
      default: 0
    },
    activeQuestions: {
      type: Number,
      default: 0
    },

    // Difficulty Distribution
    difficultyDistribution: {
      easy: { type: Number, default: 0 },
      medium: { type: Number, default: 0 },
      hard: { type: Number, default: 0 }
    },
    averageDifficulty: {
      type: Number,
      default: 0
    },

    // Engagement Statistics
    totalAttempts: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },

    // Content Statistics
    totalQuizzes: {
      type: Number,
      default: 0
    },
    totalTests: {
      type: Number,
      default: 0
    },

    // Performance Metrics
    popularityScore: {
      type: Number,
      default: 0
    },
    lastActivityDate: {
      type: Date,
      default: null
    },
    lastCalculated: {
      type: Date,
      default: Date.now
    }
  },
  topics: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Topic'
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Pre-save middleware to generate slug
subjectSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  this.updatedAt = new Date();
  next();
});

// Virtual for question distribution
subjectSchema.virtual('questionDistribution').get(function() {
  const total = this.stats.totalQuestions;
  if (total === 0) return { free: 0, premium: 0 };
  
  return {
    free: Math.round((this.stats.freeQuestions / total) * 100),
    premium: Math.round((this.stats.premiumQuestions / total) * 100)
  };
});

// Enhanced method to calculate comprehensive subject statistics
subjectSchema.methods.updateQuestionStats = async function() {
  const Topic = mongoose.model('Topic');
  const Question = mongoose.model('Question');

  try {
    // Get all topics for this subject
    const topics = await Topic.find({ subject: this._id });

    // Topic statistics
    const totalTopics = topics.length;
    const activeTopics = topics.filter(t => t.isActive).length;
    const premiumTopics = topics.filter(t => t.isPremium).length;

    // Get all questions for this subject
    const questions = await Question.find({ subject: this._id });

    // Question statistics
    const totalQuestions = questions.length;
    const freeQuestions = questions.filter(q => q.tier === 'free').length;
    const premiumQuestions = questions.filter(q => q.tier === 'premium').length;
    const activeQuestions = questions.filter(q => q.isActive).length;

    // Difficulty distribution
    const difficultyDistribution = {
      easy: questions.filter(q => q.difficulty === 'easy').length,
      medium: questions.filter(q => q.difficulty === 'medium').length,
      hard: questions.filter(q => q.difficulty === 'hard').length
    };

    const difficultyValues = { easy: 1, medium: 2, hard: 3 };
    const averageDifficulty = totalQuestions > 0
      ? questions.reduce((sum, q) => sum + difficultyValues[q.difficulty], 0) / totalQuestions
      : 0;

    // Engagement statistics
    const totalAttempts = questions.reduce((sum, q) => sum + q.usageCount, 0);
    const totalCorrectAnswers = questions.reduce((sum, q) => sum + q.correctAnswerCount, 0);
    const averageScore = totalAttempts > 0 ? (totalCorrectAnswers / totalAttempts) * 100 : 0;

    // Calculate popularity score
    const popularityScore = topics.reduce((sum, t) => sum + (t.stats?.popularityScore || 0), 0) / Math.max(totalTopics, 1);

    // Find last activity date
    const lastActivityDate = await this.getLastActivityDate(topics);

    // Update stats
    this.stats = {
      totalTopics,
      activeTopics,
      premiumTopics,
      totalQuestions,
      freeQuestions,
      premiumQuestions,
      activeQuestions,
      difficultyDistribution,
      averageDifficulty: Math.round(averageDifficulty * 100) / 100,
      totalAttempts,
      averageScore: Math.round(averageScore * 100) / 100,
      totalQuizzes: this.stats.totalQuizzes || 0, // Preserve existing values
      totalTests: this.stats.totalTests || 0,
      popularityScore: Math.round(popularityScore * 100) / 100,
      lastActivityDate,
      lastCalculated: new Date()
    };

    return this.save();
  } catch (error) {
    console.error('Error updating subject stats:', error);
    throw error;
  }
};

// Method to get last activity date
subjectSchema.methods.getLastActivityDate = async function(topics = null) {
  try {
    if (!topics) {
      const Topic = mongoose.model('Topic');
      topics = await Topic.find({ subject: this._id });
    }

    if (topics.length === 0) return this.updatedAt;

    // Get the most recent activity from topics
    const lastTopicActivity = topics.reduce((latest, topic) => {
      const topicDate = topic.stats?.lastActivityDate || topic.updatedAt;
      return topicDate > latest ? topicDate : latest;
    }, new Date(0));

    return lastTopicActivity > this.updatedAt ? lastTopicActivity : this.updatedAt;
  } catch (error) {
    console.error('Error getting last activity date:', error);
    return this.updatedAt;
  }
};

// Method to add category
subjectSchema.methods.addCategory = function(categoryData) {
  this.categories.push(categoryData);
  return this.save();
};

// Method to update category
subjectSchema.methods.updateCategory = function(categoryId, updateData) {
  const category = this.categories.id(categoryId);
  if (!category) {
    throw new Error('Category not found');
  }
  
  Object.assign(category, updateData);
  return this.save();
};

// Method to remove category
subjectSchema.methods.removeCategory = function(categoryId) {
  this.categories.pull(categoryId);
  return this.save();
};

// Static method to get active subjects with stats
subjectSchema.statics.getActiveWithStats = function() {
  return this.find({ isActive: true })
    .sort({ order: 1, name: 1 })
    .select('-__v');
};

// Index for efficient queries
subjectSchema.index({ isActive: 1, order: 1 });
subjectSchema.index({ 'categories.name': 1 });

module.exports = mongoose.model('Subject', subjectSchema);
