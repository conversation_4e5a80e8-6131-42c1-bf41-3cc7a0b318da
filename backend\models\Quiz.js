const mongoose = require("mongoose");

const quizSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    subject: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subject",
      required: true,
    },
    totalQuestions: {
      type: Number,
      default: 0,
    },
    questionsAnswered: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Question",
      },
    ],
    questions: [
      {
        question: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Question",
          required: true,
        },
        userAnswer: {
          type: Number,
          default: null,
        },
        isCorrect: {
          type: Boolean,
          default: null,
        },
        timeSpent: {
          type: Number,
          default: 0, // in seconds
        },
        answeredAt: {
          type: Date,
          default: null,
        },
      },
    ],
    status: {
      type: String,
      enum: ["pending", "in_progress", "completed", "abandoned"],
      default: "pending",
    },
    startedAt: {
      type: Date,
    },
    completedAt: {
      type: Date,
    },
    totalTimeSpent: {
      type: Number,
      default: 0, // in seconds
    },
    score: {
      type: Number,
      default: 0,
    },
    analytics: {
      averageTimePerQuestion: {
        type: Number,
        default: 0,
      },
      fastestQuestion: {
        type: Number,
        default: 0,
      },
      slowestQuestion: {
        type: Number,
        default: 0,
      },
      difficultyBreakdown: {
        easy: {
          correct: { type: Number, default: 0 },
          total: { type: Number, default: 0 },
        },
        medium: {
          correct: { type: Number, default: 0 },
          total: { type: Number, default: 0 },
        },
        hard: {
          correct: { type: Number, default: 0 },
          total: { type: Number, default: 0 },
        },
      },
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    mode: {
      type: String,
      enum: ["subject-quiz", "topic-quiz"],
      required: true,
    },
    topic: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Topic",
      default: null,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual for quiz duration
quizSchema.virtual("duration").get(function () {
  if (!this.startedAt || !this.completedAt) return 0;
  return Math.round((this.completedAt - this.startedAt) / 1000); // in seconds
});

// Virtual for progress percentage
quizSchema.virtual("progressPercentage").get(function () {
  const answeredQuestions = this.questions.filter(
    (q) => q.userAnswer !== null
  ).length;
  return Math.round((answeredQuestions / this.questions.length) * 100);
});

// Calculate score before saving
quizSchema.pre("save", function (next) {
  if (this.status === "completed") {
    const correctAnswers = this.questions.filter((q) => q.isCorrect).length;
    this.score = (correctAnswers / this.questions.length) * 100;
  }
  next();
});

// Method to start quiz
quizSchema.methods.start = function () {
  this.status = "in_progress";
  this.startedAt = new Date();
  return this.save();
};

// Method to complete quiz
quizSchema.methods.complete = function () {
  this.status = "completed";
  this.completedAt = new Date();
  return this.save();
};

// Method to abandon quiz
quizSchema.methods.abandon = function () {
  this.status = "abandoned";
  this.completedAt = new Date();
  return this.save();
};

// Method to answer a question
quizSchema.methods.answerQuestion = async function (
  questionId,
  answerIndex,
  timeSpent
) {
  const question = this.questions.id(questionId);
  if (!question) {
    throw new Error("Question not found in quiz");
  }

  question.userAnswer = answerIndex;
  question.timeSpent = timeSpent;

  // Get the question document to check correct answer
  const Question = mongoose.model("Question");
  const questionDoc = await Question.findById(questionId);

  question.isCorrect = questionDoc.options[answerIndex].isCorrect;

  return this.save();
};

// Static method to get user quiz history
quizSchema.statics.getUserHistory = function (userId, options = {}) {
  const { page = 1, limit = 10, status = null } = options;
  const skip = (page - 1) * limit;

  const match = { user: mongoose.Types.ObjectId(userId) };
  if (status) match.status = status;

  return this.find(match)
    .populate("subject", "name slug color")
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .select("-questions.question"); // Don't include full question data
};

// Indexes for efficient queries
quizSchema.index({ user: 1, status: 1 });
quizSchema.index({ subject: 1, tier: 1 });
quizSchema.index({ createdAt: -1 });
quizSchema.index({ "score.percentage": -1 });

module.exports = mongoose.model("Quiz", quizSchema);
