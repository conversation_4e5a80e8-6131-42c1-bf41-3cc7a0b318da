import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { ArrowLef<PERSON>, Edit, Trash2, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getSubjectById } from "../actions";
import { format } from "date-fns";

interface SubjectDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function SubjectDetailsPage({ params }: SubjectDetailsPageProps) {
  const result = await getSubjectById(params.id);

  if (!result.success) {
    notFound();
  }

  const subject = result.data;

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/subjects">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Subjects
            </Link>
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/subjects/${subject._id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
          <Button variant="destructive">
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Subject Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-4">
              {subject.color && (
                <div
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg"
                  style={{ backgroundColor: subject.color }}
                >
                  {subject.name.charAt(0).toUpperCase()}
                </div>
              )}
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-2xl">{subject.name}</CardTitle>
                  <Badge variant={subject.isActive ? "default" : "secondary"}>
                    {subject.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <CardDescription className="text-base mt-1">
                  {subject.description}
                </CardDescription>
                {subject.slug && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Slug: /{subject.slug}
                  </p>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {subject.stats?.totalQuestions || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {subject.stats?.freeQuestions || 0} free, {subject.stats?.premiumQuestions || 0} premium
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Topics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Array.isArray(subject.topics) ? subject.topics.length : 0}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quizzes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {subject.stats?.totalQuizzes || 0}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {subject.stats?.totalTests || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Subject Details */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Subject Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Order</label>
                <p className="text-sm">{subject.order || 0}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Created</label>
                <p className="text-sm">{format(new Date(subject.createdAt), "PPP")}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                <p className="text-sm">{format(new Date(subject.updatedAt), "PPP")}</p>
              </div>
              {subject.icon && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Icon</label>
                  <p className="text-sm">{subject.icon}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Topics</CardTitle>
                <Button size="sm" asChild>
                  <Link href={`/dashboard/topics/create?subject=${subject._id}`}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Topic
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {Array.isArray(subject.topics) && subject.topics.length > 0 ? (
                <div className="space-y-2">
                  {subject.topics.slice(0, 5).map((topic) => (
                    <div key={topic._id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="font-medium">{topic.topicName}</p>
                        <p className="text-sm text-muted-foreground">{topic.description}</p>
                      </div>
                      <Badge variant={topic.isActive ? "default" : "secondary"}>
                        {topic.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  ))}
                  {subject.topics.length > 5 && (
                    <p className="text-sm text-muted-foreground text-center">
                      And {subject.topics.length - 5} more topics...
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No topics found for this subject.</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
