const { validationResult } = require("express-validator");
const Subject = require("../models/Subject");
const Topic = require("../models/Topic");
const Question = require("../models/Question");
const Quiz = require("../models/Quiz");
const Test = require("../models/Test");
const User = require("../models/User");

// Get all subjects
exports.getSubjects = async (req, res) => {
  try {
    // Check if user is admin to determine what data to return
    const isAdmin = req.user && req.user.role === 'admin';

    // For admin users, get all subjects (including inactive)
    // For regular users, only get active subjects
    const query = isAdmin ? {} : { isActive: true };

    const subjects = await Subject.find(query)
      .sort({ order: 1, name: 1 })
      .populate("topics");

    // If admin, include additional stats
    let responseData = subjects;
    let adminStats = null;

    if (isAdmin) {
      // Calculate admin statistics
      const totalSubjects = subjects.length;
      const activeSubjects = subjects.filter(subject => subject.isActive).length;

      // Calculate total questions across all subjects
      const totalQuestions = await Question.countDocuments();
      const freeQuestions = await Question.countDocuments({ tier: 'free' });
      const premiumQuestions = await Question.countDocuments({ tier: 'premium' });

      // Calculate total topics across all subjects
      const totalTopics = await Topic.countDocuments();
      const activeTopics = await Topic.countDocuments({ isActive: true });

      adminStats = {
        totalSubjects,
        activeSubjects,
        inactiveSubjects: totalSubjects - activeSubjects,
        totalQuestions,
        freeQuestions,
        premiumQuestions,
        totalTopics,
        activeTopics,
        inactiveTopics: totalTopics - activeTopics
      };

      // Add individual subject stats for admin
      for (let subject of subjects) {
        const subjectQuestions = await Question.countDocuments({
          topic: { $in: subject.topics.map(t => t._id) }
        });
        const subjectFreeQuestions = await Question.countDocuments({
          topic: { $in: subject.topics.map(t => t._id) },
          tier: 'free'
        });
        const subjectPremiumQuestions = await Question.countDocuments({
          topic: { $in: subject.topics.map(t => t._id) },
          tier: 'premium'
        });

        subject.stats = {
          totalQuestions: subjectQuestions,
          freeQuestions: subjectFreeQuestions,
          premiumQuestions: subjectPremiumQuestions,
          totalTopics: subject.topics.length,
          activeTopics: subject.topics.filter(t => t.isActive).length
        };
      }
    }

    const response = {
      success: true,
      data: subjects,
    };

    // Add admin stats to response if user is admin
    if (isAdmin && adminStats) {
      response.adminStats = adminStats;
    }

    res.json(response);
  } catch (error) {
    console.error("Get subjects error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subjects" }],
    });
  }
};

// Get single subject
exports.getSubject = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id).populate("topics");
    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }
    res.json({
      success: true,
      data: subject,
    });
  } catch (error) {
    console.error("Get subject error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject" }],
    });
  }
};

// Create subject
exports.createSubject = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { name, description, icon, order } = req.body;

    // Check if subject already exists
    let subject = await Subject.findOne({ name });
    if (subject) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Subject already exists" }],
      });
    }

    // Generate slug from name
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");

    subject = new Subject({
      name,
      description,
      icon,
      order,
      slug,
      createdBy: req.user._id, // Get user ID from auth middleware
    });

    await subject.save();

    res.status(201).json({
      success: true,
      data: subject,
    });
  } catch (error) {
    console.error("Create subject error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while creating subject" }],
    });
  }
};

// Update subject
exports.updateSubject = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { name, description, icon, order, isActive } = req.body;

    // Check if subject exists
    let subject = await Subject.findById(req.params.id).populate("topics");
    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    // Check if new name already exists (if name is being changed)
    if (name && name !== subject.name) {
      const existingSubject = await Subject.findOne({ name });
      if (existingSubject) {
        return res.status(400).json({
          success: false,
          errors: [{ msg: "Subject with this name already exists" }],
        });
      }
    }

    // Update subject
    subject = await Subject.findByIdAndUpdate(
      req.params.id,
      { name, description, icon, order, isActive },
      { new: true }
    ).populate("topics");

    res.json({
      success: true,
      data: subject,
    });
  } catch (error) {
    console.error("Update subject error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating subject" }],
    });
  }
};

// Delete subject
exports.deleteSubject = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    // Soft delete by setting isActive to false
    subject.isActive = false;
    await subject.save();

    res.json({
      success: true,
      data: { msg: "Subject deleted successfully" },
    });
  } catch (error) {
    console.error("Delete subject error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while deleting subject" }],
    });
  }
};

// Get subject statistics
exports.getSubjectStats = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    // Get question counts
    const questionStats = await Question.aggregate([
      { $match: { subject: subject._id, isActive: true } },
      {
        $group: {
          _id: "$tier",
          count: { $sum: 1 },
        },
      },
    ]);

    // Get quiz counts
    const quizStats = await Quiz.aggregate([
      { $match: { subject: subject._id, isActive: true } },
      {
        $group: {
          _id: "$tier",
          count: { $sum: 1 },
        },
      },
    ]);

    // Get test counts
    const testStats = await Test.aggregate([
      { $match: { subject: subject._id, isActive: true } },
      {
        $group: {
          _id: "$tier",
          count: { $sum: 1 },
        },
      },
    ]);

    res.json({
      success: true,
      data: {
        questions: questionStats,
        quizzes: quizStats,
        tests: testStats,
        totalQuestions: subject.stats.totalQuestions,
        totalQuizzes: subject.stats.totalQuizzes,
        totalTests: subject.stats.totalTests,
      },
    });
  } catch (error) {
    console.error("Get subject stats error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject statistics" }],
    });
  }
};

// Get subject analytics (admin only)
exports.getSubjectAnalytics = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    // Get question analytics
    const questionAnalytics = await Question.aggregate([
      { $match: { subject: subject._id, isActive: true } },
      {
        $group: {
          _id: "$tier",
          count: { $sum: 1 },
          averageDifficulty: { $avg: "$difficulty" },
          averageTimeSpent: { $avg: "$averageTimeSpent" },
        },
      },
    ]);

    // Get quiz analytics
    const quizAnalytics = await Quiz.aggregate([
      { $match: { subject: subject._id, isActive: true } },
      {
        $group: {
          _id: "$tier",
          count: { $sum: 1 },
          averageScore: { $avg: "$averageScore" },
          averageTimeSpent: { $avg: "$averageTimeSpent" },
        },
      },
    ]);

    // Get test analytics
    const testAnalytics = await Test.aggregate([
      { $match: { subject: subject._id, isActive: true } },
      {
        $group: {
          _id: "$tier",
          count: { $sum: 1 },
          averageScore: { $avg: "$averageScore" },
          averageTimeSpent: { $avg: "$averageTimeSpent" },
        },
      },
    ]);

    res.json({
      success: true,
      data: {
        questions: questionAnalytics,
        quizzes: quizAnalytics,
        tests: testAnalytics,
      },
    });
  } catch (error) {
    console.error("Get subject analytics error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject analytics" }],
    });
  }
};

// Get subject questions
exports.getSubjectQuestions = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    const questions = await Question.find({
      subject: subject._id,
      isActive: true,
    })
      .select("-correctOption")
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: questions,
    });
  } catch (error) {
    console.error("Get subject questions error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject questions" }],
    });
  }
};

// Get subject quizzes
exports.getSubjectQuizzes = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    const quizzes = await Quiz.find({ subject: subject._id, isActive: true })
      .select("-questions")
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: quizzes,
    });
  } catch (error) {
    console.error("Get subject quizzes error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject quizzes" }],
    });
  }
};

// Get subject tests
exports.getSubjectTests = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    const tests = await Test.find({ subject: subject._id, isActive: true })
      .select("-questions")
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: tests,
    });
  } catch (error) {
    console.error("Get subject tests error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject tests" }],
    });
  }
};

// Get subject leaderboard
exports.getSubjectLeaderboard = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    // Get top performers for this subject
    const leaderboard = await User.aggregate([
      {
        $match: {
          "quizHistory.quiz": { $in: subject.quizzes },
          "testHistory.test": { $in: subject.tests },
        },
      },
      {
        $project: {
          firstName: 1,
          lastName: 1,
          score: {
            $add: [
              { $avg: "$quizHistory.score" },
              { $avg: "$testHistory.score" },
            ],
          },
        },
      },
      { $sort: { score: -1 } },
      { $limit: 10 },
    ]);

    res.json({
      success: true,
      data: leaderboard,
    });
  } catch (error) {
    console.error("Get subject leaderboard error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject leaderboard" }],
    });
  }
};

// Get subject progress
exports.getSubjectProgress = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    const user = await User.findById(req.user._id)
      .select("quizHistory testHistory")
      .populate("quizHistory.quiz", "subject")
      .populate("testHistory.test", "subject");

    // Filter history for this subject
    const subjectQuizzes = user.quizHistory.filter(
      (qh) => qh.quiz.subject.toString() === subject._id.toString()
    );
    const subjectTests = user.testHistory.filter(
      (th) => th.test.subject.toString() === subject._id.toString()
    );

    // Calculate progress
    const progress = {
      quizzesCompleted: subjectQuizzes.length,
      testsCompleted: subjectTests.length,
      averageQuizScore:
        subjectQuizzes.reduce((acc, qh) => acc + qh.score, 0) /
          subjectQuizzes.length || 0,
      averageTestScore:
        subjectTests.reduce((acc, th) => acc + th.score, 0) /
          subjectTests.length || 0,
      totalQuestionsAnswered:
        subjectQuizzes.reduce((acc, qh) => acc + qh.questionsAnswered, 0) +
        subjectTests.reduce((acc, th) => acc + th.questionsAnswered, 0),
      totalCorrectAnswers:
        subjectQuizzes.reduce((acc, qh) => acc + qh.correctAnswers, 0) +
        subjectTests.reduce((acc, th) => acc + th.correctAnswers, 0),
    };

    res.json({
      success: true,
      data: progress,
    });
  } catch (error) {
    console.error("Get subject progress error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject progress" }],
    });
  }
};

// Get subject categories
exports.getSubjectCategories = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    res.json({
      success: true,
      data: subject.categories,
    });
  } catch (error) {
    console.error("Get subject categories error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching subject categories" }],
    });
  }
};

// Add subject category
exports.addSubjectCategory = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    const { name, description } = req.body;

    // Check if category already exists
    const existingCategory = subject.categories.find(
      (cat) => cat.name.toLowerCase() === name.toLowerCase()
    );

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Category already exists" }],
      });
    }

    // Add category
    subject.categories.push({ name, description });
    await subject.save();

    res.json({
      success: true,
      data: subject.categories,
    });
  } catch (error) {
    console.error("Add subject category error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while adding subject category" }],
    });
  }
};

// Update subject category
exports.updateSubjectCategory = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    const { name, description, isActive } = req.body;
    const category = subject.categories.id(req.params.categoryId);

    if (!category) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Category not found" }],
      });
    }

    // Check if new name already exists
    if (name && name.toLowerCase() !== category.name.toLowerCase()) {
      const existingCategory = subject.categories.find(
        (cat) => cat.name.toLowerCase() === name.toLowerCase()
      );

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          errors: [{ msg: "Category with this name already exists" }],
        });
      }
    }

    // Update category
    category.name = name || category.name;
    category.description = description || category.description;
    category.isActive = isActive !== undefined ? isActive : category.isActive;

    await subject.save();

    res.json({
      success: true,
      data: subject.categories,
    });
  } catch (error) {
    console.error("Update subject category error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating subject category" }],
    });
  }
};

// Delete subject category
exports.deleteSubjectCategory = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Subject not found" }],
      });
    }

    const category = subject.categories.id(req.params.categoryId);

    if (!category) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "Category not found" }],
      });
    }

    // Remove category
    subject.categories.pull(req.params.categoryId);
    await subject.save();

    res.json({
      success: true,
      data: { msg: "Category deleted successfully" },
    });
  } catch (error) {
    console.error("Delete subject category error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while deleting subject category" }],
    });
  }
};
