const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to verify admin JWT token
const adminAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'No authentication token, admin access denied' }]
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if token is admin type
    if (!decoded.isAdmin || decoded.tokenType !== 'admin') {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Invalid admin token' }]
      });
    }

    // Get user from database
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Admin user not found' }]
      });
    }

    // Verify user has admin role
    if (user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        errors: [{ msg: 'Access denied. Admin privileges required.' }]
      });
    }

    // Attach user and token to request
    req.user = user;
    req.token = token;
    req.isAdmin = true;
    
    next();
  } catch (error) {
    console.error('Admin auth middleware error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Invalid admin token' }]
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        errors: [{ msg: 'Admin token expired' }]
      });
    }
    
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error in admin authentication' }]
    });
  }
};

// Middleware to verify admin role (to be used after regular auth middleware)
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      errors: [{ msg: 'Authentication required' }]
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      errors: [{ msg: 'Access denied. Admin privileges required.' }]
    });
  }

  next();
};

// Middleware for super admin access (if you want to add different admin levels)
const requireSuperAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      errors: [{ msg: 'Authentication required' }]
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      errors: [{ msg: 'Access denied. Admin privileges required.' }]
    });
  }

  // You can add additional checks here for super admin
  // For example, checking a specific field like isSuperAdmin
  // if (!req.user.isSuperAdmin) {
  //   return res.status(403).json({
  //     success: false,
  //     errors: [{ msg: 'Access denied. Super admin privileges required.' }]
  //   });
  // }

  next();
};

// Rate limiting middleware specifically for admin endpoints
const adminRateLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
  const attempts = new Map();

  return (req, res, next) => {
    const key = req.ip + ':admin';
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old attempts
    if (attempts.has(key)) {
      const userAttempts = attempts.get(key).filter(time => time > windowStart);
      attempts.set(key, userAttempts);
    }

    const currentAttempts = attempts.get(key) || [];

    if (currentAttempts.length >= maxAttempts) {
      return res.status(429).json({
        success: false,
        errors: [{ msg: 'Too many admin login attempts. Please try again later.' }]
      });
    }

    // Add current attempt
    currentAttempts.push(now);
    attempts.set(key, currentAttempts);

    next();
  };
};

// Optional admin authentication (doesn't fail if no token or not admin)
const optionalAdminAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      req.isAdmin = false;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if it's an admin token
    if (decoded.isAdmin && decoded.tokenType === 'admin') {
      const user = await User.findById(decoded.userId).select('-password');
      
      if (user && user.role === 'admin') {
        req.user = user;
        req.isAdmin = true;
      } else {
        req.user = null;
        req.isAdmin = false;
      }
    } else {
      req.user = null;
      req.isAdmin = false;
    }
    
    next();
  } catch (error) {
    // If token is invalid, continue without admin privileges
    req.user = null;
    req.isAdmin = false;
    next();
  }
};

module.exports = {
  adminAuth,
  requireAdmin,
  requireSuperAdmin,
  adminRateLimit,
  optionalAdminAuth
};
