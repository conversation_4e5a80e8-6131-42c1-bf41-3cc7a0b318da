const express = require("express");
const { check, param } = require("express-validator");
const router = express.Router();
const {
  getUserTests,
  getTest,
  startTest,
  answerQuestion,
  completeTest,
  startSubjectTest,
  startTopicTest,
  startMixedTest,
} = require("../controllers/testController");
const { auth } = require("../middleware/auth");
const { 
  validateTestId, 
  validateStartTest, 
  validateAnswerQuestion, 
  validateCompleteTest, 
  validateStartSubjectTest, 
  validateStartTopicTest, 
  validateStartMixedTest 
} = require("../validators/testValidators");

// @route   GET api/tests
// @desc    Get all tests for the current user
// @access  Private
router.get("/", auth, getUserTests);

// @route   GET api/tests/:id
// @desc    Get a single test
// @access  Private
router.get("/:id", auth, validateTestId, getTest);

// @route   POST api/tests/:id/start
// @desc    Start a test
// @access  Private
router.post("/:id/start", auth, validateTestId, validateStartTest, startTest);

// @route   POST api/tests/:id/answer
// @desc    Submit an answer for a question
// @access  Private
router.post(
  "/:id/answer",
  [
    auth,
    validateTestId,
    validateAnswerQuestion
  ],
  answerQuestion
);

// @route   POST api/tests/:id/complete
// @desc    Complete a test
// @access  Private
router.post("/:id/complete", auth, validateTestId, validateCompleteTest, completeTest);

// Start a subject-wise test for a user
router.post(
  "/subject/:subjectId/start",
  auth,
  validateStartSubjectTest,
  startSubjectTest
);

// Start a topic-wise test for a user
router.post(
  "/topic/:topicId/start",
  auth,
  validateStartTopicTest,
  startTopicTest
);

// Start a mixed test for a user
router.post(
  "/mixed/start",
  auth,
  validateStartMixedTest,
  startMixedTest
);

module.exports = router;
