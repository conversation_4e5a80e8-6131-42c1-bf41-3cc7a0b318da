# Admin Authentication Endpoints

This document describes the admin-specific authentication endpoints that have been added to the PhysioPrep backend API.

## Overview

The admin authentication system provides secure access control for administrative users with the following features:

- Separate authentication flow for admin users
- Role-based access control
- JWT tokens with admin-specific claims
- Rate limiting for security
- Refresh token support
- Integration with existing user model and patterns

## Endpoints

### 1. Admin Login

**POST** `/api/auth/admin/login`

Authenticates an admin user and returns access and refresh tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "adminpassword123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_id",
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "admin",
      "isPremium": true,
      "isPremiumActive": true,
      "isEmailVerified": true,
      "preferences": {...},
      "stats": {...},
      "avatar": null,
      "premiumExpiry": null,
      "lastLogin": "2024-01-01T00:00:00.000Z",
      "loginCount": 5,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

**Error Responses:**
- `401`: Invalid credentials or user is not an admin
- `403`: Access denied, admin privileges required
- `429`: Too many login attempts
- `400`: Validation errors

### 2. Admin Logout

**POST** `/api/auth/admin/logout`

Logs out the current admin user.

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Admin logged out successfully"
  }
}
```

**Error Responses:**
- `401`: Invalid or missing admin token
- `403`: Access denied, admin privileges required

### 3. Get Current Admin

**GET** `/api/auth/admin/me`

Returns the current authenticated admin user's information.

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "admin",
      "isPremium": true,
      "isPremiumActive": true,
      "isEmailVerified": true,
      "preferences": {...},
      "stats": {...},
      "avatar": null,
      "premiumExpiry": null,
      "lastLogin": "2024-01-01T00:00:00.000Z",
      "loginCount": 5,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

**Error Responses:**
- `401`: Invalid or missing admin token
- `403`: Access denied, admin privileges required
- `404`: Admin user not found

### 4. Refresh Admin Token

**POST** `/api/auth/admin/refresh`

Refreshes the admin access token using a valid refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**Error Responses:**
- `401`: Invalid or expired refresh token
- `403`: Access denied, admin privileges required
- `400`: Validation errors

## Security Features

### Token Structure

Admin tokens include special claims:
- `isAdmin: true`
- `tokenType: 'admin'`
- `role: 'admin'`
- Shorter expiry time (24 hours vs 30 days for regular users)

### Rate Limiting

- Login endpoint: 5 attempts per 15 minutes per IP
- Refresh endpoint: 10 attempts per 15 minutes per IP

### Validation

All endpoints include comprehensive input validation:
- Email format validation
- Password requirements
- JWT token format validation
- Required field validation

## Integration with Frontend

The admin endpoints are designed to work seamlessly with Next.js cookie-based authentication:

1. Store admin tokens in secure HTTP-only cookies
2. Use the refresh token for automatic token renewal
3. Implement proper error handling for token expiration
4. Redirect to admin login on authentication failures

## Usage Examples

### Frontend Integration (Next.js)

```javascript
// Admin login
const adminLogin = async (email, password) => {
  const response = await fetch('/api/auth/admin/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  
  if (response.ok) {
    const data = await response.json();
    // Store tokens in secure cookies
    document.cookie = `adminToken=${data.data.token}; secure; httpOnly`;
    document.cookie = `adminRefreshToken=${data.data.refreshToken}; secure; httpOnly`;
  }
};

// Get current admin
const getCurrentAdmin = async () => {
  const response = await fetch('/api/auth/admin/me', {
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });
  
  return response.json();
};
```

## Error Handling

All endpoints follow the consistent error response format:

```json
{
  "success": false,
  "errors": [
    {
      "msg": "Error message",
      "field": "fieldName" // optional
    }
  ]
}
```

## Testing

To test the admin endpoints:

1. Create an admin user in the database with `role: 'admin'`
2. Use the login endpoint to get tokens
3. Test protected endpoints with the admin token
4. Verify that regular user tokens are rejected

## Notes

- Admin tokens have shorter expiry times for enhanced security
- The system maintains backward compatibility with existing user authentication
- Admin endpoints are completely separate from regular user endpoints
- Rate limiting helps prevent brute force attacks
- All admin actions should be logged for audit purposes (can be added later)
