import { notFound } from "next/navigation";
import { SubjectForm } from "../../components/SubjectForm";
import { getSubjectById } from "../../actions";

interface EditSubjectPageProps {
  params: {
    id: string;
  };
}

export default async function EditSubjectPage({ params }: EditSubjectPageProps) {
  const result = await getSubjectById(params.id);

  if (!result.success) {
    notFound();
  }

  return <SubjectForm subject={result.data} mode="edit" />;
}
