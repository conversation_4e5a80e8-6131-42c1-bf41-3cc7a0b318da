const { asyncHand<PERSON> } = require('../middleware/errorHandler');
const {
  getTodaysQuestion,
  getQuestionForDate,
  submitDailyAnswer: submitDailyAnswerUtil,
  getUserDailyHistory,
  getDailyQuestionStats,
  createDailyQuestionsForRange,
  getDailyQuestionAnalytics,
  updateDailyQuestion
} = require('../utils/dailyQuestionManager');
const DailyQuestion = require('../models/DailyQuestion');
const User = require('../models/User');

// @desc    Get today's daily question
// @route   GET /api/daily-questions
// @access  Private
const getDailyQuestion = asyncHandler(async (req, res) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const dailyQuestion = await DailyQuestion.findOne({
    date: today
  }).populate('question');

  if (!dailyQuestion) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: 'No daily question available for today' }]
    });
  }

  // Check if user has already answered
  const hasAnswered = await User.exists({
    _id: req.user.id,
    'dailyAnswers.date': today
  });

  res.json({
    success: true,
    data: {
      question: dailyQuestion.question,
      hasAnswered,
      date: dailyQuestion.date
    }
  });
});

// @desc    Submit answer to daily question
// @route   POST /api/daily-questions/submit
// @access  Private
const submitDailyAnswer = asyncHandler(async (req, res) => {
  const { selectedOptionId, timeSpent } = req.body;
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const dailyQuestion = await DailyQuestion.findOne({
    date: today
  }).populate('question');

  if (!dailyQuestion) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: 'No daily question available for today' }]
    });
  }

  // Check if user has already answered
  const user = await User.findById(req.user.id);
  const hasAnswered = user.dailyAnswers.some(
    answer => answer.date.getTime() === today.getTime()
  );

  if (hasAnswered) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: 'You have already answered today\'s question' }]
    });
  }

  // Check if answer is correct
  const selectedOption = dailyQuestion.question.options.find(
    option => option._id.toString() === selectedOptionId
  );

  if (!selectedOption) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: 'Invalid option selected' }]
    });
  }

  const isCorrect = selectedOption.isCorrect;

  // Update user's daily answers
  user.dailyAnswers.push({
    date: today,
    question: dailyQuestion.question._id,
    selectedOptionId,
    timeSpent,
    isCorrect
  });

  // Update user's stats
  user.stats.dailyQuestionsAnswered++;
  if (isCorrect) {
    user.stats.dailyQuestionsCorrect++;
    user.stats.streak++;
  } else {
    user.stats.streak = 0;
  }

  await user.save();

  res.json({
    success: true,
    data: {
      isCorrect,
      correctAnswer: dailyQuestion.question.options.find(opt => opt.isCorrect)._id,
      explanation: dailyQuestion.question.explanation,
      streak: user.stats.streak
    }
  });
});

// @desc    Get user's daily question stats
// @route   GET /api/daily-questions/stats
// @access  Private
const getDailyStats = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id);

  const stats = {
    totalAnswered: user.stats.dailyQuestionsAnswered,
    correctAnswers: user.stats.dailyQuestionsCorrect,
    accuracy: user.stats.dailyQuestionsAnswered > 0
      ? Math.round((user.stats.dailyQuestionsCorrect / user.stats.dailyQuestionsAnswered) * 100)
      : 0,
    currentStreak: user.stats.streak,
    longestStreak: user.stats.longestStreak
  };

  res.json({
    success: true,
    data: stats
  });
});

// @desc    Get user's daily question history
// @route   GET /api/daily-questions/history
// @access  Private
const getDailyHistory = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id)
    .populate('dailyAnswers.question');

  const history = user.dailyAnswers
    .sort((a, b) => b.date - a.date)
    .map(answer => ({
      date: answer.date,
      question: {
        id: answer.question._id,
        text: answer.question.text,
        options: answer.question.options
      },
      selectedOptionId: answer.selectedOptionId,
      timeSpent: answer.timeSpent,
      isCorrect: answer.isCorrect
    }));

  res.json({
    success: true,
    data: history
  });
});

// @desc    Get daily question leaderboard
// @route   GET /api/daily-questions/leaderboard
// @access  Private
const getDailyLeaderboard = asyncHandler(async (req, res) => {
  const users = await User.find()
    .select('name stats.streak stats.dailyQuestionsCorrect')
    .sort({ 'stats.streak': -1, 'stats.dailyQuestionsCorrect': -1 })
    .limit(10);

  const leaderboard = users.map(user => ({
    name: user.name,
    streak: user.stats.streak,
    correctAnswers: user.stats.dailyQuestionsCorrect
  }));

  res.json({
    success: true,
    data: leaderboard
  });
});

// @desc    Get today's daily question
// @route   GET /api/daily-questions/today
// @access  Public (with premium access check)
const getTodaysDailyQuestion = asyncHandler(async (req, res) => {
  const dailyQuestion = await getTodaysQuestion();

  if (!dailyQuestion) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: 'No daily question available for today' }]
    });
  }

  // Check if user has access to this question tier
  if (req.allowedTiers && !req.allowedTiers.includes(dailyQuestion.tier)) {
    return res.status(403).json({
      success: false,
      errors: [{ msg: 'Premium subscription required to access today\'s question' }]
    });
  }

  // Check if user has already answered (if authenticated)
  let userResponse = null;
  let hasAnswered = false;

  if (req.user) {
    hasAnswered = dailyQuestion.hasUserAnswered(req.user._id);
    if (hasAnswered) {
      userResponse = dailyQuestion.getUserResponse(req.user._id);
    }
  }

  // Prepare response data
  const responseData = {
    id: dailyQuestion._id,
    date: dailyQuestion.date,
    tier: dailyQuestion.tier,
    isToday: dailyQuestion.isToday,
    hasAnswered,
    userResponse: userResponse ? {
      selectedOptionId: userResponse.selectedOptionId,
      isCorrect: userResponse.isCorrect,
      timeSpent: userResponse.timeSpent,
      answeredAt: userResponse.answeredAt
    } : null,
    question: {
      id: dailyQuestion.question._id,
      questionText: dailyQuestion.question.questionText,
      options: dailyQuestion.question.options.map(option => ({
        id: option._id,
        text: option.text,
        // Only show correct answer if user has answered
        isCorrect: hasAnswered ? option.isCorrect : undefined
      })),
      difficulty: dailyQuestion.question.difficulty,
      category: dailyQuestion.question.category,
      subject: dailyQuestion.question.subject,
      // Only show explanation if user has answered
      explanation: hasAnswered ? dailyQuestion.question.explanation : undefined
    },
    stats: {
      totalAttempts: dailyQuestion.stats.totalAttempts,
      correctAttempts: dailyQuestion.stats.correctAttempts,
      uniqueUsers: dailyQuestion.stats.uniqueUsers,
      accuracyPercentage: dailyQuestion.accuracyPercentage
    }
  };

  res.status(200).json({
    success: true,
    data: { dailyQuestion: responseData }
  });
});

// @desc    Get daily question for specific date
// @route   GET /api/daily-questions/date/:date
// @access  Public (with premium access check)
const getDailyQuestionForDate = asyncHandler(async (req, res) => {
  const { date } = req.params;

  const dailyQuestion = await getQuestionForDate(date);

  if (!dailyQuestion) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: 'No daily question found for this date' }]
    });
  }

  // Check if user has access to this question tier
  if (req.allowedTiers && !req.allowedTiers.includes(dailyQuestion.tier)) {
    return res.status(403).json({
      success: false,
      errors: [{ msg: 'Premium subscription required to access this question' }]
    });
  }

  // Check if user has answered (if authenticated)
  let userResponse = null;
  let hasAnswered = false;

  if (req.user) {
    hasAnswered = dailyQuestion.hasUserAnswered(req.user._id);
    if (hasAnswered) {
      userResponse = dailyQuestion.getUserResponse(req.user._id);
    }
  }

  const responseData = {
    id: dailyQuestion._id,
    date: dailyQuestion.date,
    tier: dailyQuestion.tier,
    hasAnswered,
    userResponse: userResponse ? {
      selectedOptionId: userResponse.selectedOptionId,
      isCorrect: userResponse.isCorrect,
      timeSpent: userResponse.timeSpent,
      answeredAt: userResponse.answeredAt
    } : null,
    question: {
      id: dailyQuestion.question._id,
      questionText: dailyQuestion.question.questionText,
      options: dailyQuestion.question.options.map(option => ({
        id: option._id,
        text: option.text,
        isCorrect: hasAnswered ? option.isCorrect : undefined
      })),
      difficulty: dailyQuestion.question.difficulty,
      category: dailyQuestion.question.category,
      subject: dailyQuestion.question.subject,
      explanation: hasAnswered ? dailyQuestion.question.explanation : undefined
    },
    stats: {
      totalAttempts: dailyQuestion.stats.totalAttempts,
      correctAttempts: dailyQuestion.stats.correctAttempts,
      uniqueUsers: dailyQuestion.stats.uniqueUsers,
      accuracyPercentage: dailyQuestion.accuracyPercentage
    }
  };

  res.status(200).json({
    success: true,
    data: { dailyQuestion: responseData }
  });
});

// @desc    Get user's daily question history
// @route   GET /api/daily-questions/history
// @access  Private
const getUserHistory = asyncHandler(async (req, res) => {
  const { page = 1, limit = 30 } = req.query;

  const history = await getUserDailyHistory(req.user._id, { page, limit });

  res.status(200).json({
    success: true,
    data: { history }
  });
});

// @desc    Get daily question statistics
// @route   GET /api/daily-questions/admin/stats
// @access  Private (Admin only)
const getStatistics = asyncHandler(async (req, res) => {
  const { startDate, endDate, tier } = req.query;

  const stats = await getDailyQuestionStats({ startDate, endDate, tier });

  res.status(200).json({
    success: true,
    data: { stats }
  });
});

// @desc    Get daily question analytics
// @route   GET /api/daily-questions/admin/analytics
// @access  Private (Admin only)
const getAnalytics = asyncHandler(async (req, res) => {
  const { days = 30 } = req.query;

  const analytics = await getDailyQuestionAnalytics(parseInt(days));

  res.status(200).json({
    success: true,
    data: { analytics }
  });
});

// @desc    Create daily questions for date range
// @route   POST /api/daily-questions/admin/create-range
// @access  Private (Admin only)
const createDailyQuestionsRange = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.body;

  const createdQuestions = await createDailyQuestionsForRange(startDate, endDate);

  res.status(201).json({
    success: true,
    data: { 
      message: `Created ${createdQuestions.length} daily questions`,
      createdQuestions 
    }
  });
});

// @desc    Manually update today's daily question
// @route   POST /api/daily-questions/admin/update-today
// @access  Private (Admin only)
const updateTodaysDailyQuestion = asyncHandler(async (req, res) => {
  const dailyQuestion = await updateDailyQuestion();

  if (!dailyQuestion) {
    return res.status(500).json({
      success: false,
      errors: [{ msg: 'Failed to update daily question' }]
    });
  }

  res.status(200).json({
    success: true,
    data: { 
      message: 'Daily question updated successfully',
      dailyQuestion: {
        id: dailyQuestion._id,
        date: dailyQuestion.date,
        tier: dailyQuestion.tier
      }
    }
  });
});

module.exports = {
  getDailyQuestion,
  submitDailyAnswer,
  getDailyStats,
  getDailyHistory,
  getDailyLeaderboard,
  getTodaysDailyQuestion,
  getDailyQuestionForDate,
  getUserHistory,
  getStatistics,
  getAnalytics,
  createDailyQuestionsRange,
  updateTodaysDailyQuestion
};
