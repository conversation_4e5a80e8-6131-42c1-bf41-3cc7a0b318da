const mongoose = require("mongoose");

const topicSchema = new mongoose.Schema(
  {
    topicName: {
      type: String,
      required: true,
      trim: true,
      maxlength: [100, "Topic name cannot exceed 100 characters"],
      index: true,
    },
    description: {
      type: String,
      trim: true,
    },
    subject: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subject",
      required: true,
      index: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPremium: {
      type: Boolean,
      default: false,
    },
    // Cached statistics for performance
    stats: {
      // Question Statistics
      totalQuestions: {
        type: Number,
        default: 0,
      },
      freeQuestions: {
        type: Number,
        default: 0,
      },
      premiumQuestions: {
        type: Number,
        default: 0,
      },
      activeQuestions: {
        type: Number,
        default: 0,
      },
      inactiveQuestions: {
        type: Number,
        default: 0,
      },

      // Difficulty Statistics
      averageDifficulty: {
        type: Number,
        default: 0,
        min: 0,
        max: 3, // 1=easy, 2=medium, 3=hard
      },
      difficultyDistribution: {
        easy: { type: Number, default: 0 },
        medium: { type: Number, default: 0 },
        hard: { type: Number, default: 0 },
      },

      // Engagement Statistics
      totalAttempts: {
        type: Number,
        default: 0,
      },
      averageScore: {
        type: Number,
        default: 0,
        min: 0,
        max: 100,
      },
      completionRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 100,
      },

      // Performance Metrics
      popularityScore: {
        type: Number,
        default: 0,
      },
      lastActivityDate: {
        type: Date,
        default: null,
      },

      // Cache timestamp
      lastCalculated: {
        type: Date,
        default: Date.now,
      },
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual fields for calculated statistics
topicSchema.virtual("questionSuccessRate").get(function () {
  if (this.stats.totalAttempts === 0) return 0;
  return Math.round(this.stats.averageScore);
});

topicSchema.virtual("difficultyLevel").get(function () {
  const avg = this.stats.averageDifficulty;
  if (avg <= 1.33) return "easy";
  if (avg <= 2.33) return "medium";
  return "hard";
});

topicSchema.virtual("engagementLevel").get(function () {
  const score = this.stats.popularityScore;
  if (score >= 80) return "high";
  if (score >= 40) return "medium";
  return "low";
});

topicSchema.virtual("isStatsStale").get(function () {
  if (!this.stats.lastCalculated) return true;
  const hoursSinceUpdate =
    (Date.now() - this.stats.lastCalculated) / (1000 * 60 * 60);
  return hoursSinceUpdate > 24; // Consider stale after 24 hours
});

// Pre-save middleware
topicSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Indexes for efficient queries
topicSchema.index({ topicName: 1, subject: 1 }, { unique: true });
topicSchema.index({ isActive: 1, isPremium: 1 });
topicSchema.index({ "stats.popularityScore": -1 });
topicSchema.index({ "stats.lastActivityDate": -1 });

// Method to calculate comprehensive statistics
topicSchema.methods.calculateStats = async function () {
  const Question = mongoose.model("Question");
  const User = mongoose.model("User");

  try {
    // Get all questions for this topic
    const questions = await Question.find({ topic: this._id });

    // Basic question statistics
    const totalQuestions = questions.length;
    const freeQuestions = questions.filter((q) => q.tier === "free").length;
    const premiumQuestions = questions.filter(
      (q) => q.tier === "premium"
    ).length;
    const activeQuestions = questions.filter((q) => q.isActive).length;
    const inactiveQuestions = questions.filter((q) => !q.isActive).length;

    // Difficulty statistics
    const difficultyDistribution = {
      easy: questions.filter((q) => q.difficulty === "easy").length,
      medium: questions.filter((q) => q.difficulty === "medium").length,
      hard: questions.filter((q) => q.difficulty === "hard").length,
    };

    const difficultyValues = { easy: 1, medium: 2, hard: 3 };
    const averageDifficulty =
      totalQuestions > 0
        ? questions.reduce(
            (sum, q) => sum + difficultyValues[q.difficulty],
            0
          ) / totalQuestions
        : 0;

    // Engagement statistics from questions
    const totalAttempts = questions.reduce((sum, q) => sum + q.usageCount, 0);
    const totalCorrectAnswers = questions.reduce(
      (sum, q) => sum + q.correctAnswerCount,
      0
    );
    const averageScore =
      totalAttempts > 0 ? (totalCorrectAnswers / totalAttempts) * 100 : 0;

    // Calculate completion rate from user quiz/test history
    const completionRate = await this.calculateCompletionRate();

    // Calculate popularity score based on recent activity
    const popularityScore = await this.calculatePopularityScore(questions);

    // Find last activity date
    const lastActivityDate = await this.getLastActivityDate();

    // Update stats
    this.stats = {
      totalQuestions,
      freeQuestions,
      premiumQuestions,
      activeQuestions,
      inactiveQuestions,
      averageDifficulty: Math.round(averageDifficulty * 100) / 100,
      difficultyDistribution,
      totalAttempts,
      averageScore: Math.round(averageScore * 100) / 100,
      completionRate: Math.round(completionRate * 100) / 100,
      popularityScore: Math.round(popularityScore * 100) / 100,
      lastActivityDate,
      lastCalculated: new Date(),
    };

    return this.save();
  } catch (error) {
    console.error("Error calculating topic stats:", error);
    throw error;
  }
};

// Method to calculate completion rate
topicSchema.methods.calculateCompletionRate = async function () {
  const User = mongoose.model("User");
  const Question = mongoose.model("Question");

  try {
    const totalQuestions = await Question.countDocuments({
      topic: this._id,
      isActive: true,
    });
    if (totalQuestions === 0) return 0;

    // Get users who have attempted questions from this topic
    const usersWithAttempts = await User.aggregate([
      { $match: { "quizHistory.0": { $exists: true } } },
      { $unwind: "$quizHistory" },
      {
        $lookup: {
          from: "questions",
          localField: "quizHistory.quiz",
          foreignField: "topic",
          as: "topicQuestions",
        },
      },
      { $match: { "topicQuestions.topic": this._id } },
      {
        $group: {
          _id: "$_id",
          questionsAttempted: { $sum: 1 },
        },
      },
      {
        $match: {
          questionsAttempted: { $gte: totalQuestions },
        },
      },
    ]);

    const totalUsers = await User.countDocuments({
      "quizHistory.0": { $exists: true },
    });
    return totalUsers > 0 ? (usersWithAttempts.length / totalUsers) * 100 : 0;
  } catch (error) {
    console.error("Error calculating completion rate:", error);
    return 0;
  }
};

// Method to calculate popularity score
topicSchema.methods.calculatePopularityScore = async function (
  questions = null
) {
  try {
    if (!questions) {
      const Question = mongoose.model("Question");
      questions = await Question.find({ topic: this._id });
    }

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Base score from question count (0-30 points)
    const questionScore = Math.min(questions.length * 2, 30);

    // Usage score from recent attempts (0-40 points)
    const recentAttempts = questions.reduce((sum, q) => {
      // Assuming usageCount includes recent activity
      return sum + q.usageCount;
    }, 0);
    const usageScore = Math.min(recentAttempts / 10, 40);

    // Success rate score (0-30 points)
    const totalAttempts = questions.reduce((sum, q) => sum + q.usageCount, 0);
    const totalCorrect = questions.reduce(
      (sum, q) => sum + q.correctAnswerCount,
      0
    );
    const successRate = totalAttempts > 0 ? totalCorrect / totalAttempts : 0;
    const successScore = successRate * 30;

    return questionScore + usageScore + successScore;
  } catch (error) {
    console.error("Error calculating popularity score:", error);
    return 0;
  }
};

// Method to get last activity date
topicSchema.methods.getLastActivityDate = async function () {
  const Question = mongoose.model("Question");

  try {
    const questions = await Question.find({ topic: this._id })
      .sort({ updatedAt: -1 })
      .limit(1);
    if (questions.length === 0) return this.updatedAt;

    // Return the most recent between question update and topic update
    return questions[0].updatedAt > this.updatedAt
      ? questions[0].updatedAt
      : this.updatedAt;
  } catch (error) {
    console.error("Error getting last activity date:", error);
    return this.updatedAt;
  }
};

// Method to refresh stats if stale
topicSchema.methods.refreshStatsIfNeeded = async function () {
  if (this.isStatsStale) {
    await this.calculateStats();
  }
  return this;
};

// Static method to get topics with fresh stats
topicSchema.statics.getWithStats = async function (filter = {}, options = {}) {
  const topics = await this.find(filter, null, options).populate(
    "subject",
    "name"
  );

  // Refresh stale stats in background
  const staleTopics = topics.filter((topic) => topic.isStatsStale);
  if (staleTopics.length > 0) {
    // Don't await - update in background
    Promise.all(staleTopics.map((topic) => topic.calculateStats())).catch(
      console.error
    );
  }

  return topics;
};

// Static method to calculate aggregate stats for admin dashboard
topicSchema.statics.getAggregateStats = async function () {
  try {
    const stats = await this.aggregate([
      {
        $group: {
          _id: null,
          totalTopics: { $sum: 1 },
          activeTopics: { $sum: { $cond: ["$isActive", 1, 0] } },
          inactiveTopics: { $sum: { $cond: ["$isActive", 0, 1] } },
          premiumTopics: { $sum: { $cond: ["$isPremium", 1, 0] } },
          freeTopics: { $sum: { $cond: ["$isPremium", 0, 1] } },
          totalQuestions: { $sum: "$stats.totalQuestions" },
          freeQuestions: { $sum: "$stats.freeQuestions" },
          premiumQuestions: { $sum: "$stats.premiumQuestions" },
          totalAttempts: { $sum: "$stats.totalAttempts" },
          averagePopularity: { $avg: "$stats.popularityScore" },
        },
      },
    ]);

    return (
      stats[0] || {
        totalTopics: 0,
        activeTopics: 0,
        inactiveTopics: 0,
        premiumTopics: 0,
        freeTopics: 0,
        totalQuestions: 0,
        freeQuestions: 0,
        premiumQuestions: 0,
        totalAttempts: 0,
        averagePopularity: 0,
      }
    );
  } catch (error) {
    console.error("Error calculating aggregate stats:", error);
    throw error;
  }
};

// Post-save hook to update subject stats
topicSchema.post("save", async function (doc) {
  const Subject = require("./Subject");
  if (doc.subject) {
    await Subject.findByIdAndUpdate(doc.subject, {
      $addToSet: { topics: doc._id },
    });

    // Update subject stats
    const subject = await Subject.findById(doc.subject);
    if (subject && subject.updateQuestionStats) {
      await subject.updateQuestionStats();
    }
  }
});

// Post-remove hook
topicSchema.post("remove", async function (doc) {
  const Subject = require("./Subject");
  if (doc.subject) {
    await Subject.findByIdAndUpdate(doc.subject, {
      $pull: { topics: doc._id },
    });

    // Update subject stats
    const subject = await Subject.findById(doc.subject);
    if (subject && subject.updateQuestionStats) {
      await subject.updateQuestionStats();
    }
  }
});

const Topic = mongoose.model("Topic", topicSchema);

module.exports = Topic;
