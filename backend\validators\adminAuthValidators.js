const { check, validationResult } = require("express-validator");

// Validation middleware to check for errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array(),
    });
  }
  next();
};

// Admin login validation
const validateAdminLogin = [
  check("email", "Please include a valid email")
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email address"),
  check("password", "Password is required")
    .exists()
    .notEmpty()
    .withMessage("Password cannot be empty"),
  check("password", "Password must be at least 6 characters")
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters long"),
  handleValidationErrors,
];

// Admin refresh token validation
const validateAdminRefreshToken = [
  check("refreshToken", "Refresh token is required")
    .exists()
    .notEmpty()
    .withMessage("Refresh token cannot be empty"),
  check("refreshToken", "Invalid refresh token format")
    .isJWT()
    .withMessage("Refresh token must be a valid JWT"),
  handleValidationErrors,
];

// Admin password change validation (if you want to add this endpoint later)
const validateAdminPasswordChange = [
  check("currentPassword", "Current password is required")
    .exists()
    .notEmpty()
    .withMessage("Current password cannot be empty"),
  check("newPassword", "New password must be at least 8 characters")
    .isLength({ min: 8 })
    .withMessage("New password must be at least 8 characters long"),
  check("newPassword", "New password must contain at least one number")
    .matches(/\d/)
    .withMessage("New password must contain at least one number"),
  check("newPassword", "New password must contain at least one uppercase letter")
    .matches(/[A-Z]/)
    .withMessage("New password must contain at least one uppercase letter"),
  check("newPassword", "New password must contain at least one lowercase letter")
    .matches(/[a-z]/)
    .withMessage("New password must contain at least one lowercase letter"),
  check("newPassword", "New password must contain at least one special character")
    .matches(/[!@#$%^&*(),.?":{}|<>]/)
    .withMessage("New password must contain at least one special character"),
  check("confirmPassword", "Password confirmation is required")
    .exists()
    .notEmpty()
    .withMessage("Password confirmation cannot be empty"),
  check("confirmPassword")
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error("Password confirmation does not match new password");
      }
      return true;
    }),
  handleValidationErrors,
];

// Admin profile update validation (if you want to add this endpoint later)
const validateAdminProfileUpdate = [
  check("name", "Name must be between 2 and 50 characters")
    .optional()
    .isLength({ min: 2, max: 50 }),
  check("name", "Name contains invalid characters")
    .optional()
    .trim()
    .escape(),
  check("email", "Please provide a valid email address")
    .optional()
    .isEmail()
    .normalizeEmail(),
  check("preferences", "Preferences must be a valid object")
    .optional()
    .isObject(),
  check("preferences.notifications", "Notifications preference must be true or false")
    .optional()
    .isBoolean(),
  check("preferences.dailyReminder", "Daily reminder preference must be true or false")
    .optional()
    .isBoolean(),
  check("preferences.theme", "Theme must be one of: light, dark, auto")
    .optional()
    .isIn(["light", "dark", "auto"]),
  handleValidationErrors,
];

// Admin user creation validation (if you want to add this endpoint later)
const validateAdminUserCreation = [
  check("name", "Name cannot be empty")
    .not()
    .isEmpty(),
  check("name", "Name must be between 2 and 50 characters")
    .isLength({ min: 2, max: 50 }),
  check("email", "Please provide a valid email address")
    .isEmail()
    .normalizeEmail(),
  check("password", "Password must be at least 8 characters long")
    .isLength({ min: 8 }),
  check("password", "Password must contain at least one number")
    .matches(/\d/),
  check("password", "Password must contain at least one uppercase letter")
    .matches(/[A-Z]/),
  check("password", "Password must contain at least one lowercase letter")
    .matches(/[a-z]/),
  check("role", "Role must be either 'user' or 'admin'")
    .optional()
    .isIn(["user", "admin"]),
  check("isPremium", "isPremium must be true or false")
    .optional()
    .isBoolean(),
  handleValidationErrors,
];

// Validation for admin session management (if you want to add this)
const validateAdminSessionId = [
  check("sessionId", "Session ID cannot be empty")
    .exists()
    .notEmpty(),
  check("sessionId", "Session ID must be a valid MongoDB ObjectId")
    .isMongoId(),
  handleValidationErrors,
];

module.exports = {
  validateAdminLogin,
  validateAdminRefreshToken,
  validateAdminPasswordChange,
  validateAdminProfileUpdate,
  validateAdminUserCreation,
  validateAdminSessionId,
  handleValidationErrors,
};
