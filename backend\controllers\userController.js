const { validationResult } = require('express-validator');
const User = require('../models/User');
const bcrypt = require('bcryptjs');

// Get all users (admin only)
exports.getUsers = async (req, res) => {
  try {
    const users = await User.find()
      .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching users' }]
    });
  }
};

// Get single user
exports.getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching user' }]
    });
  }
};

// Update user
exports.updateUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { firstName, lastName, email, role, tier } = req.body;
    const updateData = {};

    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (email) updateData.email = email;
    if (role) updateData.role = role;
    if (tier) updateData.tier = tier;

    const user = await User.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while updating user' }]
    });
  }
};

// Delete user
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    res.json({
      success: true,
      data: { message: 'User deleted successfully' }
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while deleting user' }]
    });
  }
};

// Get user statistics
exports.getUserStats = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('stats quizHistory testHistory');

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    res.json({
      success: true,
      data: {
        stats: user.stats,
        quizHistory: user.quizHistory,
        testHistory: user.testHistory
      }
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching user statistics' }]
    });
  }
};

// Get user analytics (admin only)
exports.getUserAnalytics = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('stats quizHistory testHistory dailyQuestions');

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    // Calculate analytics
    const analytics = {
      totalQuizzes: user.quizHistory.length,
      totalTests: user.testHistory.length,
      totalDailyQuestions: user.dailyQuestions.length,
      averageQuizScore: user.stats.averageQuizScore || 0,
      averageTestScore: user.stats.averageTestScore || 0,
      dailyQuestionAccuracy: user.stats.dailyQuestionAccuracy || 0,
      lastActive: user.stats.lastActive,
      subscriptionStatus: user.subscription.status,
      subscriptionPlan: user.subscription.plan
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Get user analytics error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching user analytics' }]
    });
  }
};

// Get user progress
exports.getUserProgress = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('stats quizHistory testHistory dailyQuestions');

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    // Calculate progress
    const progress = {
      totalQuestionsAnswered: user.stats.questionsAnswered,
      totalCorrectAnswers: user.stats.correctAnswers,
      quizzesCompleted: user.stats.quizzesCompleted,
      testsCompleted: user.stats.testsCompleted,
      dailyQuestionsAnswered: user.stats.dailyQuestionsAnswered,
      dailyQuestionsCorrect: user.stats.dailyQuestionsCorrect,
      overallAccuracy: user.stats.overallAccuracy || 0,
      lastActive: user.stats.lastActive
    };

    res.json({
      success: true,
      data: progress
    });
  } catch (error) {
    console.error('Get user progress error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching user progress' }]
    });
  }
};

// Get user history
exports.getUserHistory = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('quizHistory testHistory dailyQuestions')
      .populate('quizHistory.quiz', 'title subject')
      .populate('testHistory.test', 'title subject')
      .populate('dailyQuestions.question', 'text subject');

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: 'User not found' }]
      });
    }

    res.json({
      success: true,
      data: {
        quizHistory: user.quizHistory,
        testHistory: user.testHistory,
        dailyQuestions: user.dailyQuestions
      }
    });
  } catch (error) {
    console.error('Get user history error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching user history' }]
    });
  }
};

// Get user leaderboard
exports.getUserLeaderboard = async (req, res) => {
  try {
    const users = await User.find()
      .select('firstName lastName stats')
      .sort({ 'stats.overallScore': -1 })
      .limit(10);

    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching leaderboard' }]
    });
  }
};

// Update user preferences
exports.updateUserPreferences = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { emailNotifications, pushNotifications, theme } = req.body;
    const updateData = {
      preferences: {
        ...req.user.preferences,
        emailNotifications,
        pushNotifications,
        theme
      }
    };

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true }
    ).select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update preferences error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while updating preferences' }]
    });
  }
};

// Update user subscription
exports.updateUserSubscription = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { status, plan, startDate, endDate } = req.body;
    const updateData = {
      subscription: {
        status,
        plan,
        startDate,
        endDate
      }
    };

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true }
    ).select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update subscription error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while updating subscription' }]
    });
  }
};

// Reset user password
exports.resetUserPassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Get user with password
    const user = await User.findById(req.user._id).select('+password');

    // Check current password
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: 'Current password is incorrect' }]
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      data: { message: 'Password reset successfully' }
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while resetting password' }]
    });
  }
};

// Deactivate user account
exports.deactivateUser = async (req, res) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.user._id,
      { isActive: false },
      { new: true }
    ).select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    res.json({
      success: true,
      data: { message: 'Account deactivated successfully' }
    });
  } catch (error) {
    console.error('Deactivate account error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while deactivating account' }]
    });
  }
};

// Reactivate user account
exports.reactivateUser = async (req, res) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.user._id,
      { isActive: true },
      { new: true }
    ).select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    res.json({
      success: true,
      data: { message: 'Account reactivated successfully' }
    });
  } catch (error) {
    console.error('Reactivate account error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while reactivating account' }]
    });
  }
};

// Get user profile
exports.getProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching profile' }]
    });
  }
};

// Update user profile
exports.updateProfile = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { name, preferences } = req.body;
    const updateData = {};

    if (name) updateData.name = name;
    if (preferences) {
      updateData.preferences = {
        ...req.user.preferences,
        ...preferences
      };
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while updating profile' }]
    });
  }
};

// Change password
exports.changePassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;

    // Get user with password
    const user = await User.findById(req.user._id).select('+password');

    // Check current password
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: 'Current password is incorrect' }]
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      data: { message: 'Password updated successfully' }
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while changing password' }]
    });
  }
};

// Update user avatar
exports.updateAvatar = async (req, res) => {
  try {
    const { avatarUrl } = req.body;

    if (!avatarUrl) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: 'Avatar URL is required' }]
      });
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { avatar: avatarUrl },
      { new: true }
    ).select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update avatar error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while updating avatar' }]
    });
  }
};

// Delete user account
exports.deleteAccount = async (req, res) => {
  try {
    const { password } = req.body;

    // Verify password
    const user = await User.findById(req.user._id).select('+password');
    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: 'Password is incorrect' }]
      });
    }

    // Delete user
    await User.findByIdAndDelete(req.user._id);

    res.json({
      success: true,
      data: { message: 'Account deleted successfully' }
    });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while deleting account' }]
    });
  }
}; 