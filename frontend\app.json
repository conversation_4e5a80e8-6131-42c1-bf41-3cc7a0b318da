{"expo": {"name": "PhysioPrep", "slug": "physioprep", "version": "1.0.0", "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "experiments": {"tsconfigPaths": true}, "plugins": ["expo-router"], "orientation": "portrait", "scheme": "physioprep", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.darshanx01.physioprep"}, "extra": {"router": {}, "eas": {"projectId": "077a1a87-f7d3-4b1b-b823-34fc8b4f69f4"}}}}