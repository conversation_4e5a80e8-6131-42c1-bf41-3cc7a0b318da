"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Topic } from "@/types/types";
import { TopicAdminStats } from "../actions";

interface TopicsStatsProps {
  topics: Topic[];
  adminStats: TopicAdminStats | null;
}

export function TopicsStats({ topics, adminStats }: TopicsStatsProps) {
  const stats = adminStats || {
    totalTopics: topics.length,
    activeTopics: topics.filter(topic => topic.isActive).length,
    inactiveTopics: topics.filter(topic => !topic.isActive).length,
    premiumTopics: topics.filter(topic => topic.isPremium).length,
    freeTopics: topics.filter(topic => !topic.isPremium).length,
    totalQuestions: 0,
    freeQuestions: 0,
    premiumQuestions: 0,
    totalAttempts: 0,
    averagePopularity: 0,
  };

  const statCards = [
    {
      title: "Total Topics",
      value: stats.totalTopics,
      description: "All topics in the system",
    },
    {
      title: "Active Topics",
      value: stats.activeTopics,
      description: "Currently active topics",
    },
    {
      title: "Inactive Topics",
      value: stats.inactiveTopics,
      description: "Topics currently inactive",
    },
    {
      title: "Premium Topics",
      value: stats.premiumTopics,
      description: "Premium tier topics",
    },
    {
      title: "Free Topics",
      value: stats.freeTopics,
      description: "Free tier topics",
    },
    {
      title: "Total Questions",
      value: stats.totalQuestions,
      description: adminStats
        ? `${stats.freeQuestions} free, ${stats.premiumQuestions} premium`
        : "Questions across all topics",
    },
    {
      title: "Total Attempts",
      value: stats.totalAttempts,
      description: "Total attempts across all questions",
    },
    {
      title: "Average Popularity",
      value: stats.averagePopularity.toFixed(2),
      description: "Average popularity score",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}