import { View, Text, ScrollView } from 'react-native';
import { Button } from '../ui/button';
import { Ionicons } from '@expo/vector-icons';
import colors from 'tailwindcss/colors';
import { LinearGradient } from 'expo-linear-gradient';

interface Option {
  text: string;
  isCorrect: boolean;
}

interface Question {
  text: string;
  options: Option[];
  explanation: string;
  difficulty?: string;
}

interface QuizReviewProps {
  questions: { question: Question; timeSpent?: number; isCorrect?: boolean; userAnswer?: number }[];
  userAnswers: number[];
  onBack?: () => void;
  totalTime: number;
}

const formatSeconds = (seconds: number) => {
  const m = Math.floor(seconds / 60);
  const s = seconds % 60;
  return `${m}:${s.toString().padStart(2, '0')}`;
};

const QuizReview = ({ questions, userAnswers, onBack, totalTime }: QuizReviewProps) => {
  // Calculate summary
  const correctCount = questions.reduce((sum, q, i) => sum + (q.isCorrect ? 1 : 0), 0);
  const incorrectCount = questions.length - correctCount;
  const score = Math.round((correctCount / questions.length) * 100);

  return (
    <View className="flex-1 bg-background">
      <ScrollView className="  p-4">
        {/* Summary */}
        <View className="mb-6 flex-row items-center justify-between rounded-2xl bg-indigo-600 p-4 shadow">
          <View className="flex-1">
            <Text className="mb-1 text-2xl font-bold text-primary">Quiz Review</Text>
            <View className="mb-1 flex-row items-center">
              <Ionicons name="timer-outline" size={18} color={colors.amber[500]} />
              <Text className="ml-2 text-base text-amber-500">
                Total Time: <Text className="font-semibold">{formatSeconds(totalTime)}</Text>
              </Text>
            </View>
            <View className="mb-1 flex-row items-center">
              <Ionicons name="checkmark-circle" size={18} color="#22c55e" />
              <Text className="ml-2 text-base text-green-600">
                Correct: <Text className="font-semibold">{correctCount}</Text>
              </Text>
              <Ionicons name="close-circle" size={18} color="#ef4444" style={{ marginLeft: 12 }} />
              <Text className="ml-2 text-base text-red-500">
                Incorrect: <Text className="font-semibold">{incorrectCount}</Text>
              </Text>
            </View>
            <View className="flex-row items-center">
              <Ionicons name="trophy-outline" size={18} color={colors.pink[400]} />
              <Text className="ml-2 text-base text-pink-400">
                Score: <Text className="font-semibold ">{score}%</Text>
              </Text>
            </View>
          </View>
        </View>
        {/* Questions */}
        {questions.map((qObj, idx) => {
          const q = qObj.question;
          const userIdx = typeof qObj.userAnswer === 'number' ? qObj.userAnswer : userAnswers[idx];
          const correctIdx = q.options.findIndex((o) => o.isCorrect);
          const isCorrect = qObj.isCorrect ?? userIdx === correctIdx;
          return (
            <LinearGradient
              colors={[colors.indigo[400], colors.indigo[600], colors.indigo[700]]}
              locations={[0, 0.5, 1]}
              start={{ x: 0, y: 0 }}
              key={idx}
              className="mb-6 overflow-hidden rounded-2xl bg-indigo-400/70 p-4 shadow-xl shadow-white ">
              <View className="mb-2 flex-row items-center">
                <Text className="flex-1 font-bold text-lg text-primary ">
                  <Text className='font-bold text-2xl leading-6 ' >{idx + 1}. </Text>
                  {q.text}
                </Text>
                <View
                  className={`ml-2 rounded-full border px-2 py-1 ${isCorrect ? 'border-green-100 bg-green-100' : 'border-red-100 bg-red-100'}`}>
                  <Text
                    className={`text-xs font-bold ${isCorrect ? 'text-green-600' : 'text-red-500'}`}>
                    {isCorrect ? 'Correct' : 'Incorrect'}
                  </Text>
                </View>
              </View>
              {q.options.map((opt, oIdx) => {
                const isUser = userIdx === oIdx;
                const isOptionCorrect = opt.isCorrect;
                return (
                  <View
                    key={oIdx}
                    className={`mb-2 flex-row items-center rounded-lg border px-3 py-2 ${
                      isOptionCorrect
                        ? 'border-green-500 bg-green-50'
                        : isUser
                          ? 'border-red-500 bg-red-50'
                          : 'border-gray-200 bg-white'
                    }`}>
                    <Ionicons
                      name={
                        isOptionCorrect
                          ? 'checkmark-circle'
                          : isUser
                            ? 'close-circle'
                            : 'ellipse-outline'
                      }
                      size={18}
                      color={isOptionCorrect ? '#22c55e' : isUser ? '#ef4444' : '#a3a3a3'}
                      style={{ marginRight: 8 }}
                    />
                    <Text className="flex-1 text-base">{opt.text}</Text>
                    {isUser && !isOptionCorrect && (
                      <Text className="ml-2 text-xs text-red-500">Your answer</Text>
                    )}
                    {isOptionCorrect && (
                      <Text className="ml-2 text-xs text-green-600">Correct</Text>
                    )}
                  </View>
                );
              })}
              {/* <View className="mt-2 flex-row items-center">
                <Ionicons name="time-outline" size={16} color="#6366F1" />
                <Text className="ml-2 text-sm text-gray-600">
                  Time Spent:{' '}
                  <Text className="font-semibold">{formatSeconds(qObj.timeSpent || 0)}</Text>
                </Text>
              </View> */}
              <Text className="mt-2 text-sm text-slate-200">Explanation: {q.explanation}</Text>
            </LinearGradient>
          );
        })}
      </ScrollView>
      {onBack && (
        <View className="px-6">
          <Button title="Back" onPress={onBack} className="my-4" />
        </View>
      )}
    </View>
  );
};

export default QuizReview;
