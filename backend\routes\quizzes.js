const express = require("express");
const router = express.Router();
const { body, param } = require("express-validator");

// Import controllers
const {
  getQuizzes,
  getQuiz,
  createQuiz,
  updateQuiz,
  deleteQuiz,
  getQuizStats,
  getQuizAnalytics,
  getQuizQuestions,
  getQuizLeaderboard,
  getQuizProgress,
  submitQuiz,
  getQuizResults,
  getQuizHistory,
  getQuizRecommendations,
} = require("../controllers/quizController");

// Import middleware
const {
  auth,
  admin,
  premium,
  checkPremiumAccess,
} = require("../middleware/auth");

const {
  validateCreateQuiz,
  validateUpdateQuiz,
  validateSubmitQuiz,
} = require("../validators/quizValidators");

// Public routes
router.get("/", getQuizzes);
router.get("/recommendations", auth, getQuizRecommendations);
router.get("/:id", param("id").isMongoId(), getQuiz);
router.get(
  "/:id/stats",
  param("id").isMongoId(),

  getQuizStats
);
router.get(
  "/:id/questions",
  param("id").isMongoId(),

  getQuizQuestions
);
router.get(
  "/:id/leaderboard",
  param("id").isMongoId(),

  getQuizLeaderboard
);

// Admin routes
router.post(
  "/",
  auth,
  admin,
  validateCreateQuiz,

  createQuiz
);
router.put(
  "/:id",
  auth,
  admin,
  param("id").isMongoId(),
  validateUpdateQuiz,

  updateQuiz
);
router.delete(
  "/:id",
  auth,
  admin,
  param("id").isMongoId(),

  deleteQuiz
);

// User routes (requires authentication)
router.get(
  "/:id/progress",
  auth,
  param("id").isMongoId(),

  getQuizProgress
);
router.post(
  "/:id/submit",
  auth,
  param("id").isMongoId(),

  validateSubmitQuiz,

  submitQuiz
);
router.get(
  "/:id/results",
  auth,
  param("id").isMongoId(),

  getQuizResults
);
router.get(
  "/:id/history",
  auth,
  param("id").isMongoId(),

  getQuizHistory
);

// Premium routes
router.get(
  "/premium/:id",
  auth,
  premium,
  param("id").isMongoId(),

  getQuiz
);
router.post(
  "/premium/:id/submit",
  auth,
  premium,
  param("id").isMongoId(),
  validateSubmitQuiz,
  submitQuiz
);

// Start a subject-wise quiz for a user
router.post(
  "/subject/:subjectId/start",
  auth,
  param("subjectId").isMongoId(),

  require("../controllers/quizController").startSubjectQuiz
);
// Start a topic-wise quiz for a user
router.post(
  "/topic/:topicId/start",
  auth,
  param("topicId").isMongoId(),
  require("../controllers/quizController").startTopicQuiz
);

module.exports = router;
