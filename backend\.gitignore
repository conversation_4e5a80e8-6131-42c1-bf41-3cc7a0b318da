# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.keystore
*.log
*.crt
*.cer
*.der
*.bak
*.swp
*.swo
.idea/
.vscode/
*.iml
*.sublime-workspace
*.sublime-project
coverage/
build/
tmp/
temp/
.cache/
*.env
.env.*
.envrc
*.local
*.test.*
test-results/
*.pid
*.seed
*.sqlite
*.db
*.db-journal
*.tgz
*.gz
*.zip
*.tar
*.rar
*.7z
*.lock
package-lock.json
yarn.lock
pnpm-lock.yaml
npm-shrinkwrap.json
*.log.*
*.out
*.pid
*.pid.lock
*.sock
*.tmp
*.bak
*.old
*.orig
*.rej
*.swo
*.swp
*.swn
*.sublime-workspace
*.sublime-project
*.code-workspace
*.patch
*.diff
*.orig.*
*.rej
*.min.js
*.map
*.coverage
*.lcov
*.lcov.info
*.nyc_output
*.eslintcache
*.tsbuildinfo
*.rpt2_cache/
*.rts2_cache_cjs/
*.rts2_cache_es/
*.rts2_cache_umd/
*.next/
.next
out/
.storybook/
storybook-static/
cypress/videos/
cypress/screenshots/
cypress/results/
playwright-report/
test-results/
*.snap
*.spec.js
*.spec.ts
*.test.js
*.test.ts

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env
.vercel
.env.example



# typescript
*.tsbuildinfo

#others

