const express = require("express");
const router = express.Router();
const topicController = require("../controllers/topicController");
const { body } = require("express-validator");
const { optionalAuth } = require("../middleware/auth");

// Get all topics
router.get("/", optionalAuth, topicController.getTopics);

// Get topic by ID
router.get("/:id", optionalAuth, topicController.getTopic);

// Create topic
router.post(
  "/",
  [
    body("topicName").notEmpty().withMessage("Topic name is required"),
    body("subject").notEmpty().withMessage("Subject is required"),
  ],
  topicController.createTopic
);

// Update topic
router.put(
  "/:id",
  [
    body("topicName")
      .optional()
      .notEmpty()
      .withMessage("Topic name cannot be empty"),
    body("subject")
      .optional()
      .notEmpty()
      .withMessage("Subject cannot be empty"),
  ],
  topicController.updateTopic
);

// Delete topic (soft delete)
router.delete("/:id", topicController.deleteTopic);

module.exports = router;
