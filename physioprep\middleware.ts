import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;
  const headers = new Headers(request.headers);
  headers.set("x-current-path", request.nextUrl.pathname);

  // Check if the path is a protected route
  const isProtectedRoute =
    path.startsWith("/dashboard") && !path.includes("/login");

  // Get the isLoggedIn status from cookies
  const isLoggedIn = request.cookies.get("isLoggedIn")?.value === "true";
  const isAdmin = request.cookies.get("adminToken")?.value !== null;

  // If trying to access a protected route without being logged in
  if (isProtectedRoute && !isLoggedIn && !isAdmin) {
    // Redirect to the login page
    return NextResponse.redirect(new URL("/dashboard/login", request.url));
  }

  // If trying to access login page while already logged in
  if (path.includes("/login") && isLoggedIn && isAdmin) {
    // Redirect to the dashboard
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Continue with the request
  return NextResponse.next({ request: { headers } });
}

// Configure the middleware to run only on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)",
    "/dashboard/:path*",
  ]
};
