"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export async function signOut() {
  try {
    // Get the cookie store
    const cookieStore = await cookies();
    
    // Delete the cookies
    cookieStore.delete("isLoggedIn");
    cookieStore.delete("adminToken");
    cookieStore.delete("adminRefreshToken");
    
    // Force redirect to login page
    redirect("/dashboard/login");
    
  } catch (error) {
    console.error("Sign out error:", error);
    // Even if there's an error, redirect to login
    redirect("/dashboard/login");
  }
}