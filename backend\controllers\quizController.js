const { validationResult } = require("express-validator");
const { asyncHand<PERSON> } = require("../middleware/errorHandler");
const Quiz = require("../models/Quiz");
const Question = require("../models/Question");
const User = require("../models/User");
const Subject = require("../models/Subject");
const Topic = require("../models/Topic");

// @desc    Get all quizzes
// @route   GET /api/quizzes
// @access  Private
const getQuizzes = asyncHandler(async (req, res) => {
  const quizzes = await Quiz.find()
    .populate("subject", "name")
    .populate("topic", "name")
    .select("-questions.correctAnswer");

  res.json({
    success: true,
    data: quizzes,
  });
});

// @desc    Get quiz by ID
// @route   GET /api/quizzes/:id
// @access  Private
const getQuiz = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findById(req.params.id)
    .populate("subject", "name")
    .populate("topic", "topicName");

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  res.json({
    success: true,
    data: quiz,
  });
});

// @desc    Create new quiz
// @route   POST /api/quizzes
// @access  Private/Admin
const createQuiz = asyncHandler(async (req, res) => {
  const { quizType, topic, subject } = req.body;

  let questions;
  if (quizType === "subject") {
    questions = await Question.find({ topic: { $in: topic }, isActive: true })
      .populate("topic", "name")
      .select("-correctAnswer");
  } else if (quizType === "topic") {
    questions = await Question.find({ topic: topic, isActive: true })
      .populate("topic", "name")
      .select("-correctAnswer");
  } else {
    questions = await Question.find({ isActive: true })
      .populate("topic", "name")
      .select("-correctAnswer");
  }

  const quiz = await Quiz.create({
    quizType,
    topic,
    subject,
    questions,
    totalQuestions: questions.length,
    questionsAnswered: 0,
  });

  res.status(201).json({
    success: true,
    data: quiz,
  });
});

// @desc    Update quiz
// @route   PUT /api/quizzes/:id
// @access  Private/Admin
const updateQuiz = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  res.json({
    success: true,
    data: quiz,
  });
});

// @desc    Delete quiz
// @route   DELETE /api/quizzes/:id
// @access  Private/Admin
const deleteQuiz = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findByIdAndDelete(req.params.id);

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  res.json({
    success: true,
    data: {},
  });
});

// @desc    Get quiz statistics
// @route   GET /api/quizzes/:id/stats
// @access  Private
const getQuizStats = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findById(req.params.id);

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  // Get quiz statistics
  const stats = await User.aggregate([
    {
      $match: {
        "quizHistory.quiz": quiz._id,
      },
    },
    {
      $group: {
        _id: null,
        totalAttempts: { $sum: 1 },
        averageScore: { $avg: "$quizHistory.score" },
        averageTimeSpent: { $avg: "$quizHistory.timeSpent" },
        highestScore: { $max: "$quizHistory.score" },
        lowestScore: { $min: "$quizHistory.score" },
      },
    },
  ]);

  res.json({
    success: true,
    data: stats[0] || {
      totalAttempts: 0,
      averageScore: 0,
      averageTimeSpent: 0,
      highestScore: 0,
      lowestScore: 0,
    },
  });
});

// @desc    Get quiz analytics
// @route   GET /api/quizzes/:id/analytics
// @access  Private/Admin
const getQuizAnalytics = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findById(req.params.id);

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  // Get detailed analytics
  const analytics = await User.aggregate([
    {
      $match: {
        "quizHistory.quiz": quiz._id,
      },
    },
    {
      $unwind: "$quizHistory",
    },
    {
      $match: {
        "quizHistory.quiz": quiz._id,
      },
    },
    {
      $group: {
        _id: {
          date: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: "$quizHistory.completedAt",
            },
          },
        },
        attempts: { $sum: 1 },
        averageScore: { $avg: "$quizHistory.score" },
        averageTimeSpent: { $avg: "$quizHistory.timeSpent" },
      },
    },
    { $sort: { "_id.date": 1 } },
  ]);

  res.json({
    success: true,
    data: analytics,
  });
});

// @desc    Get quiz questions
// @route   GET /api/quizzes/:id/questions
// @access  Private
const getQuizQuestions = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findById(req.params.id)
    .select("questions")
    .populate("questions.question");

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  res.json({
    success: true,
    data: quiz.questions,
  });
});

// @desc    Get quiz leaderboard
// @route   GET /api/quizzes/:id/leaderboard
// @access  Private
const getQuizLeaderboard = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findById(req.params.id);

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  // Get top performers
  const leaderboard = await User.aggregate([
    {
      $match: {
        "quizHistory.quiz": quiz._id,
      },
    },
    {
      $unwind: "$quizHistory",
    },
    {
      $match: {
        "quizHistory.quiz": quiz._id,
      },
    },
    {
      $project: {
        firstName: 1,
        lastName: 1,
        score: "$quizHistory.score",
        timeSpent: "$quizHistory.timeSpent",
        completedAt: "$quizHistory.completedAt",
      },
    },
    { $sort: { score: -1, timeSpent: 1 } },
    { $limit: 10 },
  ]);

  res.json({
    success: true,
    data: leaderboard,
  });
});

// @desc    Get quiz progress
// @route   GET /api/quizzes/:id/progress
// @access  Private
const getQuizProgress = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findById(req.params.id);

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  const user = await User.findById(req.user._id)
    .select("quizHistory")
    .populate("quizHistory.quiz", "_id");

  // Find user's attempts for this quiz
  const attempts = user.quizHistory.filter(
    (attempt) => attempt.quiz._id.toString() === quiz._id.toString()
  );

  // Calculate progress
  const progress = {
    attempts: attempts.length,
    highestScore: Math.max(...attempts.map((a) => a.score), 0),
    averageScore:
      attempts.reduce((acc, a) => acc + a.score, 0) / attempts.length || 0,
    averageTimeSpent:
      attempts.reduce((acc, a) => acc + a.timeSpent, 0) / attempts.length || 0,
    lastAttempt: attempts[attempts.length - 1]?.completedAt || null,
  };

  res.json({
    success: true,
    data: progress,
  });
});

// @desc    Submit quiz
// @route   POST /api/quizzes/:id/submit
// @access  Private
const submitQuiz = asyncHandler(async (req, res) => {
  const { answers, timeSpent } = req.body;
  const quiz = await Quiz.findById(req.params.id).populate(
    "questions.question"
  );

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  // Calculate score
  let score = 0;
  const results = quiz.questions.map((question, index) => {
    // Find the index of the option where isCorrect is true

    const isCorrect =
      question.question.options[answers[index]]?.isCorrect || false;
    if (isCorrect) score++;
    return {
      questionId: question._id,
      isCorrect,
      correctAnswer: question.correctAnswer,
    };
  });

  // Update user stats
  const user = await User.findById(req.user._id);
  user.quizHistory.push({
    quiz: quiz._id,
    score,
    timeSpent,
    answers,
    completedAt: new Date(),
  });
  user.stats.totalQuizzesTaken++;
  user.stats.totalQuestionsAnswered += quiz.questions.length;
  user.stats.correctAnswers += score;
  // Calculate average score based on all quizzes taken
  user.stats.averageScore =
    user.quizHistory.reduce((acc, q) => acc + (q.score || 0), 0) /
    user.stats.totalQuizzesTaken;
  await user.save();

  res.json({
    success: true,
    data: {
      score,
      totalQuestions: quiz.questions.length,
      percentage: Math.round((score / quiz.questions.length) * 100),
      results,
    },
  });
});

// @desc    Get quiz results
// @route   GET /api/quizzes/:id/results
// @access  Private
const getQuizResults = asyncHandler(async (req, res) => {
  const quiz = await Quiz.findById(req.params.id);

  if (!quiz) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Quiz not found" }],
    });
  }

  const user = await User.findById(req.user._id)
    .select("quizHistory")
    .populate("quizHistory.quiz", "_id");

  // Find user's attempts for this quiz
  const attempts = user.quizHistory.filter(
    (attempt) => attempt.quiz._id.toString() === quiz._id.toString()
  );

  res.json({
    success: true,
    data: attempts,
  });
});

// @desc    Get quiz history
// @route   GET /api/quizzes/:id/history
// @access  Private
const getQuizHistory = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id)
    .select("quizHistory")
    .populate("quizHistory.quiz", "title subject topic");

  res.json({
    success: true,
    data: user.quizHistory,
  });
});

// @desc    Get quiz recommendations
// @route   GET /api/quizzes/recommendations
// @access  Private
const getQuizRecommendations = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id)
    .select("quizHistory subject")
    .populate("quizHistory.quiz", "subject topic");

  // Get user's completed quizzes
  const completedQuizzes = user.quizHistory.map((h) => h.quiz._id);

  // Find quizzes in user's subjects that haven't been completed
  const recommendations = await Quiz.find({
    subject: { $in: user.subjects },
    _id: { $nin: completedQuizzes },
    isActive: true,
  })
    .populate("subject")
    .populate("topic")
    .select("-questions.correctAnswer")
    .limit(5);

  res.json({
    success: true,
    data: recommendations,
  });
});

// @desc    Start a subject-wise quiz for a user
// @route   POST /api/quizzes/subject/:subjectId/start
// @access  Private
const startSubjectQuiz = asyncHandler(async (req, res) => {
  const subjectId = req.params.subjectId;
  const userId = req.user._id;
  const subject = await Subject.findById(subjectId).populate("topics");
  if (!subject)
    return res
      .status(404)
      .json({ success: false, errors: [{ msg: "Subject not found" }] });
  const user = await User.findById(userId).select("quizHistory");
  const correctlyAnsweredIds = new Set();
  user.quizHistory.forEach((qh) => {
    if (qh.answers && Array.isArray(qh.answers)) {
      qh.answers.forEach((ans) => {
        if (ans.isCorrect) correctlyAnsweredIds.add(ans.questionId.toString());
      });
    }
  });
  let selectedQuestions = [];
  // At least one per topic
  for (const topic of subject.topics) {
    const q = await Question.findOne({
      topic: topic._id,
      isActive: true,
      _id: { $nin: Array.from(correctlyAnsweredIds) },
    });
    if (q) selectedQuestions.push(q);
  }
  // Fill up to 10 with randoms from all topics (still excluding previously correct)
  if (selectedQuestions.length < 10) {
    const allQs = await Question.find({
      subject: subjectId,
      isActive: true,
      _id: { $nin: Array.from(correctlyAnsweredIds) },
    });

    console.log(`Found ${allQs.length} questions for subject ${subjectId}`);
    const extra = allQs.filter(
      (q) => !selectedQuestions.some((sel) => sel._id.equals(q._id))
    );
    extra.sort(() => 0.5 - Math.random());
    selectedQuestions = selectedQuestions.concat(
      extra.slice(0, 10 - selectedQuestions.length)
    );
  }
  if (selectedQuestions.length < 1)
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Not enough new questions available for this subject" }],
    });
  selectedQuestions = selectedQuestions.slice(0, 10);
  const quiz = await Quiz.create({
    user: userId,
    subject: subjectId,
    mode: "subject-quiz",
    totalQuestions: selectedQuestions.length,
    questions: selectedQuestions.map((q) => ({ question: q._id })),
    status: "pending",
  });

  // Populate questions.question, but remove correct answer info from options
  const createdQuiz = await Quiz.findById(quiz._id)
    .populate("questions.question")
    .select(-"questions.question.options.isCorrect");

  res.status(201).json({ success: true, data: createdQuiz });
});

// @desc    Start a topic-wise quiz for a user
// @route   POST /api/quizzes/topic/:topicId/start
// @access  Private
const startTopicQuiz = asyncHandler(async (req, res) => {
  const topicId = req.params.topicId;
  const userId = req.user._id;
  const user = await User.findById(userId).select("quizHistory");
  const correctlyAnsweredIds = new Set();
  user.quizHistory.forEach((qh) => {
    if (qh.answers && Array.isArray(qh.answers)) {
      qh.answers.forEach((ans) => {
        if (ans.isCorrect) correctlyAnsweredIds.add(ans.questionId.toString());
      });
    }
  });
  const allQuestions = await Question.find({
    topic: topicId,
    isActive: true,
    _id: { $nin: Array.from(correctlyAnsweredIds) },
  });
  allQuestions.sort(() => 0.5 - Math.random());
  const selectedQuestions = allQuestions.slice(0, 10);
  if (selectedQuestions.length < 1)
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Not enough new questions available for this topic" }],
    });
  const quiz = await Quiz.create({
    user: userId,
    subject: selectedQuestions[0]?.subject,
    topic: topicId,
    mode: "topic-quiz",
    totalQuestions: selectedQuestions.length,
    questions: selectedQuestions.map((q) => ({ question: q._id })),
    status: "pending",
  });

  // Populate questions.question, but remove correct answer info from options
  const createdQuiz = await Quiz.findById(quiz._id)
    .populate("questions.question")
    .select(-"questions.question.options.isCorrect");
  res.status(201).json({ success: true, data: createdQuiz });
});

module.exports = {
  getQuizzes,
  getQuiz,
  createQuiz,
  updateQuiz,
  deleteQuiz,
  getQuizStats,
  getQuizAnalytics,
  getQuizQuestions,
  getQuizLeaderboard,
  getQuizProgress,
  submitQuiz,
  getQuizResults,
  getQuizHistory,
  getQuizRecommendations,
  startSubjectQuiz,
  startTopicQuiz,
};
