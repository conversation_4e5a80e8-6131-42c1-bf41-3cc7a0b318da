const { check, param, validationResult } = require('express-validator');
const mongoose = require('mongoose');

// Validation middleware to check for errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// Custom validator for MongoDB ObjectId
const isValidObjectId = (value) => {
  return mongoose.Types.ObjectId.isValid(value);
};

// Validate test ID param
const validateTestId = [
  param('id')
    .custom(isValidObjectId)
    .withMessage('Test ID must be a valid ObjectId'),
  handleValidationErrors
];

// Validate start test (can be extended if body fields are needed)
const validateStartTest = [
  handleValidationErrors
];

// Validate answer question
const validateAnswerQuestion = [
  check('questionId', 'Question ID is required').not().isEmpty(),
  check('questionId', 'Question ID must be a valid ObjectId').custom(isValidObjectId),
  check('answerIndex', 'Answer index is required').isInt({ min: 0 }),
  check('timeSpent', 'Time spent is required').isInt({ min: 0 }),
  handleValidationErrors
];

// Validate complete test (can be extended if body fields are needed)
const validateCompleteTest = [
  handleValidationErrors
];

// Validate start subject test
const validateStartSubjectTest = [
  param('subjectId')
    .custom(isValidObjectId)
    .withMessage('Subject ID must be a valid ObjectId'),
  handleValidationErrors
];

// Validate start topic test
const validateStartTopicTest = [
  param('topicId')
    .custom(isValidObjectId)
    .withMessage('Topic ID must be a valid ObjectId'),
  handleValidationErrors
];

// Validate start mixed test (no params, can be extended if needed)
const validateStartMixedTest = [
  handleValidationErrors
];

module.exports = {
  validateTestId,
  validateStartTest,
  validateAnswerQuestion,
  validateCompleteTest,
  validateStartSubjectTest,
  validateStartTopicTest,
  validateStartMixedTest,
  handleValidationErrors
}; 