"use client";
import { cn } from "@/lib/utils";
import React, { useState } from "react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "./ui/button";
import { Check, ChevronsUpDown } from "lucide-react";

const CustomDD = ({
  label,
  data,
  selectedItems,
  setSelectedItems,
  handleChange = () => {}, // Default to a no-op function
  singleSelection = false,
}) => {
  const [openDropDown, setOpenDropDown] = useState(false);

  return (
    <div className="grid gap-2">
      <label>{label}</label>
      <Popover open={openDropDown} onOpenChange={setOpenDropDown}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={openDropDown}
            className="w-[400px] justify-between @max-md:w-full"
          >
            {selectedItems.length > 0
              ? !singleSelection
                ? `${selectedItems.length} ${label} Selected`
                : selectedItems
              : `Select ${label}`}
            <ChevronsUpDown className="opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0 @max-md:w-full">
          <Command>
            <CommandInput placeholder={`Search ${label}...`} className="h-9" />
            <CommandList className="max-h-[300px] overflow-y-auto">
              <CommandEmpty>No {label} found.</CommandEmpty>
              <CommandGroup>
                {data.map((item) => {
                  const index =
                    !singleSelection &&
                    selectedItems.findIndex(
                      (selectedItem) => selectedItem._id === item._id
                    );
                  const isSelected = singleSelection
                    ? item._id === selectedItems
                    : index !== -1;

                  return (
                    <CommandItem
                      key={item._id}
                      value={item.name}
                      onSelect={() => {
                        setSelectedItems((prev) => {
                          if (singleSelection) {
                            return item._id;
                          }

                          const updated = isSelected
                            ? prev.filter(
                                (selectedItem) => selectedItem._id !== item._id
                              )
                            : [...prev, item];

                          handleChange(updated); // Always safe to call since it defaults to a no-op
                          return updated;
                        });
                      }}
                    >
                      {item.name}
                      <Check
                        className={cn(
                          "ml-auto",
                          isSelected ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default CustomDD;
