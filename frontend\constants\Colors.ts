/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

export const Colors = {
  white: 'rgb(255, 255, 255)',
  white2: 'rgb(230, 230, 230)',
  black: 'rgb(0, 0, 0)',
  grey6: 'rgb(29, 30, 28)',
  grey5: 'rgb(48, 50, 47)',
  grey4: 'rgb(61, 63, 59)',
  grey3: 'rgb(81, 85, 79)',
  grey2: 'rgb(124, 129, 121)',
  grey: 'rgb(162, 167, 160)',
  background: 'rgb(10, 10, 10)',
  foreground: 'rgb(249, 254, 247)',
  root: 'rgb(2, 4, 1)',
  card: 'rgb(2, 4, 1)',
  destructive: 'rgb(254, 67, 54)',
  primary: 'rgb(128, 224, 77)',
  secondary: 'rgb(155, 220, 148)',
  accent: 'rgb(86, 218, 220)',
};

// export const Colors = {
//   light: {
//     text: '#11181C',
//     background: '#fff',
//     tint: tintColorLight,
//     icon: '#687076',
//     tabIconDefault: '#687076',
//     tabIconSelected: tintColorLight,
//   },
//   dark: {
//     text: '#ECEDEE',
//     background: '#151718',
//     tint: tintColorDark,
//     icon: '#9BA1A6',
//     tabIconDefault: '#9BA1A6',
//     tabIconSelected: tintColorDark,
//   },
// };
