"use client";

import { ColumnDef } from "@tanstack/react-table";
import { CheckCircle, MoreHorizontal, Trash, XCircle, Eye } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { JSX } from "react";
import { deleteExData } from "@/server-actions/ExerciseRelated";
import { Exercise, BodyPart, Condition, ExerciseType } from "@/app/types";

// Define the shape of your data
export type User = {
  id: string;
  name: string;
  email: string;
};

// Define columns
export const columns: ColumnDef<User>[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
];

// We're using the types imported from @/app/types instead of this local type

const DeleteData = async (id: string, endPoint: string) => {
  const response = await deleteExData(id, endPoint);
  if (response?.success) {
    toast.success(" Deleted Successfully!");
  } else {
    toast.error("Something went wrong");
  }
};

const ActionMenu = ({
  DataId,
  endPoint,
  editComponent,
}: {
  DataId: string;
  endPoint: string;
  editComponent?: JSX.Element;
}) => {
  const router = useRouter();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="center"
        autoCapitalize="words"
        className="self-center text-center"
      >
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => {
            router.push(`/dashboard/exercises/${endPoint === 'exercises' ? '' : endPoint + '/'}${DataId}`);
          }}
        >
          <Eye className="h-4 w-4 mr-2" />
          View Details
        </DropdownMenuItem>

        {editComponent}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          asChild
          onClick={() => {
            DeleteData(DataId, endPoint);
            router.refresh();
          }}
        >
          <div className="flex justify-center">
            <Button variant="destructive">
              <Trash /> Delete
            </Button>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const BodyPartsColumns: ColumnDef<BodyPart>[] = [
  {
    accessorKey: "index",
    header: "No.",
    cell: ({ row }) => row.index + 1, // Add 1 to make it 1-based index
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: (row) => {
      const isActive = row.getValue();
      return isActive ? (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <CheckCircle className="h-4 w-4 text-green-500 " />
          Active
        </div>
      ) : (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <XCircle className="h-4 w-4 text-red-500 " />
          Inactive
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <ActionMenu
        DataId={row.original?._id}
        endPoint="bodyparts"
      />
    ),
  },
];

export const ExerciseTypesColumns: ColumnDef<ExerciseType>[] = [
  {
    accessorKey: "index",
    header: "No.",
    cell: ({ row }) => row.index + 1, // Add 1 to make it 1-based index
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "bodyParts",
    header: "Body Parts",
    cell: (row) => {
      const bodyParts = row.getValue();
      return (bodyParts as { name: string }[])
        .map((bodyPart: { name: string }) => bodyPart.name)
        .join(", ");
    },
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: (row) => {
      const isActive = row.getValue();
      return isActive ? (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <CheckCircle className="h-4 w-4 text-green-500 " />
          Active
        </div>
      ) : (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <XCircle className="h-4 w-4 text-red-500 " />
          Inactive
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <ActionMenu
        DataId={row.original?._id}
        endPoint="exercise-types"
      />
    ),
  },
];

export const CondtionsCol: ColumnDef<Condition>[] = [
  {
    accessorKey: "index",
    header: "No.",
    cell: ({ row }) => row.index + 1, // Add 1 to make it 1-based index
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: (row) => {
      const description = row.getValue();
      return (
        <div className=" text-sm ">
          {typeof description === "string"
            ? description.slice(0, 30)
            : "no description available"}
          {typeof description === "string" && description.length > 30 && (
            <span>...</span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "bodyParts",
    header: "Body Parts",
    cell: (row) => {
      const bodyParts = row.getValue();
      return (bodyParts as { name: string }[])
        .map((bodyPart: { name: string }) => bodyPart.name)
        .join(", ");
    },
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: (row) => {
      const isActive = row.getValue();
      return isActive ? (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <CheckCircle className="h-4 w-4 text-green-500 " />
          Active
        </div>
      ) : (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <XCircle className="h-4 w-4 text-red-500 " />
          Inactive
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <ActionMenu
        DataId={row.original?._id}
        endPoint="conditions"
      />
    ),
  },
];

export const ExerciseColumns: ColumnDef<Exercise>[] = [
  {
    accessorKey: "index",
    header: "No.",
    cell: ({ row }) => row.index + 1, // Add 1 to make it 1-based index
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "bodyParts",
    header: "Body Parts",
    cell: (row) => {
      const bodyParts = row.getValue();
      return (bodyParts as { name: string }[])
        .map((bodyPart: { name: string }) => bodyPart.name)
        .join(", ");
    },
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: (row) => {
      const isActive = row.getValue();
      return isActive ? (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <CheckCircle className="h-4 w-4 text-green-500 " />
          Active
        </div>
      ) : (
        <div className="flex items-center capitalize gap-2 my-2 ">
          <XCircle className="h-4 w-4 text-red-500 " />
          Inactive
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <ActionMenu
        DataId={row.original?._id}
        endPoint="exercises"
      />
    ),
  },
];
