"use client";

import { useActionState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { adminLogin, type LoginState } from "@/app/dashboard/login/actions";

export function LoginForm({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);
  
  // Initialize with proper state structure
  const initialState: LoginState = { 
    error: null, 
    success: false 
  };
  
  const [state, formAction, isPending] = useActionState(
    adminLogin,
    initialState
  );

  console.log("Current state:", state);
  console.log("Is pending:", isPending);

  // Handle state changes
  useEffect(() => {
    // Only show error if there's actually an error and we're not pending
    if (state?.error && !isPending) {
      toast.error(state.error);
    }
    
    // Success case - this might not be reached due to redirect in server action
    if (state?.success && !isPending) {
      toast.success("Login successful!");
      // Clear form
      formRef.current?.reset();
      // Navigate to dashboard
      router.push("/dashboard");
      router.refresh(); // Ensure fresh data
    }
  }, [state, isPending, router]);

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Admin Login</CardTitle>
          <CardDescription>
            Enter your credentials to access the admin dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form ref={formRef} action={formAction}>
            <div className="flex flex-col gap-6">
              {/* Error Display */}
              {state?.error && !isPending && (
                <div className="p-3 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                  {state.error}
                </div>
              )}
              
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  disabled={isPending}
                  autoComplete="email"
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  required
                  disabled={isPending}
                  autoComplete="current-password"
                />
              </div>
              
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isPending}
              >
                {isPending ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Logging in...
                  </div>
                ) : (
                  "Login"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-muted-foreground">
            Admin access only. Unauthorized access is prohibited.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}