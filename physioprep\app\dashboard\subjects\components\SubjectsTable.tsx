"use client";

import { useState, useTransition } from "react";
import { toast } from "sonner";
import { createSubjectColumns } from "@/components/datatable/columns/subjectColumns";
import { deleteSubject } from "../actions";
import { Subject } from "@/types/types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { EnhancedDataTable } from "@/components/datatable/EnhancedDataTable";

interface SubjectsTableProps {
  subjects: Subject[];
}

export function SubjectsTable({ subjects: initialSubjects }: SubjectsTableProps) {
  const [subjects, setSubjects] = useState<Subject[]>(initialSubjects);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subjectToDelete, setSubjectToDelete] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const handleDeleteClick = (id: string) => {
    setSubjectToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (!subjectToDelete) return;

    startTransition(async () => {
      try {
        const result = await deleteSubject(subjectToDelete);
        if (result.success) {
          setSubjects(prev => prev.filter(subject => subject._id !== subjectToDelete));
          toast.success("Subject deleted successfully");
        } else {
          toast.error(result.errors[0]?.msg || "Failed to delete subject");
        }
      } catch {
        toast.error("An unexpected error occurred");
      } finally {
        setDeleteDialogOpen(false);
        setSubjectToDelete(null);
      }
    });
  };

  const columns = createSubjectColumns(handleDeleteClick);

  return (
    <>
      <EnhancedDataTable
        columns={columns}
        data={subjects}
        searchPlaceholder="Search subjects by name, description..."
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the subject
              and all associated data including topics and questions.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
