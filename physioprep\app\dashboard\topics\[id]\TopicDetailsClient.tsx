"use client";

import { useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { useAtom, useSetAtom } from "jotai";
import { topicAtom, getTopicByIdAtom } from "@/store/topics";
import { lexicalToHtml } from "@/lib/editor-utils";

interface TopicDetailsClientProps {
  topicId: string;
}

export function TopicDetailsClient({ topicId }: TopicDetailsClientProps) {
  const [currentTopic] = useAtom(topicAtom);
  const getTopicById = useSetAtom(getTopicByIdAtom);


  // Initialize the current topic atom with the server-side data
  useEffect(() => {
    getTopicById(topicId);
  }, [topicId, getTopicById]);

  // Get the most up-to-date topic data
  const topic = currentTopic;

  if (!topic) {
    return (
      <div className="flex flex-col items-center justify-center h-96 text-center text-muted-foreground">
        <div className="mb-4">
          <Trash2 className="w-12 h-12 mx-auto text-gray-300" />
        </div>
        <h2 className="text-xl font-semibold mb-2">Topic Not Found</h2>
        <p className="mb-4">We couldn&#39;t find the topic you&#39;re looking for.</p>
        <Button variant="outline" asChild>
          <Link href="/dashboard/topics">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Topics
          </Link>
        </Button>
      </div>
    );
  }

  // Convert Lexical JSON to HTML for display
  const getDescriptionHtml = () => {
    if (!topic.description) return '';

    try {
      // Check if description is already HTML (legacy format)
      if (typeof topic.description === 'string' && topic.description.includes('<')) {
        return topic.description;
      }

      // Check if it's Lexical JSON format
      if (typeof topic.description === 'string' && topic.description.startsWith('{')) {
        return lexicalToHtml(topic.description);
      }

      // If it's plain text, wrap in paragraph
      return `<p>${topic.description}</p>`;
    } catch (error) {
      console.error('Error converting description:', error);
      // Fallback to plain text
      return `<p>${topic.description}</p>`;
    }
  };

  const descriptionHtml = getDescriptionHtml();

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/topics">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Topics
            </Link>
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/topics/${topic._id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
          <Button variant="destructive">
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Topic Header */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <CardTitle className="text-2xl">{topic.topicName}</CardTitle>
                  <Badge variant={topic.isActive ? "default" : "secondary"}>
                    {topic.isActive ? "Active" : "Inactive"}
                  </Badge>
                  <Badge variant={topic.isPremium ? "destructive" : "outline"}>
                    {topic.isPremium ? "Premium" : "Free"}
                  </Badge>
                </div>
                <CardDescription className="text-base">
                  Subject:{" "}
                  {typeof topic.subject === "object"
                    ? topic.subject.name
                    : topic.subject}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          {descriptionHtml && (
            <CardContent>
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <div
                  dangerouslySetInnerHTML={{
                    __html: descriptionHtml,
                  }}
                  className="rich-text-content space-y-4 leading-relaxed"
                  style={{
                    lineHeight: '1.7',
                    color: 'inherit'
                  }}
                />
              </div>
            </CardContent>
          )}
        </Card>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Questions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {topic.stats?.totalQuestions || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {topic.stats?.freeQuestions || 0} free,{" "}
                {topic.stats?.premiumQuestions || 0} premium
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Questions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {topic.stats?.activeQuestions || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {topic.stats?.inactiveQuestions || 0} inactive
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Average Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {topic.stats?.averageScore
                  ? `${Math.round(topic.stats.averageScore)}%`
                  : "-"}
              </div>
              <p className="text-xs text-muted-foreground">
                {topic.stats?.totalAttempts || 0} attempts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Difficulty</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {topic.stats?.averageDifficulty
                  ? topic.stats.averageDifficulty.toFixed(1)
                  : "-"}
              </div>
              <p className="text-xs text-muted-foreground">
                Popularity:{" "}
                {topic.stats?.popularityScore
                  ? Math.round(topic.stats.popularityScore)
                  : 0}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Difficulty Distribution */}
        {topic.stats?.difficultyDistribution && (
          <Card>
            <CardHeader>
              <CardTitle>Difficulty Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {topic.stats.difficultyDistribution.easy}
                  </div>
                  <p className="text-sm text-muted-foreground">Easy</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {topic.stats.difficultyDistribution.medium}
                  </div>
                  <p className="text-sm text-muted-foreground">Medium</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {topic.stats.difficultyDistribution.hard}
                  </div>
                  <p className="text-sm text-muted-foreground">Hard</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Topic Details */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Topic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Subject
                </label>
                <p className="text-sm">
                  {typeof topic.subject === "object"
                    ? topic.subject.name
                    : topic.subject}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Status
                </label>
                <p className="text-sm">
                  {topic.isActive ? "Active" : "Inactive"}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Tier
                </label>
                <p className="text-sm">
                  {topic.isPremium ? "Premium" : "Free"}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Created
                </label>
                <p className="text-sm">
                  {format(new Date(topic.createdAt), "PPP")}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Last Updated
                </label>
                <p className="text-sm">
                  {format(new Date(topic.updatedAt), "PPP")}
                </p>
              </div>
              {topic.stats?.lastActivityDate && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Last Activity
                  </label>
                  <p className="text-sm">
                    {format(new Date(topic.stats.lastActivityDate), "PPP")}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Questions</CardTitle>
                <Button size="sm" asChild>
                  <Link href={`/dashboard/questions/create?topic=${topic._id}`}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Question
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {topic.stats?.totalQuestions && topic.stats.totalQuestions > 0 ? (
                <div className="space-y-2">
                  <p className="text-sm">
                    This topic has <strong>{topic.stats.totalQuestions}</strong>{" "}
                    questions
                  </p>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      {topic.stats.freeQuestions} Free
                    </Badge>
                    <Badge variant="secondary">
                      {topic.stats.premiumQuestions} Premium
                    </Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="w-full mt-2"
                  >
                    <Link href={`/dashboard/questions?topic=${topic._id}`}>
                      View All Questions
                    </Link>
                  </Button>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  No questions found for this topic. Create some questions to
                  get started.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
