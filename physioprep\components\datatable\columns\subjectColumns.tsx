"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Edit, Trash2, Eye } from "lucide-react";
import { Subject } from "@/types/types";
import Link from "next/link";
import { format } from "date-fns";

interface SubjectActionsProps {
  subject: Subject;
  onDelete: (id: string) => void;
}

function SubjectActions({ subject, onDelete }: SubjectActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/dashboard/subjects/${subject._id}`}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/dashboard/subjects/${subject._id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => onDelete(subject._id)}
          className="text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const createSubjectColumns = (
  onDelete: (id: string) => void
): ColumnDef<Subject>[] => [
  {
    accessorKey: "index",
    header: "No.",
    cell: ({ row }) => row.index + 1, // Add 1 to make it 1-based index
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => {
      const subject = row.original;
      return (
        <div className="flex items-center space-x-2">
          {subject.color && (
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: subject.color }}
            />
          )}
          <div>
            <div className="font-medium">{subject.name}</div>
            {subject.slug && (
              <div className="text-sm text-muted-foreground">/{subject.slug}</div>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => {
      const description = row.getValue("description") as string;
      return (
        <div className="max-w-[300px] truncate" title={description}>
          {description}
        </div>
      );
    },
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      const isActive = row.getValue("isActive") as boolean;
      return (
        <Badge variant={isActive ? "default" : "secondary"}>
          {isActive ? "Active" : "Inactive"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "order",
    header: "Order",
    cell: ({ row }) => {
      const order = row.getValue("order") as number;
      return <div className="text-center">{order || 0}</div>;
    },
  },
  {
    accessorKey: "stats",
    header: "Questions",
    cell: ({ row }) => {
      const subject = row.original;
      const totalQuestions = subject.stats?.totalQuestions || 0;
      const freeQuestions = subject.stats?.freeQuestions || 0;
      const premiumQuestions = subject.stats?.premiumQuestions || 0;
      
      return (
        <div className="text-sm">
          <div className="font-medium">{totalQuestions} total</div>
          <div className="text-muted-foreground">
            {freeQuestions} free, {premiumQuestions} premium
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "topics",
    header: "Topics",
    cell: ({ row }) => {
      const topics = row.original.topics;
      const topicCount = Array.isArray(topics) ? topics.length : 0;
      return <div className="text-center">{topicCount}</div>;
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created",
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as string;
      return (
        <div className="text-sm text-muted-foreground">
          {format(new Date(createdAt), "MMM dd, yyyy")}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const subject = row.original;
      return <SubjectActions subject={subject} onDelete={onDelete} />;
    },
  },
];
