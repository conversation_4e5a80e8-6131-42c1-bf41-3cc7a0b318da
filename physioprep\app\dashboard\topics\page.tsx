import { Suspense } from "react";
import Link from "next/link";
import { Plus, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TopicsTable } from "./components/TopicsTable";
import { TopicsTableSkeleton } from "./components/TopicsTableSkeleton";
import { TopicsStats } from "./components/TopicsStats";
import { getTopics, getSubjectsForDropdown, TopicAdminStats } from "./actions";
import { RefreshButton } from "./components/RefreshButton";

export default async function TopicsPage() {
  try {
    // Fetch topics and subjects data
    const [topicsResult, subjectsResult] = await Promise.all([
      getTopics(),
      getSubjectsForDropdown(),
    ]);

    const topics = topicsResult.success ? topicsResult.data.topics : [];
    const subjects = subjectsResult.success ? subjectsResult.data : [];
    const adminStats = topicsResult.success
      ? topicsResult.data.adminStats
      : null;

    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center space-x-2 mb-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        <div className="flex items-center justify-between space-y-2">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Topics</h2>
            <p className="text-muted-foreground">
              Manage topics and their associated questions across different
              subjects.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <RefreshButton />
            <Button asChild>
              <Link href="/dashboard/topics/create">
                <Plus className="mr-2 h-4 w-4" />
                Add Topic
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Section */}
        <TopicsStats
          topics={topics}
          adminStats={adminStats as TopicAdminStats}
        />

        {/* Topics Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Topics</CardTitle>
            <CardDescription>
              A list of all topics in the system. You can view, edit, filter, or
              delete topics from here.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<TopicsTableSkeleton />}>
              <TopicsTable topics={topics} subjects={subjects} />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    );
  } catch (error) {
    console.error("Error loading topics page:", error);
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
            <p className="text-muted-foreground">
              Failed to load topics. Please try again later.
            </p>
          </div>
        </div>
      </div>
    );
  }
}
