const express = require('express');
const router = express.Router();

// Import controllers
const {
  adminLogin,
  adminLogout,
  getCurrentAdmin,
  refreshAdminToken
} = require('../controllers/adminAuthController');

// Import middleware
const { 
  adminAuth, 
  adminRateLimit 
} = require('../middleware/adminAuth');

// Import validators
const {
  validateAdminLogin,
  validateAdminRefreshToken
} = require('../validators/adminAuthValidators');

// @route   POST /api/auth/admin/login
// @desc    Admin login
// @access  Public
router.post('/login', 
  adminRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  validateAdminLogin, 
  adminLogin
);

// @route   POST /api/auth/admin/logout
// @desc    Admin logout
// @access  Private (Admin)
router.post('/logout', 
  adminAuth, 
  adminLogout
);

// @route   GET /api/auth/admin/me
// @desc    Get current admin user
// @access  Private (Admin)
router.get('/me', 
  adminAuth, 
  getCurrentAdmin
);

// @route   POST /api/auth/admin/refresh
// @desc    Refresh admin token
// @access  Public (with valid refresh token)
router.post('/refresh', 
  adminRateLimit(10, 15 * 60 * 1000), // 10 attempts per 15 minutes
  validateAdminRefreshToken, 
  refreshAdminToken
);

module.exports = router;
