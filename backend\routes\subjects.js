const express = require('express');
const router = express.Router();
const { body, param } = require('express-validator');

// Import controllers
const {
  getSubjects,
  getSubject,
  createSubject,
  updateSubject,
  deleteSubject,
  getSubjectStats,
  getSubjectAnalytics,
  getSubjectQuestions,
  getSubjectQuizzes,
  getSubjectTests,
  getSubjectLeaderboard,
  getSubjectProgress,
  getSubjectCategories,
  addSubjectCategory,
  updateSubjectCategory,
  deleteSubjectCategory
} = require('../controllers/subjectController');

const topicController = require('../controllers/topicController');

// Import middleware
const { auth, admin, optionalAuth } = require('../middleware/auth');
const { handleValidationErrors } = require('../validators/authValidators');

// Validation for subject creation/update
const validateSubject = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Subject name is required')
    .isLength({ max: 100 })
    .withMessage('Subject name cannot exceed 100 characters'),
  
  body('description')
    .trim()
    .notEmpty()
    .withMessage('Description is required'),
  
  body('icon')
    .optional()
    .trim(),
  
  body('color')
    .optional()
    .trim()
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('Color must be a valid hex code'),
  
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer'),
  
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  
  handleValidationErrors
];

// Validation for category operations
const validateCategory = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Category name is required'),
  
  body('description')
    .optional()
    .trim(),
  
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  
  handleValidationErrors
];

// Public routes (with optional auth to detect admin users)
router.get('/', optionalAuth, getSubjects);
router.get('/:id', param('id').isMongoId(), handleValidationErrors, getSubject);
router.get('/:id/stats', param('id').isMongoId(), handleValidationErrors, getSubjectStats);
router.get('/:id/questions', param('id').isMongoId(), handleValidationErrors, getSubjectQuestions);
router.get('/:id/quizzes', param('id').isMongoId(), handleValidationErrors, getSubjectQuizzes);
router.get('/:id/tests', param('id').isMongoId(), handleValidationErrors, getSubjectTests);
router.get('/:id/leaderboard', param('id').isMongoId(), handleValidationErrors, getSubjectLeaderboard);
router.get('/:id/progress', auth, param('id').isMongoId(), handleValidationErrors, getSubjectProgress);
router.get('/:id/categories', param('id').isMongoId(), handleValidationErrors, getSubjectCategories);

// Topic routes
router.get('/topics', topicController.getTopics);
router.get('/topics/:id', topicController.getTopic);
router.post('/topics', [
  body('topicName').notEmpty().withMessage('Topic name is required'),
  body('subject').notEmpty().withMessage('Subject is required')
], topicController.createTopic);
router.put('/topics/:id', [
  body('topicName').optional().notEmpty().withMessage('Topic name cannot be empty'),
  body('subject').optional().notEmpty().withMessage('Subject is required')
], topicController.updateTopic);
router.delete('/topics/:id', topicController.deleteTopic);

// Admin routes
router.post('/', auth, admin, validateSubject, createSubject);
router.put('/:id', auth, admin, param('id').isMongoId(), validateSubject, handleValidationErrors, updateSubject);
router.delete('/:id', auth, admin, param('id').isMongoId(), handleValidationErrors, deleteSubject);
router.get('/:id/analytics', auth, admin, param('id').isMongoId(), handleValidationErrors, getSubjectAnalytics);
router.post('/:id/categories', auth, admin, param('id').isMongoId(), validateCategory, handleValidationErrors, addSubjectCategory);
router.put('/:id/categories/:categoryId', auth, admin, param('id').isMongoId(), param('categoryId').isMongoId(), validateCategory, handleValidationErrors, updateSubjectCategory);
router.delete('/:id/categories/:categoryId', auth, admin, param('id').isMongoId(), param('categoryId').isMongoId(), handleValidationErrors, deleteSubjectCategory);

module.exports = router;
