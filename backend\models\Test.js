const mongoose = require('mongoose');

const testSchema = new mongoose.Schema({
  subject: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subject',
    required: true
  },
  questions: [{
    question: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
      required: true
    },
    userAnswer: {
      type: Number,
      default: null
    },
    isCorrect: {
      type: Boolean,
      default: null
    },
    timeSpent: {
      type: Number,
      default: 0 // in seconds
    }
  }],
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  timeLimit: {
    type: Number, // in minutes
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'abandoned'],
    default: 'pending'
  },
  score: {
    type: Number,
    default: 0
  },
  startedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  totalQuestions: {
    type: Number,
    default: 0
  },
  questionsAnswered: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Question'
  }],
  createdAt: {
      type: Date,
    default: Date.now
  },
  mode: {
    type: String,
    enum: ['subject-test', 'topic-test', 'mixed-test'],
    required: true
  },
  topic: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Topic',
    default: null
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard', 'mixed'],
    default: 'mixed',
    required: true
  },
});

// Calculate score before saving
testSchema.pre('save', function(next) {
  if (this.status === 'completed') {
    const correctAnswers = this.questions.filter(q => q.isCorrect).length;
    this.score = (correctAnswers / this.questions.length) * 100;
  }
  next();
});

// Method to start test
testSchema.methods.start = function() {
  this.status = 'in_progress';
  this.startedAt = new Date();
  return this.save();
};

// Method to complete test
testSchema.methods.complete = function() {
  this.status = 'completed';
  this.completedAt = new Date();
  return this.save();
};

// Method to abandon test
testSchema.methods.abandon = function() {
  this.status = 'abandoned';
  this.completedAt = new Date();
  return this.save();
};

// Method to answer a question
testSchema.methods.answerQuestion = async function(questionId, answerIndex, timeSpent) {
  const question = this.questions.id(questionId);
  if (!question) {
    throw new Error('Question not found in test');
  }

  question.userAnswer = answerIndex;
  question.timeSpent = timeSpent;
  
  // Get the question document to check correct answer
  const Question = mongoose.model('Question');
  const questionDoc = await Question.findById(questionId);
  
  question.isCorrect = questionDoc.options[answerIndex].isCorrect;
  
  return this.save();
};

const Test = mongoose.model('Test', testSchema);

module.exports = Test;
