"use client";
import React, { Children, cloneElement, useState } from "react";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";

function CustomDrawer({ Btn, title, description, children }) {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <Button>{Btn}</Button>
        </DrawerTrigger>
        <DrawerContent className="flex flex-col h-[90vh]">
          <DrawerHeader className="text-left shrink-0">
            <DrawerTitle>{title}</DrawerTitle>
            <DrawerDescription>{description}</DrawerDescription>
          </DrawerHeader>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto px-4">
            {Children.map(children, (child) =>
              React.isValidElement(child)
                ? cloneElement(child, { setDrawer: setOpen })
                : child
            )}
          </div>

          <DrawerFooter className="pt-2 shrink-0">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
}

export default CustomDrawer;
