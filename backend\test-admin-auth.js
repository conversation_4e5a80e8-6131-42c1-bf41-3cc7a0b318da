/**
 * Simple test script for admin authentication endpoints
 * Run this script to verify that the admin authentication system is working correctly
 * 
 * Prerequisites:
 * 1. Server must be running
 * 2. Database must be connected
 * 3. At least one user with role 'admin' must exist in the database
 * 
 * Usage: node test-admin-auth.js
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000/api';
const ADMIN_EMAIL = '<EMAIL>'; // Change this to your admin email
const ADMIN_PASSWORD = 'admin123'; // Change this to your admin password

// Test data storage
let adminToken = '';
let refreshToken = '';

// Helper function to make API requests
const apiRequest = async (method, endpoint, data = null, headers = {}) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
};

// Test functions
const testAdminLogin = async () => {
  console.log('\n🔐 Testing Admin Login...');
  
  const result = await apiRequest('POST', '/auth/admin/login', {
    email: ADMIN_EMAIL,
    password: ADMIN_PASSWORD
  });

  if (result.success && result.data.success) {
    adminToken = result.data.data.token;
    refreshToken = result.data.data.refreshToken;
    console.log('✅ Admin login successful');
    console.log(`   Token: ${adminToken.substring(0, 20)}...`);
    console.log(`   User: ${result.data.data.user.name} (${result.data.data.user.email})`);
    console.log(`   Role: ${result.data.data.user.role}`);
    return true;
  } else {
    console.log('❌ Admin login failed');
    console.log('   Error:', result.error);
    return false;
  }
};

const testGetCurrentAdmin = async () => {
  console.log('\n👤 Testing Get Current Admin...');
  
  if (!adminToken) {
    console.log('❌ No admin token available');
    return false;
  }

  const result = await apiRequest('GET', '/auth/admin/me', null, {
    'Authorization': `Bearer ${adminToken}`
  });

  if (result.success && result.data.success) {
    console.log('✅ Get current admin successful');
    console.log(`   User: ${result.data.data.user.name} (${result.data.data.user.email})`);
    console.log(`   Role: ${result.data.data.user.role}`);
    console.log(`   Last Login: ${result.data.data.user.lastLogin}`);
    return true;
  } else {
    console.log('❌ Get current admin failed');
    console.log('   Error:', result.error);
    return false;
  }
};

const testRefreshToken = async () => {
  console.log('\n🔄 Testing Refresh Token...');
  
  if (!refreshToken) {
    console.log('❌ No refresh token available');
    return false;
  }

  const result = await apiRequest('POST', '/auth/admin/refresh', {
    refreshToken: refreshToken
  });

  if (result.success && result.data.success) {
    const newToken = result.data.data.token;
    const newRefreshToken = result.data.data.refreshToken;
    console.log('✅ Token refresh successful');
    console.log(`   New Token: ${newToken.substring(0, 20)}...`);
    console.log(`   New Refresh Token: ${newRefreshToken.substring(0, 20)}...`);
    
    // Update tokens for further tests
    adminToken = newToken;
    refreshToken = newRefreshToken;
    return true;
  } else {
    console.log('❌ Token refresh failed');
    console.log('   Error:', result.error);
    return false;
  }
};

const testAdminLogout = async () => {
  console.log('\n🚪 Testing Admin Logout...');
  
  if (!adminToken) {
    console.log('❌ No admin token available');
    return false;
  }

  const result = await apiRequest('POST', '/auth/admin/logout', null, {
    'Authorization': `Bearer ${adminToken}`
  });

  if (result.success && result.data.success) {
    console.log('✅ Admin logout successful');
    console.log(`   Message: ${result.data.data.message}`);
    return true;
  } else {
    console.log('❌ Admin logout failed');
    console.log('   Error:', result.error);
    return false;
  }
};

const testInvalidCredentials = async () => {
  console.log('\n🚫 Testing Invalid Credentials...');
  
  const result = await apiRequest('POST', '/auth/admin/login', {
    email: '<EMAIL>',
    password: 'wrongpassword'
  });

  if (!result.success || !result.data.success) {
    console.log('✅ Invalid credentials properly rejected');
    console.log(`   Status: ${result.status}`);
    return true;
  } else {
    console.log('❌ Invalid credentials were accepted (this is a problem!)');
    return false;
  }
};

const testNonAdminUser = async () => {
  console.log('\n👥 Testing Non-Admin User Access...');
  
  // This test assumes you have a regular user account
  // You may need to modify the email/password or skip this test
  const result = await apiRequest('POST', '/auth/admin/login', {
    email: '<EMAIL>', // Change to a regular user email
    password: 'user123' // Change to the user's password
  });

  if (!result.success || !result.data.success) {
    console.log('✅ Non-admin user properly rejected');
    console.log(`   Status: ${result.status}`);
    return true;
  } else {
    console.log('❌ Non-admin user was allowed access (this is a problem!)');
    return false;
  }
};

// Main test runner
const runTests = async () => {
  console.log('🧪 Starting Admin Authentication Tests');
  console.log('=====================================');
  
  const tests = [
    { name: 'Admin Login', fn: testAdminLogin },
    { name: 'Get Current Admin', fn: testGetCurrentAdmin },
    { name: 'Refresh Token', fn: testRefreshToken },
    { name: 'Get Current Admin (with new token)', fn: testGetCurrentAdmin },
    { name: 'Admin Logout', fn: testAdminLogout },
    { name: 'Invalid Credentials', fn: testInvalidCredentials },
    { name: 'Non-Admin User Access', fn: testNonAdminUser }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
  }

  console.log('\n📊 Test Results');
  console.log('================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! Admin authentication system is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the implementation.');
  }
};

// Run the tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
