import { useEffect, useState, useRef } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useLocalSearchParams, useNavigation, useRouter } from 'expo-router';
import { quizApi } from 'services/api';
import { Button } from 'components/ui/button';
import { useAtom } from 'jotai';
import {
  quizAtom,
  loadingQuizAtom,
  errorQuizAtom,
  currentQuizIndexAtom,
  quizAnswersAtom,
} from 'store/quiz';
import QuizHeader from 'components/quiz/QuizHeader';
import AnswerOption from 'components/quiz/AnswerOption';
import SubmissionModal from 'components/quiz/SubmissionModal';
import JumpToQuestionModal from 'components/quiz/JumpToQuestionModal';
import QuizReview from 'components/questions/QuizReview';
import { Ionicons } from '@expo/vector-icons';
import { handleError } from 'lib/utils';
import { CustomHeader } from '~/common/CustomHeader';

export default function SubjectQuizPage() {
  const { id: subjectId } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const [quiz, setQuiz] = useAtom(quizAtom);
  const [loading, setLoading] = useAtom(loadingQuizAtom);
  const [error, setError] = useAtom(errorQuizAtom);
  const [currentIndex, setCurrentIndex] = useAtom(currentQuizIndexAtom);
  const [answers, setAnswers] = useAtom(quizAnswersAtom);
  const [submitting, setSubmitting] = useState(false);
  const [showReview, setShowReview] = useState(false);
  const [showSubmissionModal, setShowSubmissionModal] = useState(false);
  const [result, setResult] = useState<{ score: number; totalQuestions: number } | null>(null);
  const [elapsed, setElapsed] = useState(0);
  const [timerActive, setTimerActive] = useState(true);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const navigation = useNavigation();
  const [showJumpModal, setShowJumpModal] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      header: () => (
        <CustomHeader title={`${result ? 'Result Of The Quiz' : 'Subject Quiz'} `} showBack />
      ),
    });
  }, [navigation, result]);

  // Fetch quiz
  useEffect(() => {
    if (!subjectId) return;
    setLoading(true);
    setError(null);

    const fetchQuiz = async () => {
      try {
        const res = await quizApi.startSubjectQuiz(subjectId as string);
        console.log(JSON.stringify(res.data.data, null, 2));
        setQuiz(res.data.data);
        setAnswers([]);
        setCurrentIndex(0);
        setElapsed(0);
        setTimerActive(true);
      } catch (err) {
        handleError(err);
      } finally {
        setLoading(false);
      }
    };
    fetchQuiz();
    // eslint-disable-next-line
  }, [subjectId]);

  // Timer logic
  useEffect(() => {
    if (timerActive) {
      timerRef.current = setInterval(() => {
        setElapsed((prev) => prev + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [timerActive]);

  // Handlers
  const handleSelect = (optionIdx: number) => {
    const newAnswers = [...answers];
    newAnswers[currentIndex] = optionIdx;
    setAnswers(newAnswers);
  };

  const handleNext = () => {
    if (currentIndex < (quiz?.questions.length || 0) - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleSubmit = async () => {
    if (!quiz) return;
    setTimerActive(false);
    setSubmitting(true);
    try {
      const res = await quizApi.submitQuiz(quiz._id, { answers, timeSpent: elapsed });
      setResult({ score: res.data.data.score, totalQuestions: res.data.data.totalQuestions });
      console.log(res.data.data)
      setShowSubmissionModal(true);      
    } catch (err) {
      handleError(err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleJumpTo = (idx: number) => {
    setCurrentIndex(idx);
    setShowJumpModal(false);
  };

  // UI
  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-background">
        <ActivityIndicator size="large" />
      </View>
    );
  }
  if (error) {
    return (
      <View className="flex-1 items-center justify-center bg-background p-6">
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" className="mb-2" />
        <Text className="text-lg text-red-500 mb-2">{error}</Text>
        <Button title="Retry" onPress={() => router.dismissAll()} className="mt-4" />
      </View>
    );
  }
  if (!quiz) return null;

  if (showReview && result) {
    const reviewQuestions = quiz.questions.map((q, i) => ({
      ...q,
      isCorrect:
        typeof q.isCorrect === 'boolean'
          ? q.isCorrect
          : answers[i] === q.question.options.findIndex((o) => o.isCorrect),
      userAnswer: answers[i],
    }));
    return (
      <QuizReview
        totalTime={elapsed}
        questions={reviewQuestions}
        userAnswers={answers}
        onBack={() => router.back()}
      />
    );
  }

  const q = quiz.questions[currentIndex]?.question;
  return (
    <View className="flex-1 bg-background p-4">
      <QuizHeader current={currentIndex} total={quiz.questions.length} elapsed={elapsed} />
      <View className="mb-4 rounded-2xl bg-indigo-500 p-4 shadow">
        <Text className="mb-2 text-lg font-bold text-primary">
          Question {currentIndex + 1} / {quiz.questions.length}
        </Text>
        <Text className="mb-4 text-lg leading-6 text-foreground">{q.text}</Text>
        {q.options.map((opt, idx) => (
          <AnswerOption
            key={idx}
            text={opt.text}
            selected={answers[currentIndex] === idx}
            onPress={() => handleSelect(idx)}
            disabled={submitting}
          />
        ))}
      </View>
      <View className="mt-6 flex-row items-center justify-between">
        <Button title="Previous" onPress={handlePrev} disabled={currentIndex === 0 || submitting} />
        <Button
          title="Jump to Question"
          onPress={() => setShowJumpModal(true)}
          disabled={submitting}
          className="mx-2 py-2"
        />
        {currentIndex === quiz.questions.length - 1 ? (
          <Button
            title={submitting ? 'Submitting...' : 'Submit'}
            onPress={handleSubmit}
            disabled={submitting || answers.length < quiz.questions.length}
          />
        ) : (
          <Button
            title="Next"
            onPress={handleNext}
            disabled={answers[currentIndex] == null || submitting}
          />
        )}
      </View>
      <JumpToQuestionModal
        visible={showJumpModal}
        currentIndex={currentIndex}
        totalQuestions={quiz.questions.length}
        onJump={handleJumpTo}
        onClose={() => setShowJumpModal(false)}
        submitting={submitting}
      />
      <SubmissionModal
        visible={!!result && showSubmissionModal}
        onClose={() => {
          setShowSubmissionModal(false);
          setShowReview(true); // Automatically show review after submission
        }}
        score={result?.score || 0}
        total={result?.totalQuestions || 0}
        time={elapsed}
      />
    </View>
  );
}
