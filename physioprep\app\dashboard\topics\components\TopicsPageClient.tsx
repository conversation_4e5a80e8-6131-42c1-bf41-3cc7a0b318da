"use client";

import { useEffect } from "react";
import Link from "next/link";
import { Plus, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TopicsTable } from "./TopicsTable";
import { RefreshButton } from "./RefreshButton";
import { useAtom } from "jotai";
import { topicsAtom } from "@/store/topics";
import { Topic } from "@/types/types";

interface TopicsPageClientProps {
  initialTopics: Topic[];
  subjects: any[];
  adminStats: any;
}

export function TopicsPageClient({ initialTopics, subjects, adminStats }: TopicsPageClientProps) {
  const [topics, setTopics] = useAtom(topicsAtom);

  // Initialize the topics atom with server-side data
  useEffect(() => {
    setTopics(initialTopics);
  }, [initialTopics, setTopics]);

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-2 mb-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Topics</h2>
          <p className="text-muted-foreground">
            Manage topics and their associated questions across different
            subjects.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <RefreshButton />
          <Button asChild>
            <Link href="/dashboard/topics/create">
              <Plus className="mr-2 h-4 w-4" />
              Add Topic
            </Link>
          </Button>
        </div>
      </div>

      {/* Admin Statistics */}
      {adminStats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Topics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adminStats.totalTopics}</div>
              <p className="text-xs text-muted-foreground">
                {adminStats.activeTopics} active, {adminStats.inactiveTopics} inactive
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adminStats.totalQuestions}</div>
              <p className="text-xs text-muted-foreground">
                {adminStats.freeQuestions} free, {adminStats.premiumQuestions} premium
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Attempts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adminStats.totalAttempts}</div>
              <p className="text-xs text-muted-foreground">
                Avg popularity: {Math.round(adminStats.averagePopularity || 0)}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Premium Topics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adminStats.premiumTopics}</div>
              <p className="text-xs text-muted-foreground">
                {adminStats.freeTopics} free topics
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Topics</CardTitle>
          <CardDescription>
            A list of all topics in your system. You can create, edit, and manage topics here.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TopicsTable topics={topics} />
        </CardContent>
      </Card>
    </div>
  );
}
