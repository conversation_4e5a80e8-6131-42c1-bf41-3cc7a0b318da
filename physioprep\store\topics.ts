import { Topic } from "@/types/types";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// Base atoms
const topicAtom = atom<Topic | null>(null);
const topicsAtom = atomWithStorage<Topic[]>("topics", []);

// Atom to get a specific topic by ID from the topics list
const getTopicByIdAtom = atom(null, (get, set, id: string) => {
  const topics = get(topicsAtom);
  set(topicAtom, topics.find((topic) => topic._id === id) ?? null);
});


export {
  topicAtom,
  topicsAtom,
  getTopicByIdAtom,
  };
