const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Subject = require('../models/Subject');
const Topic = require('../models/Topic');
const Question = require('../models/Question');
const User = require('../models/User');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Dummy subjects data with enhanced topic descriptions
const subjects = [
  {
    name: 'Anatomy',
    slug: 'anatomy',
    description: 'Comprehensive study of the structure and organization of the human body, including bones, muscles, organs, and tissues.',
    icon: 'body',
    color: '#FF6B6B',
    isActive: true,
    order: 1,
    categories: [
      { name: 'Musculoskeletal', description: 'Bones, joints, and muscles', isActive: true },
      { name: 'Organ Systems', description: 'Internal organs and systems', isActive: true }
    ],
    topics: [
      {
        topicName: 'Skeletal System',
        description: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"","text":"Comprehensive study of bones and joints","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Key topics include:","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Bone structure and composition","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Joint classifications and movements","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Bone development and repair","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1}],"direction":"ltr","format":"","indent":0,"listType":"bullet","start":1,"tag":"ul","type":"list","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}',
        isActive: true,
        isPremium: false
      },
      {
        topicName: 'Muscular System',
        description: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"","text":"Study of muscles and movement mechanics","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"This topic covers muscle types, contraction mechanisms, and movement patterns essential for physiotherapy practice.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}',
        isActive: true,
        isPremium: true
      }
    ]
  },
  {
    name: 'Physiology',
    slug: 'physiology',
    description: 'In-depth study of how the human body functions, including cellular processes, organ systems, and physiological responses.',
    icon: 'heart',
    color: '#4ECDC4',
    isActive: true,
    order: 2,
    categories: [
      { name: 'Cardiovascular', description: 'Heart and circulation', isActive: true },
      { name: 'Respiratory', description: 'Breathing and gas exchange', isActive: true }
    ],
    topics: [
      {
        topicName: 'Cardiovascular System',
        description: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"","text":"Heart and blood vessel function","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Understanding cardiac cycle, blood pressure regulation, and circulation patterns.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}',
        isActive: true,
        isPremium: false
      },
      {
        topicName: 'Respiratory System',
        description: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"","text":"Breathing mechanics and gas exchange","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Covers ventilation, perfusion, and respiratory control mechanisms.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}',
        isActive: true,
        isPremium: true
      }
    ]
  },
  {
    name: 'Pathology',
    slug: 'pathology',
    description: 'Study of diseases, their causes, mechanisms, and effects on the human body.',
    icon: 'medical',
    color: '#45B7D1',
    isActive: true,
    order: 3,
    categories: [
      { name: 'Infectious', description: 'Bacterial and viral diseases', isActive: true },
      { name: 'Chronic', description: 'Long-term conditions', isActive: true }
    ],
    topics: [
      {
        topicName: 'Infectious Diseases',
        description: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"","text":"Bacterial and viral infections","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Study of pathogenic organisms and their impact on human health.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}',
        isActive: true,
        isPremium: false
      },
      {
        topicName: 'Chronic Conditions',
        description: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"","text":"Long-term health conditions","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"Understanding chronic diseases and their management in physiotherapy.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}',
        isActive: true,
        isPremium: true
      }
    ]
  }
];

// Default admin credentials
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPass123!'; // Change as needed

// Utility function to generate realistic question text
const generateQuestionText = (tier, difficulty, topicName, questionNumber) => {
  const questionTypes = {
    easy: [
      `Which of the following best describes the basic function of the ${topicName.toLowerCase()}?`,
      `What is the primary characteristic of ${topicName.toLowerCase()}?`,
      `Identify the main component of the ${topicName.toLowerCase()}:`
    ],
    medium: [
      `Analyze the relationship between ${topicName.toLowerCase()} and its surrounding structures:`,
      `What would be the most likely outcome if the ${topicName.toLowerCase()} was compromised?`,
      `Compare and contrast the different aspects of ${topicName.toLowerCase()}:`
    ],
    hard: [
      `Evaluate the complex interactions within the ${topicName.toLowerCase()} system:`,
      `Synthesize your understanding of ${topicName.toLowerCase()} to explain this clinical scenario:`,
      `Apply advanced principles of ${topicName.toLowerCase()} to solve this problem:`
    ]
  };

  const questions = questionTypes[difficulty] || questionTypes.medium;
  const baseQuestion = questions[questionNumber % questions.length];

  return `${baseQuestion} (${tier.toUpperCase()} - ${difficulty.toUpperCase()})`;
};

// Utility function to generate realistic explanations
const generateExplanation = (tier, difficulty, topicName) => {
  const explanations = {
    easy: `This question tests basic understanding of ${topicName.toLowerCase()}. The correct answer demonstrates fundamental knowledge that is essential for physiotherapy practice.`,
    medium: `This ${tier} question requires intermediate knowledge of ${topicName.toLowerCase()}. Understanding this concept is crucial for clinical decision-making and patient assessment.`,
    hard: `This advanced ${tier} question challenges your comprehensive understanding of ${topicName.toLowerCase()}. It requires integration of multiple concepts and clinical reasoning skills.`
  };

  return explanations[difficulty] || explanations.medium;
};

// Function to generate dummy questions with enhanced fields and realistic content
const generateQuestions = (topicId, subjectId, adminId, tier, count, topicName = 'Topic') => {
  const questions = [];
  const difficulties = ['easy', 'medium', 'hard'];

  // Weight difficulties: more easy questions, fewer hard ones
  const difficultyWeights = {
    easy: 0.5,    // 50% easy
    medium: 0.35, // 35% medium
    hard: 0.15    // 15% hard
  };

  for (let i = 1; i <= count; i++) {
    // Select difficulty based on weights
    const rand = Math.random();
    let difficulty;
    if (rand < difficultyWeights.easy) {
      difficulty = 'easy';
    } else if (rand < difficultyWeights.easy + difficultyWeights.medium) {
      difficulty = 'medium';
    } else {
      difficulty = 'hard';
    }

    // Generate realistic usage statistics
    const baseUsage = difficulty === 'easy' ? 80 : difficulty === 'medium' ? 50 : 30;
    const usageCount = Math.floor(Math.random() * 50) + baseUsage; // Varied usage based on difficulty

    // Success rate varies by difficulty and tier
    const baseSuccessRate = difficulty === 'easy' ? 0.75 : difficulty === 'medium' ? 0.60 : 0.45;
    const tierBonus = tier === 'premium' ? 0.05 : 0; // Premium questions slightly easier
    const successRate = Math.min(0.95, baseSuccessRate + tierBonus + (Math.random() * 0.2 - 0.1));
    const correctAnswerCount = Math.floor(usageCount * successRate);

    // Time spent varies by difficulty
    const baseTime = difficulty === 'easy' ? 45 : difficulty === 'medium' ? 75 : 120;
    const averageTimeSpent = Math.floor(baseTime + (Math.random() * 30 - 15)); // ±15 seconds variation

    questions.push({
      text: generateQuestionText(tier, difficulty, topicName, i),
      options: [
        { text: `Correct answer for ${difficulty} ${tier} question`, isCorrect: true },
        { text: `Plausible but incorrect option A`, isCorrect: false },
        { text: `Plausible but incorrect option B`, isCorrect: false },
        { text: `Clearly incorrect distractor`, isCorrect: false }
      ],
      explanation: generateExplanation(tier, difficulty, topicName),
      topic: topicId,
      subject: subjectId,
      createdBy: adminId,
      difficulty: difficulty,
      tier: tier,
      isActive: Math.random() > 0.05, // 95% active questions
      usageCount: usageCount,
      correctAnswerCount: correctAnswerCount,
      averageTimeSpent: averageTimeSpent,
      lastAttemptDate: new Date(Date.now() - Math.floor(Math.random() * 60 * 24 * 60 * 60 * 1000)), // Random date within last 60 days
      tags: [
        tier,
        difficulty,
        topicName.toLowerCase().replace(/\s+/g, '-'),
        `question-set-${Math.ceil(i / 5)}`, // Group questions in sets of 5
        Math.random() > 0.7 ? 'high-yield' : 'standard',
        Math.random() > 0.8 ? 'clinical' : 'theoretical'
      ]
    });
  }
  return questions;
};

// Main seeding function
const seedData = async () => {
  try {
    // Clear existing data
    await Subject.deleteMany({});
    await Topic.deleteMany({});
    await Question.deleteMany({});
    await User.deleteMany({ email: adminEmail }); // Remove old admin if exists

    // Create default admin user if not exists
    let adminUser = await User.findOne({ email: adminEmail });
    if (!adminUser) {
      adminUser = await User.create({
        name: 'Admin',
        email: adminEmail,
        password: adminPassword,
        role: 'admin',
        isEmailVerified: true
      });
      console.log('Default admin user created:', adminEmail);
    } else {
      console.log('Admin user already exists:', adminEmail);
    }
    const adminId = adminUser._id;
    console.log('Seeding data...');

    // Insert subjects (without topics field) with enhanced statistics initialization
    const createdSubjects = await Subject.insertMany(
      subjects.map(({ topics, ...rest }) => ({
        ...rest,
        createdBy: adminId,
        // Initialize enhanced statistics
        stats: {
          totalTopics: 0,
          activeTopics: 0,
          premiumTopics: 0,
          totalQuestions: 0,
          freeQuestions: 0,
          premiumQuestions: 0,
          activeQuestions: 0,
          difficultyDistribution: {
            easy: 0,
            medium: 0,
            hard: 0
          },
          averageDifficulty: 0,
          totalAttempts: 0,
          averageScore: 0,
          totalQuizzes: 0,
          totalTests: 0,
          popularityScore: 0,
          lastActivityDate: new Date(),
          lastCalculated: new Date()
        }
      }))
    );

    console.log('Subjects created successfully');

    // Create topics and questions for each subject, then update subject.topics
    for (const [i, subject] of createdSubjects.entries()) {
      const subjectTopics = subjects[i].topics;
      const createdTopics = [];
      for (const topicData of subjectTopics) {
        const topic = new Topic({
          ...topicData,
          subject: subject._id,
          // Initialize enhanced statistics
          stats: {
            totalQuestions: 0,
            freeQuestions: 0,
            premiumQuestions: 0,
            activeQuestions: 0,
            inactiveQuestions: 0,
            averageDifficulty: 0,
            difficultyDistribution: {
              easy: 0,
              medium: 0,
              hard: 0
            },
            totalAttempts: 0,
            averageScore: 0,
            completionRate: 0,
            popularityScore: 0,
            lastActivityDate: new Date(),
            lastCalculated: new Date()
          }
        });
        await topic.save();
        createdTopics.push(topic._id);
      }
      // Update subject's topics array with ObjectIds
      subject.topics = createdTopics;
      await subject.save();
      // For each topic, create questions
      for (let j = 0; j < createdTopics.length; j++) {
        const topicId = createdTopics[j];
        const topicName = subjects[i].topics[j].topicName;

        // Create 15 free questions with varied difficulty
        const freeQuestions = generateQuestions(topicId, subject._id, adminId, 'free', 15, topicName);
        await Question.insertMany(freeQuestions);

        // Create 10 premium questions with varied difficulty
        const premiumQuestions = generateQuestions(topicId, subject._id, adminId, 'premium', 10, topicName);
        await Question.insertMany(premiumQuestions);

        console.log(`Created 25 questions for topic: ${topicName}`);
      }

      console.log(`Completed subject: ${subject.name}`);
    }

    console.log('All topics and questions created successfully');

    // Calculate comprehensive statistics for all topics and subjects
    console.log('Calculating comprehensive statistics...');

    const allTopics = await Topic.find({});
    for (const topic of allTopics) {
      await topic.calculateStats();
      console.log(`Updated stats for topic: ${topic.topicName}`);
    }

    const allSubjects = await Subject.find({});
    for (const subject of allSubjects) {
      if (typeof subject.updateQuestionStats === 'function') {
        await subject.updateQuestionStats();
        console.log(`Updated stats for subject: ${subject.name}`);
      }
    }

    console.log('Statistics calculation completed!');

    // Display summary statistics
    const totalQuestions = await Question.countDocuments();
    const totalTopics = await Topic.countDocuments();
    const totalSubjects = await Subject.countDocuments();
    const freeQuestions = await Question.countDocuments({ tier: 'free' });
    const premiumQuestions = await Question.countDocuments({ tier: 'premium' });

    console.log('\n=== SEEDING SUMMARY ===');
    console.log(`✅ Subjects created: ${totalSubjects}`);
    console.log(`✅ Topics created: ${totalTopics}`);
    console.log(`✅ Questions created: ${totalQuestions}`);
    console.log(`   - Free questions: ${freeQuestions}`);
    console.log(`   - Premium questions: ${premiumQuestions}`);
    console.log(`✅ Admin user: ${adminEmail}`);
    console.log(`✅ Admin password: ${adminPassword}`);
    console.log('======================\n');

    console.log('🎉 Data seeding completed successfully!');

  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    mongoose.disconnect();
  }
};

// Run the seeding function
seedData(); 