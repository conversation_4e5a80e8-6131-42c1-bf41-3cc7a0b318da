import { TouchableOpacity, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function AnswerOption({
  text,
  selected,
  onPress,
  disabled,
}: {
  text: string;
  selected: boolean;
  onPress: () => void;
  disabled?: boolean;
}) {
  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.85}
      disabled={disabled}
      className={`mb-2 flex-row items-center rounded-xl border px-4 py-3 ${selected ? 'border-primary bg-primary' : 'border-gray-200 bg-white'} ${disabled ? 'opacity-60' : ''}`}
      style={{
        shadowColor: selected ? '#6366F1' : '#000',
        shadowOpacity: selected ? 0.15 : 0.05,
        shadowRadius: 8,
        shadowOffset: { width: 0, height: 2 },
        elevation: selected ? 3 : 1,
      }}>
      <Ionicons
        name={selected ? 'radio-button-on' : 'ellipse-outline'}
        size={22}
        color={selected ? '#fff' : '#6366F1'}
        style={{ marginRight: 12 }}
      />
      <Text
        className={`flex-1 text-base ${selected ? 'font-bold text-white' : 'font-medium text-gray-900'}`}>
        {text}
      </Text>
    </TouchableOpacity>
  );
} 