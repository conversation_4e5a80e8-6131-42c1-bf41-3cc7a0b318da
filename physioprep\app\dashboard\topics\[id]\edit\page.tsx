import { notFound } from "next/navigation";
import { TopicForm } from "../../components/TopicForm";
import { getTopicById } from "../../actions";

interface EditTopicPageProps {
  params: {
    id: string;
  };
}

export default async function EditTopicPage({ params }: EditTopicPageProps) {
  const result = await getTopicById(params.id);

  if (!result.success) {
    notFound();
  }

  return <TopicForm topic={result.data} mode="edit" />;
}
