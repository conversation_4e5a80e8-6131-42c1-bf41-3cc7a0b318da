"use client";

import {
  Color,
  EditorB<PERSON><PERSON>,
  Editor<PERSON><PERSON>nt,
  Editor<PERSON><PERSON>ance,
  EditorRoot,
  handleCommandNavigation,
  HighlightExtension,
  JSONContent,
  TextStyle,
  TiptapImage,
  TiptapUnderline,
  Youtube,
} from "novel";
import React from "react";

import { defaultExtensions } from "./extentions";
import { slashCommand, suggestionItems } from "./slash-command";
import {
  EditorCommand,
  EditorCommandEmpty,
  EditorCommandItem,
  EditorCommandList,
} from "novel";
import { EditorView } from "prosemirror-view";
import { NodeSelector } from "./selectors/node-selector";
import { LinkSelector } from "./selectors/link-selector";
import { TextButtons } from "./selectors/text-buttons";
import { ColorSelector } from "./selectors/color-selector";

const extensions = [
  ...defaultExtensions,
  slashCommand,
  Color,
  TextStyle,
  HighlightExtension,
  TiptapUnderline,
  TiptapImage,
  Youtube,
];

interface NovelEditorProps {
  data: JSONContent;
  setData: (json: JSONContent) => void;
}

const NovelEditor: React.FC<NovelEditorProps> = ({ data, setData }) => {
  const [openNode, setOpenNode] = React.useState(false);
  const [openLink, setOpenLink] = React.useState(false);
  const [openColor, setOpenColor] = React.useState(false);
  const [openAI, setOpenAI] = React.useState(false);

  return (
    <EditorRoot>
      <EditorContent
        initialContent={data}
        onUpdate={({ editor }: { editor: EditorInstance }) => {
          const json = editor.getJSON();
          setData(json);
        }}
        extensions={extensions}
        className="p-4 min-h-[200px] prose max-w-none focus:outline-none [&_.ProseMirror]:min-h-[150px] [&_.ProseMirror]:focus:outline-none"
        editorProps={{
          handleDOMEvents: {
            keydown: (_view: EditorView, event: KeyboardEvent) =>
              handleCommandNavigation(event),
          },
          attributes: {
            class:
              "prose prose-lg dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full",
          },
        }}
      >
        <EditorBubble
          tippyOptions={{
            placement: openAI ? "bottom-start" : "top",
          }}
          className="flex w-fit max-w-[90vw] overflow-hidden rounded border border-muted bg-background shadow-xl"
        >
          <NodeSelector open={openNode} onOpenChange={setOpenNode} />
          <LinkSelector open={openLink} onOpenChange={setOpenLink} />
          <TextButtons />
          <ColorSelector open={openColor} onOpenChange={setOpenColor} />
        </EditorBubble>
        <EditorCommand className="z-50 h-auto max-h-[330px]  w-72 overflow-y-auto rounded-md border border-muted bg-background px-1 py-2 shadow-md transition-all">
          <EditorCommandEmpty className="px-2 text-muted-foreground">
            No results
          </EditorCommandEmpty>
          <EditorCommandList>
            {suggestionItems.map((item) => (
              <EditorCommandItem
                value={item.title}
                onCommand={({
                  editor,
                  range,
                }: {
                  editor: EditorInstance;
                  range: { from: number; to: number };
                }) => {
                  if (item.command) {
                    item.command({ editor, range });
                  }
                }}
                className="flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-accent aria-selected:bg-accent"
                key={item.title}
              >
                <div className="flex h-10 w-10 items-center justify-center rounded-md border border-muted bg-background">
                  {item.icon}
                </div>
                <div>
                  <p className="font-medium">{item.title}</p>
                  <p className="text-xs text-muted-foreground">
                    {item.description}
                  </p>
                </div>
              </EditorCommandItem>
            ))}
          </EditorCommandList>
        </EditorCommand>
      </EditorContent>
    </EditorRoot>
  );
};

export default NovelEditor;
