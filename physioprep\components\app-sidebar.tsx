"use client"

import * as React from "react"
import {
  AudioWaveform,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  Library,
  FolderOpen,
  HelpCircle,
  PlayCircle,
  Timer,
  Calendar,
  BarChart3,
  Users,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Acme Inc",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: Command,
      plan: "Free",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: SquareTerminal,
      isActive: true,
      items: [
        {
          title: "Overview",
          url: "/dashboard",
        },
        {
          title: "Progress",
          url: "/dashboard/progress",
        },
        {
          title: "Analytics",
          url: "/dashboard/analytics",
        },
      ],
    },
    {
      title: "Subjects",
      url: "/dashboard/subjects",
      icon: Library,
      items: [
        {
          title: "All Subjects",
          url: "/dashboard/subjects",
        },
        {
          title: "Create Subject",
          url: "/dashboard/subjects/create",
        },
        {
          title: "Subject Analytics",
          url: "/dashboard/subjects/analytics",
        },
        {
          title: "Categories",
          url: "/dashboard/subjects/categories",
        },
      ],
    },
    {
      title: "Topics",
      url: "/dashboard/topics",
      icon: FolderOpen,
      items: [
        {
          title: "All Topics",
          url: "/dashboard/topics",
        },
        {
          title: "Create Topic",
          url: "/dashboard/topics/create",
        },
        {
          title: "Topic Management",
          url: "/dashboard/topics/manage",
        },
        {
          title: "By Subject",
          url: "/dashboard/topics/by-subject",
        },
      ],
    },
    {
      title: "Questions",
      url: "/dashboard/questions",
      icon: HelpCircle,
      items: [
        {
          title: "All Questions",
          url: "/dashboard/questions",
        },
        {
          title: "Create Question",
          url: "/dashboard/questions/create",
        },
        {
          title: "By Topic",
          url: "/dashboard/questions/by-topic",
        },
        {
          title: "Random Questions",
          url: "/dashboard/questions/random",
        },
        {
          title: "Question Bank",
          url: "/dashboard/questions/bank",
        },
      ],
    },
    {
      title: "Quizzes",
      url: "/dashboard/quizzes",
      icon: PlayCircle,
      items: [
        {
          title: "All Quizzes",
          url: "/dashboard/quizzes",
        },
        {
          title: "Create Quiz",
          url: "/dashboard/quizzes/create",
        },
        {
          title: "Subject Quizzes",
          url: "/dashboard/quizzes/subject",
        },
        {
          title: "Topic Quizzes",
          url: "/dashboard/quizzes/topic",
        },
        {
          title: "Quiz Analytics",
          url: "/dashboard/quizzes/analytics",
        },
        {
          title: "Leaderboard",
          url: "/dashboard/quizzes/leaderboard",
        },
      ],
    },
    {
      title: "Tests",
      url: "/dashboard/tests",
      icon: Timer,
      items: [
        {
          title: "All Tests",
          url: "/dashboard/tests",
        },
        {
          title: "Create Test",
          url: "/dashboard/tests/create",
        },
        {
          title: "Subject Tests",
          url: "/dashboard/tests/subject",
        },
        {
          title: "Topic Tests",
          url: "/dashboard/tests/topic",
        },
        {
          title: "Mixed Tests",
          url: "/dashboard/tests/mixed",
        },
        {
          title: "Test Results",
          url: "/dashboard/tests/results",
        },
      ],
    },
    {
      title: "Daily Questions",
      url: "/dashboard/daily-questions",
      icon: Calendar,
      items: [
        {
          title: "Today's Question",
          url: "/dashboard/daily-questions/today",
        },
        {
          title: "Question History",
          url: "/dashboard/daily-questions/history",
        },
        {
          title: "Statistics",
          url: "/dashboard/daily-questions/stats",
        },
        {
          title: "Leaderboard",
          url: "/dashboard/daily-questions/leaderboard",
        },
        {
          title: "Manage Questions",
          url: "/dashboard/daily-questions/manage",
        },
      ],
    },
    {
      title: "Analytics",
      url: "/dashboard/analytics",
      icon: BarChart3,
      items: [
        {
          title: "Overview",
          url: "/dashboard/analytics",
        },
        {
          title: "User Analytics",
          url: "/dashboard/analytics/users",
        },
        {
          title: "Performance",
          url: "/dashboard/analytics/performance",
        },
        {
          title: "Reports",
          url: "/dashboard/analytics/reports",
        },
      ],
    },
    {
      title: "User Management",
      url: "/dashboard/users",
      icon: Users,
      items: [
        {
          title: "All Users",
          url: "/dashboard/users",
        },
        {
          title: "Admins",
          url: "/dashboard/users/admins",
        },
        {
          title: "Premium Users",
          url: "/dashboard/users/premium",
        },
        {
          title: "User Activity",
          url: "/dashboard/users/activity",
        },
      ],
    },
    {
      title: "Settings",
      url: "/dashboard/settings",
      icon: Settings2,
      items: [
        {
          title: "General",
          url: "/dashboard/settings/general",
        },
        {
          title: "Security",
          url: "/dashboard/settings/security",
        },
        {
          title: "API Keys",
          url: "/dashboard/settings/api-keys",
        },
        {
          title: "Backup",
          url: "/dashboard/settings/backup",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Design Engineering",
      url: "#",
      icon: Frame,
    },
    {
      name: "Sales & Marketing",
      url: "#",
      icon: PieChart,
    },
    {
      name: "Travel",
      url: "#",
      icon: Map,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
