const nodemailer = require('nodemailer');

// Email templates
const emailTemplates = {
  emailVerification: (data) => ({
    subject: 'Verify Your PhysioPrep Account',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #FF6B6B; margin: 0; font-size: 28px;">PhysioPrep</h1>
            <p style="color: #666; margin: 5px 0 0 0;">Exam Preparation Platform</p>
          </div>
          
          <h2 style="color: #333; margin-bottom: 20px;">Welcome, ${data.name}!</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            Thank you for joining PhysioPrep! To complete your registration and start your exam preparation journey, 
            please verify your email address by clicking the button below.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.verificationUrl}" 
               style="background-color: #FF6B6B; color: white; padding: 15px 30px; text-decoration: none; 
                      border-radius: 5px; font-weight: bold; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            If the button doesn't work, you can copy and paste this link into your browser:
          </p>
          <p style="color: #00FFFF; word-break: break-all; margin-bottom: 25px;">
            ${data.verificationUrl}
          </p>
          
          <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
            <p style="color: #999; font-size: 14px; margin: 0;">
              If you didn't create an account with PhysioPrep, you can safely ignore this email.
            </p>
          </div>
        </div>
      </div>
    `,
    text: `
      Welcome to PhysioPrep, ${data.name}!
      
      Please verify your email address by visiting: ${data.verificationUrl}
      
      If you didn't create an account with PhysioPrep, you can safely ignore this email.
    `
  }),

  passwordReset: (data) => ({
    subject: 'Reset Your PhysioPrep Password',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #FF6B6B; margin: 0; font-size: 28px;">PhysioPrep</h1>
            <p style="color: #666; margin: 5px 0 0 0;">Exam Preparation Platform</p>
          </div>
          
          <h2 style="color: #333; margin-bottom: 20px;">Password Reset Request</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            Hi ${data.name},
          </p>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            We received a request to reset your password for your PhysioPrep account. 
            Click the button below to create a new password:
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.resetUrl}" 
               style="background-color: #FF6B6B; color: white; padding: 15px 30px; text-decoration: none; 
                      border-radius: 5px; font-weight: bold; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            If the button doesn't work, you can copy and paste this link into your browser:
          </p>
          <p style="color: #00FFFF; word-break: break-all; margin-bottom: 25px;">
            ${data.resetUrl}
          </p>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 25px 0;">
            <p style="color: #856404; margin: 0; font-size: 14px;">
              <strong>Security Notice:</strong> This password reset link will expire in 10 minutes for your security.
            </p>
          </div>
          
          <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
            <p style="color: #999; font-size: 14px; margin: 0;">
              If you didn't request a password reset, you can safely ignore this email. 
              Your password will remain unchanged.
            </p>
          </div>
        </div>
      </div>
    `,
    text: `
      Password Reset Request - PhysioPrep
      
      Hi ${data.name},
      
      We received a request to reset your password. Visit this link to create a new password:
      ${data.resetUrl}
      
      This link will expire in 10 minutes for your security.
      
      If you didn't request a password reset, you can safely ignore this email.
    `
  }),

  welcomePremium: (data) => ({
    subject: 'Welcome to PhysioPrep Premium!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #FF6B6B; margin: 0; font-size: 28px;">PhysioPrep</h1>
            <p style="color: #00FFFF; margin: 5px 0 0 0; font-weight: bold;">PREMIUM</p>
          </div>
          
          <h2 style="color: #333; margin-bottom: 20px;">Welcome to Premium!</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            Congratulations ${data.name}! Your PhysioPrep Premium subscription is now active.
          </p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 25px 0;">
            <h3 style="color: #333; margin-top: 0;">Premium Features Now Available:</h3>
            <ul style="color: #666; line-height: 1.8;">
              <li>Access to all premium questions and tests</li>
              <li>Advanced analytics and performance tracking</li>
              <li>Unlimited quiz attempts</li>
              <li>Priority customer support</li>
              <li>Exclusive study materials</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/dashboard" 
               style="background-color: #00FFFF; color: #0F0F0F; padding: 15px 30px; text-decoration: none; 
                      border-radius: 5px; font-weight: bold; display: inline-block;">
              Start Learning
            </a>
          </div>
          
          <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
            <p style="color: #999; font-size: 14px; margin: 0;">
              Thank you for choosing PhysioPrep Premium. We're here to help you succeed!
            </p>
          </div>
        </div>
      </div>
    `,
    text: `
      Welcome to PhysioPrep Premium!
      
      Congratulations ${data.name}! Your Premium subscription is now active.
      
      Premium Features:
      - Access to all premium questions and tests
      - Advanced analytics and performance tracking
      - Unlimited quiz attempts
      - Priority customer support
      - Exclusive study materials
      
      Start learning: ${process.env.FRONTEND_URL}/dashboard
    `
  })
};

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_PORT == 465, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Send email function
const sendEmail = async ({ to, subject, template, data, html, text }) => {
  try {
    const transporter = createTransporter();

    let emailContent = {};

    if (template && emailTemplates[template]) {
      emailContent = emailTemplates[template](data);
    } else if (html || text) {
      emailContent = { subject, html, text };
    } else {
      throw new Error('Either template or html/text content must be provided');
    }

    const mailOptions = {
      from: `PhysioPrep <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to,
      subject: emailContent.subject || subject,
      html: emailContent.html,
      text: emailContent.text
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully:', result.messageId);
    return result;

  } catch (error) {
    console.error('❌ Email sending failed:', error);
    throw error;
  }
};

// Send bulk emails
const sendBulkEmail = async (recipients, { subject, template, data, html, text }) => {
  try {
    const results = [];
    
    for (const recipient of recipients) {
      try {
        const result = await sendEmail({
          to: recipient.email,
          subject,
          template,
          data: { ...data, ...recipient },
          html,
          text
        });
        results.push({ email: recipient.email, success: true, messageId: result.messageId });
      } catch (error) {
        results.push({ email: recipient.email, success: false, error: error.message });
      }
    }

    return results;
  } catch (error) {
    console.error('❌ Bulk email sending failed:', error);
    throw error;
  }
};

module.exports = {
  sendEmail,
  sendBulkEmail,
  emailTemplates
};
