import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import Link from "next/link";
import { headers } from "next/headers";

export const metadata = {
  title: "PhysioPrep-Dashboard",
  description: "Dashboard",
};

const DashboardLayout = async ({ children }) => {
  // Get the current path from headers
  const headerList = await headers();
  const pathname = headerList.get("x-current-path");
  console.log('pathname', pathname);

  if (pathname === "/dashboard/login") {
    return <>{children}</>;
  }

  const deCamelCase = (str) => {
    return str
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase());
  };

  return (
    <>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  {pathname?.split("/").map((path, index) => {
                    if (path === "") {
                      return null;
                    }
                    if (index === 0) {
                      return (
                        <BreadcrumbItem key={index}>
                          <Link href="/dashboard">Dashboard</Link>
                        </BreadcrumbItem>
                      );
                    }
                    if (index === pathname.split("/").length - 1) {
                      return (
                        <BreadcrumbItem key={index}>
                          <BreadcrumbPage className="capitalize">
                            {deCamelCase(path)}
                          </BreadcrumbPage>
                        </BreadcrumbItem>
                      );
                    }
                    return (
                      <div key={index} className="flex items-center gap-2">
                        <BreadcrumbItem>
                          <BreadcrumbPage className="capitalize">
                            {deCamelCase(path)}
                          </BreadcrumbPage>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator className="hidden md:block" />
                      </div>
                    );
                  })}
                  {/* <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href={pathname}>
                      {pathname}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>{pathname}</BreadcrumbPage>
                  </BreadcrumbItem> */}
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>

          {children}
        </SidebarInset>
      </SidebarProvider>
    </>
  );
};

export default DashboardLayout;
