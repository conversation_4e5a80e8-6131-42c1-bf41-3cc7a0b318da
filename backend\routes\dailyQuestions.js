const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');

// Import controllers
const {
  getDailyQuestion,
  submitDailyAnswer,
  getDailyStats,
  getDailyHistory,
  getDailyLeaderboard,
  getTodaysDailyQuestion,
  getDailyQuestionForDate,
  getUserHistory,
  getStatistics,
  getAnalytics,
  createDailyQuestionsRange,
  updateTodaysDailyQuestion
} = require('../controllers/dailyQuestionController');

// Import middleware
const { 
  auth,
  admin,
  premium,
  checkPremiumAccess,
  optionalAuth,
  rateLimitByUser
} = require('../middleware/auth');

const { handleValidationErrors } = require('../validators/authValidators');

// Import validators
const {
  validateSubmitDailyAnswer
} = require('../validators/dailyQuestionValidators');

// Validation for date parameter
const validateDate = [
  param('date')
    .isISO8601()
    .withMessage('Date must be in ISO 8601 format (YYYY-MM-DD)'),
  
  handleValidationErrors
];

// Validation for creating daily questions range
const validateCreateRange = [
  body('startDate')
    .isISO8601()
    .withMessage('Start date must be in ISO 8601 format'),
  
  body('endDate')
    .isISO8601()
    .withMessage('End date must be in ISO 8601 format')
    .custom((endDate, { req }) => {
      if (new Date(endDate) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  handleValidationErrors
];

// Validation for statistics query
const validateStatsQuery = [
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be in ISO 8601 format'),
  
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be in ISO 8601 format'),
  
  query('tier')
    .optional()
    .isIn(['free', 'premium'])
    .withMessage('Tier must be either free or premium'),
  
  handleValidationErrors
];

// Validation for analytics query
const validateAnalyticsQuery = [
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365'),
  
  handleValidationErrors
];

// Regular user routes
router.get('/', auth, getDailyQuestion);
router.post('/submit', auth, validateSubmitDailyAnswer, submitDailyAnswer);
router.get('/stats', auth, getDailyStats);
router.get('/history', auth, getDailyHistory);
router.get('/leaderboard', auth, getDailyLeaderboard);

// Premium user routes
router.get('/today', optionalAuth, checkPremiumAccess, getTodaysDailyQuestion);
router.post('/today/submit', 
  auth,
  checkPremiumAccess,
  rateLimitByUser(5, 15 * 60 * 1000), // 5 submissions per 15 minutes
  validateSubmitDailyAnswer,
  submitDailyAnswer
);
router.get('/date/:date', 
  optionalAuth, 
  checkPremiumAccess, 
  validateDate, 
  getDailyQuestionForDate
);

// Admin routes
router.get('/admin/stats', 
  auth, 
  admin, 
  validateStatsQuery, 
  getStatistics
);
router.get('/admin/analytics', 
  auth, 
  admin, 
  validateAnalyticsQuery, 
  getAnalytics
);
router.post('/admin/create-range', 
  auth, 
  admin, 
  validateCreateRange, 
  createDailyQuestionsRange
);
router.post('/admin/update-today', 
  auth, 
  admin, 
  updateTodaysDailyQuestion
);

module.exports = router;
