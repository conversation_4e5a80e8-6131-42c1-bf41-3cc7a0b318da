import { View, Text } from 'react-native';
import { ProgressBar } from 'components/ProgressBar';
import { Ionicons } from '@expo/vector-icons';
import colors from 'tailwindcss/colors';

export const TimerDisplay = ({ seconds }: { seconds: number }) => (
  <View className="flex-row items-center">
    <Ionicons name="timer-outline" size={20} color={colors.indigo[500]} />
    <Text className="ml-1 text-base font-semibold text-primary">{`${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')}`}</Text>
  </View>
);

export default function QuizHeader({ current, total, elapsed }: { current: number; total: number; elapsed: number }) {
  return (
    <View className="mb-4 flex-row items-center justify-between">
      <ProgressBar
        value={((current + 1) / total) * 100}
        style={{ flex: 1, marginRight: 12 }}
        color={colors.indigo[500]}
      />
      <TimerDisplay seconds={elapsed} />
    </View>
  );
} 