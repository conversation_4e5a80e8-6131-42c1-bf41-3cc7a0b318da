const express = require('express');
const router = express.Router();

// Import controllers
const {
  getQuestions,
  getQuestionById,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  getQuestionsByTopic,
  getRandomQuestions
} = require('../controllers/questionController');

// Import middleware
const { auth, admin } = require('../middleware/auth');

// Import validators
const {
  validateCreateQuestion,
  validateUpdateQuestion
} = require('../validators/questionValidators');

// @route   GET /api/questions
// @desc    Get all questions
// @access  Private
router.get('/', auth, getQuestions);

// @route   GET /api/questions/:id
// @desc    Get question by ID
// @access  Private
router.get('/:id', auth, getQuestionById);

// @route   POST /api/questions
// @desc    Create new question
// @access  Private/Admin
router.post('/', auth, admin, validateCreateQuestion, createQuestion);

// @route   PUT /api/questions/:id
// @desc    Update question
// @access  Private/Admin
router.put('/:id', auth, admin, validateUpdateQuestion, updateQuestion);

// @route   DELETE /api/questions/:id
// @desc    Delete question
// @access  Private/Admin
router.delete('/:id', auth, admin, deleteQuestion);

// @route   GET /api/questions/topic/:topicId
// @desc    Get questions by topic
// @access  Private
router.get('/topic/:topicId', auth, getQuestionsByTopic);

// @route   GET /api/questions/random/:count
// @desc    Get random questions
// @access  Private
router.get('/random/:count', auth, getRandomQuestions);

module.exports = router;
