const { check, validationResult } = require("express-validator");

// Validation middleware to check for errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array(),
    });
  }
  next();
};

// User registration validation
const validateRegister = [
  check("name", "Name is required").not().isEmpty(),
  check("name", "Name must be between 2 and 50 characters").isLength({
    min: 2,
    max: 50,
  }),
  check("email", "Please include a valid email").isEmail(),
  check("password", "Password must be at least 8 characters").isLength({
    min: 8,
  }),
  check("password", "Password must contain at least one number").matches(/\d/),
  check(
    "password",
    "Password must contain at least one uppercase letter"
  ).matches(/[A-Z]/),
  check(
    "password",
    "Password must contain at least one lowercase letter"
  ).matches(/[a-z]/),
  handleValidationErrors,
];

// User login validation
const validateLogin = [
  check("email", "Please include a valid email").isEmail(),
  check("password", "Password is required").exists(),
  handleValidationErrors,
];

// Password reset request validation
const validatePasswordResetRequest = [
  check("email", "Please include a valid email").isEmail(),
  handleValidationErrors,
];

// Password reset validation
const validatePasswordReset = [
  check("token", "Reset token is required").not().isEmpty(),
  check("password", "Password must be at least 6 characters").isLength({
    min: 6,
  }),
  check("password", "Password must contain at least one number").matches(/\d/),
  check(
    "password",
    "Password must contain at least one uppercase letter"
  ).matches(/[A-Z]/),
  check(
    "password",
    "Password must contain at least one lowercase letter"
  ).matches(/[a-z]/),
  handleValidationErrors,
];

// Change password validation
const validateChangePassword = [
  check("currentPassword", "Current password is required").exists(),
  check("newPassword", "New password must be at least 6 characters").isLength({
    min: 6,
  }),
  check("newPassword", "New password must contain at least one number").matches(
    /\d/
  ),
  check(
    "newPassword",
    "New password must contain at least one uppercase letter"
  ).matches(/[A-Z]/),
  check(
    "newPassword",
    "New password must contain at least one lowercase letter"
  ).matches(/[a-z]/),
  handleValidationErrors,
];

// Update profile validation
const validateUpdateProfile = [
  check("name", "Name must be between 2 and 50 characters")
    .optional()
    .isLength({ min: 2, max: 50 }),
  check("preferences", "Preferences must be an object").optional().isObject(),
  check(
    "preferences.notifications",
    "Notifications preference must be a boolean"
  )
    .optional()
    .isBoolean(),
  check(
    "preferences.dailyReminder",
    "Daily reminder preference must be a boolean"
  )
    .optional()
    .isBoolean(),
  check("preferences.theme", "Theme must be light, dark, or auto")
    .optional()
    .isIn(["light", "dark", "auto"]),
  handleValidationErrors,
];

// Email verification validation
const validateEmailVerification = [
  check("token", "Verification token is required").not().isEmpty(),
  handleValidationErrors,
];

module.exports = {
  validateRegister,
  validateLogin,
  validatePasswordResetRequest,
  validatePasswordReset,
  validateChangePassword,
  validateUpdateProfile,
  validateEmailVerification,
  handleValidationErrors,
};
