import { Suspense } from "react";
import Link from "next/link";
import { Plus, ArrowLeft} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SubjectsTableSkeleton } from "./components/SubjectsTableSkeleton";
import { getSubjects } from "./actions";
import { RefreshButton } from "./components/RefreshButton";
import { SubjectsTable } from "./components/SubjectsTable";

export default async function SubjectsPage() {
  // Fetch subjects data to get stats
  const subjectsResult = await getSubjects();
  const subjects = subjectsResult.success ? subjectsResult.data.subjects : [];
  const adminStats = subjectsResult.success ? subjectsResult.data.adminStats : null;

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-2 mb-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Subjects</h2>
          <p className="text-muted-foreground">
            Manage subjects and their associated topics and questions.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <RefreshButton />
          <Button asChild>
            <Link href="/dashboard/subjects/create">
              <Plus className="mr-2 h-4 w-4" />
              Add Subject
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subjects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {adminStats?.totalSubjects ?? subjects.length}
            </div>
            <p className="text-xs text-muted-foreground">
              All subjects in the system
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subjects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {adminStats?.activeSubjects ?? subjects.filter(subject => subject.isActive).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently active subjects
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {adminStats?.totalQuestions ?? subjects.reduce((total, subject) => total + (subject.stats?.totalQuestions || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {adminStats ? `${adminStats.freeQuestions} free, ${adminStats.premiumQuestions} premium` : "Questions across all subjects"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Topics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {adminStats?.totalTopics ?? subjects.reduce((total, subject) => {
                if (Array.isArray(subject.topics)) {
                  return total + subject.topics.length;
                }
                return total;
              }, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {adminStats ? `${adminStats.activeTopics} active topics` : "Topics across all subjects"}
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Subjects</CardTitle>
          <CardDescription>
            A list of all subjects in the system. You can view, edit, or delete subjects from here.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<SubjectsTableSkeleton />}>
            <SubjectsTable subjects={subjects} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
