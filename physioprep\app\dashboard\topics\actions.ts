"use server";

import axios from "axios";
import { revalidatePath } from "next/cache";
import {
  getApiUrl,
  checkAuth,
  handleServerActionError,
  handleRedirectError,
} from "@/services/api";
import type { Topic, Subject, ApiResponse } from "@/types/types";

// Define the state types for form actions
export type TopicFormState = {
  error: string | null;
  success: boolean;
  message?: string;
  data?: Topic;
};

export type TopicCreateData = {
  topicName: string;
  description: string;
  subject: string;
  isActive?: boolean;
  isPremium?: boolean;
};

export type TopicUpdateData = TopicCreateData;

// Define admin stats type
export type TopicAdminStats = {
  totalTopics: number;
  activeTopics: number;
  inactiveTopics: number;
  premiumTopics: number;
  freeTopics: number;
  totalQuestions: number;
  freeQuestions: number;
  premiumQuestions: number;
  totalAttempts: number;
  averagePopularity: number;
};

// Define topics response type with admin stats
export type TopicsResponse = {
  topics: Topic[];
  adminStats?: TopicAdminStats;
};

// Get all topics
export async function getTopics(): Promise<ApiResponse<TopicsResponse>> {
  try {
    const authCheck = await checkAuth(true); // Require admin
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/topics`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: {
        topics: response.data.data.topics || response.data,
        adminStats: response.data.data.stats,
      },
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch topics"
    );
  }
}

// Get topic by ID
export async function getTopicById(id: string): Promise<ApiResponse<Topic>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/topics/${id}`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: response.data.data || response.data,
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch topic"
    );
  }
}

// Get all subjects for dropdown
export async function getSubjectsForDropdown(): Promise<
  ApiResponse<Subject[]>
> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/subjects`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: response.data.data || response.data,
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch subjects"
    );
  }
}

// Create new topic
export async function createTopic(
  prevState: TopicFormState,
  formData: FormData
): Promise<TopicFormState> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        error: "Admin authentication required",
        success: false,
      };
    }

    // Extract form data
    const topicName = formData.get("topicName") as string;
    const descriptionString = formData.get("description") as string;
    const subject = formData.get("subject") as string;
    const isActive = formData.get("isActive") === "on";
    const isPremium = formData.get("isPremium") === "on";

    // Validate required fields
    if (!topicName || !subject) {
      return {
        error: "Topic name and subject are required",
        success: false,
      };
    }

    const topicData: TopicCreateData = {
      topicName: topicName.trim(),
      description: descriptionString,
      subject: subject.trim(),
      isActive,
      isPremium,
    };

    const apiUrl = await getApiUrl();
    const response = await axios.post(`${apiUrl}/topics`, topicData, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
        "Content-Type": "application/json",
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/topics");
      return {
        error: null,
        success: true,
        message: "Topic created successfully",
        data: response.data.data,
      };
    }

    return {
      error: response.data.errors?.[0]?.msg || "Failed to create topic",
      success: false,
    };
  } catch (error) {
    await handleRedirectError(error as Error);

    console.log(error);
    const errorResult = await handleServerActionError(
      error as Error,
      "Failed to create topic"
    );
    return {
      error: errorResult.errors[0]?.msg || "Failed to create topic",
      success: false,
    };
  }
}

// Update existing topic
export async function updateTopic(
  id: string,
  prevState: TopicFormState,
  formData: FormData
): Promise<TopicFormState> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        error: "Admin authentication required",
        success: false,
      };
    }

    // Extract form data
    const topicName = formData.get("topicName") as string;
    const descriptionString = formData.get("description") as string;
    const subject = formData.get("subject") as string;
    const isActive = formData.get("isActive") === "on";
    const isPremium = formData.get("isPremium") === "on";

    // Validate required fields
    if (!topicName || !subject) {
      return {
        error: "Topic name and subject are required",
        success: false,
      };
    }

    const topicData: TopicUpdateData = {
      topicName: topicName.trim(),
      description: descriptionString,
      subject: subject.trim(),
      isActive,
      isPremium,
    };

    const apiUrl = await getApiUrl();
    const response = await axios.put(`${apiUrl}/topics/${id}`, topicData, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
        "Content-Type": "application/json",
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/topics");
      revalidatePath(`/dashboard/topics/${id}`);
      return {
        error: null,
        success: true,
        message: "Topic updated successfully",
        data: response.data.data,
      };
    }

    return {
      error: response.data.errors?.[0]?.msg || "Failed to update topic",
      success: false,
    };
  } catch (error) {
    await handleRedirectError(error as Error);

    const errorResult = await handleServerActionError(
      error as Error,
      "Failed to update topic"
    );
    return {
      error: errorResult.errors[0]?.msg || "Failed to update topic",
      success: false,
    };
  }
}

// Delete topic
export async function deleteTopic(
  id: string
): Promise<ApiResponse<{ message: string }>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.delete(`${apiUrl}/topics/${id}`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/topics");
      return {
        success: true,
        data: { message: "Topic deleted successfully" },
      };
    }

    return {
      success: false,
      errors: response.data.errors || [{ msg: "Failed to delete topic" }],
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to delete topic"
    );
  }
}
