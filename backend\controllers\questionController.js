const Question = require("../models/Question");
const { asyncHandler } = require("../middleware/errorHandler");
const Topic = require("../models/Topic");

// @desc    Get all questions
// @route   GET /api/questions
// @access  Private
const getQuestions = asyncHandler(async (req, res) => {
  const questions = await Question.find()
    .populate("topic")
    .select("-correctAnswer");

  res.json({
    success: true,
    data: questions,
  });
});

// @desc    Get question by ID
// @route   GET /api/questions/:id
// @access  Private
const getQuestionById = asyncHandler(async (req, res) => {
  const question = await Question.findById(req.params.id).populate("topic");

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  res.json({
    success: true,
    data: question,
  });
});

// @desc    Create new question
// @route   POST /api/questions
// @access  Private/Admin
const createQuestion = asyncHandler(async (req, res) => {
  const topic = await Topic.findById(req.body.topic);
  if (!topic) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Topic not found" }],
    });
  }
  const subject = topic.subject;
  const question = await Question.create({
    ...req.body,
    subject,
    createdBy: req.user._id,
  });

  // Update topic statistics immediately for admin users
  if (req.user.role === 'admin') {
    await topic.calculateStats();
  }

  const questions = await Question.find({ topic: question.topic }).populate(
    "topic"
  );
  res.status(201).json({
    success: true,
    data: questions,
  });
});

// @desc    Update question
// @route   PUT /api/questions/:id
// @access  Private/Admin
const updateQuestion = asyncHandler(async (req, res) => {
  const question = await Question.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  // Update topic statistics immediately for admin users
  if (req.user.role === 'admin') {
    const topic = await Topic.findById(question.topic);
    if (topic) {
      await topic.calculateStats();
    }
  }

  const questions = await Question.find({ topic: question.topic }).populate(
    "topic",
    "name"
  );
  res.json({
    success: true,
    data: questions,
  });
});

// @desc    Delete question
// @route   DELETE /api/questions/:id
// @access  Private/Admin
const deleteQuestion = asyncHandler(async (req, res) => {
  const question = await Question.findById(req.params.id);

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  const topicId = question.topic;
  await question.deleteOne(); // Use deleteOne instead of findByIdAndDelete

  // Update topic statistics immediately for admin users
  if (req.user.role === 'admin') {
    const topic = await Topic.findById(topicId);
    if (topic) {
      await topic.calculateStats();
    }
  }

  const questions = await Question.find({ topic: topicId }).populate(
    "topic",
    "name"
  );

  res.json({
    success: true,
    data: questions,
  });
});

// @desc    Get questions by topic
// @route   GET /api/questions/topic/:topicId
// @access  Private
const getQuestionsByTopic = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    search = "",
    difficulty = "",
    tier = "",
    sortBy = "createdAt",
    sortOrder = "desc",
  } = req.query;

  const filter = { topic: req.params.topicId };
  if (difficulty) filter.difficulty = difficulty;
  if (tier) filter.tier = tier;
  if (search) {
    filter.$or = [
      { text: { $regex: search, $options: "i" } },
      { "options.text": { $regex: search, $options: "i" } },
    ];
  }

  const sort = {};
  sort[sortBy] = sortOrder === "asc" ? 1 : -1;

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const [questions, total] = await Promise.all([
    Question.find(filter)
      .populate("topic", "name")
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit)),
    Question.countDocuments(filter),
  ]);

  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.json({
    success: true,
    data: {
      questions,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalQuestions: total,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit),
      },
    },
  });
});

// @desc    Get random questions
// @route   GET /api/questions/random/:count
// @access  Private
const getRandomQuestions = asyncHandler(async (req, res) => {
  const count = parseInt(req.params.count);
  const questions = await Question.aggregate([
    { $sample: { size: count } },
    {
      $lookup: {
        from: "topics",
        localField: "topic",
        foreignField: "_id",
        as: "topic",
      },
    },
    {
      $project: {
        correctAnswer: 0,
      },
    },
  ]);

  res.json({
    success: true,
    data: questions,
  });
});

// Get all questions with filtering and pagination
const getQuestionsFiltered = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    topic,
    category,
    difficulty,
    tier,
    search,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = req.query;

  // Build filter object
  const filter = { isActive: true };

  if (topic) filter.topic = topic;
  if (category) filter.category = category;
  if (difficulty) filter.difficulty = difficulty;

  // Apply tier filtering based on user's premium status
  if (tier) {
    filter.tier = tier;
  } else if (req.allowedTiers) {
    filter.tier = { $in: req.allowedTiers };
  }

  // Add search functionality
  if (search) {
    filter.$or = [
      { questionText: { $regex: search, $options: "i" } },
      { explanation: { $regex: search, $options: "i" } },
      { tags: { $in: [new RegExp(search, "i")] } },
    ];
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === "asc" ? 1 : -1;

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Execute query
  const [questions, total] = await Promise.all([
    Question.find(filter)
      .populate("topic", "name slug color")
      .populate("createdBy", "name")
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    Question.countDocuments(filter),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    success: true,
    data: {
      questions,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalQuestions: total,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit),
      },
    },
  });
});

// Get single question by ID
const getQuestion = asyncHandler(async (req, res) => {
  const question = await Question.findById(req.params.id)
    .populate("topic", "name slug color")
    .populate("createdBy", "name");

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  // Check if user has access to this question tier
  if (req.allowedTiers && !req.allowedTiers.includes(question.tier)) {
    return res.status(403).json({
      success: false,
      errors: [
        { msg: "Premium subscription required to access this question" },
      ],
    });
  }

  res.status(200).json({
    success: true,
    data: { question },
  });
});

// Submit answer to question
const submitAnswer = asyncHandler(async (req, res) => {
  const { selectedOptionId, timeSpent = 0 } = req.body;

  const question = await Question.findById(req.params.id);

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  // Check if user has access to this question tier
  if (req.allowedTiers && !req.allowedTiers.includes(question.tier)) {
    return res.status(403).json({
      success: false,
      errors: [
        { msg: "Premium subscription required to access this question" },
      ],
    });
  }

  // Check if selected option exists
  const selectedOption = question.options.id(selectedOptionId);
  if (!selectedOption) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Invalid option selected" }],
    });
  }

  // Check if answer is correct
  const isCorrect = selectedOption.isCorrect;
  const correctAnswer = question.getCorrectAnswer();

  // Update question stats
  await question.updateStats(isCorrect, timeSpent);

  // Update user stats if authenticated
  if (req.user) {
    req.user.stats.totalQuestionsAnswered += 1;
    if (isCorrect) {
      req.user.stats.correctAnswers += 1;
    }

    // Recalculate average score
    req.user.stats.averageScore = Math.round(
      (req.user.stats.correctAnswers / req.user.stats.totalQuestionsAnswered) *
        100
    );

    // Update activity streak
    await req.user.updateStreak();
    await req.user.save();
  }

  res.status(200).json({
    success: true,
    data: {
      isCorrect,
      correctAnswer: {
        id: correctAnswer._id,
        text: correctAnswer.text,
      },
      explanation: question.explanation,
      userStats: req.user
        ? {
            totalAnswered: req.user.stats.totalQuestionsAnswered,
            correctAnswers: req.user.stats.correctAnswers,
            accuracyPercentage: req.user.accuracyPercentage,
          }
        : null,
    },
  });
});

// Get questions for quiz/test
const getQuestionsForQuiz = asyncHandler(async (req, res) => {
  const { topic, category, difficulty, tier, count = 10 } = req.query;

  // Build filters
  const filters = {
    limit: parseInt(count),
    excludeIds: [],
  };

  if (topic) filters.topic = topic;
  if (category) filters.category = category;
  if (difficulty) filters.difficulty = difficulty;

  // Apply tier filtering based on user's premium status
  if (tier) {
    filters.tier = tier;
  } else if (req.allowedTiers) {
    filters.tier = req.allowedTiers.includes("premium") ? null : "free";
  }

  const questions = await Question.getForQuizTest(filters);

  if (questions.length === 0) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "No questions found matching the criteria" }],
    });
  }

  res.status(200).json({
    success: true,
    data: { questions },
  });
});

// Bulk delete questions (Admin only)
const bulkDeleteQuestions = asyncHandler(async (req, res) => {
  const { questionIds } = req.body;

  const result = await Question.deleteMany({
    _id: { $in: questionIds },
    createdBy: req.user._id, // Only allow deleting own questions unless admin
  });

  res.status(200).json({
    success: true,
    data: {
      message: `${result.deletedCount} questions deleted successfully`,
      deletedCount: result.deletedCount,
    },
  });
});

// Bulk update questions (Admin only)
const bulkUpdateQuestions = asyncHandler(async (req, res) => {
  const { questionIds, updates } = req.body;

  const result = await Question.updateMany(
    { _id: { $in: questionIds } },
    updates,
    { runValidators: true }
  );

  res.status(200).json({
    success: true,
    data: {
      message: `${result.modifiedCount} questions updated successfully`,
      modifiedCount: result.modifiedCount,
    },
  });
});

// Get daily question
const getDailyQuestion = asyncHandler(async (req, res) => {
  try {
    // Get a random question that hasn't been used recently
    const question = await Question.aggregate([
      {
        $match: {
          isActive: true,
          tier: req.user.isPremiumActive()
            ? { $in: ["free", "premium"] }
            : "free",
        },
      },
      { $sample: { size: 1 } },
    ]);

    if (!question.length) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "No questions available" }],
      });
    }

    res.json({
      success: true,
      data: question[0],
    });
  } catch (error) {
    console.error("Get daily question error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching daily question" }],
    });
  }
});

module.exports = {
  getQuestions,
  getQuestionById,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  getQuestionsByTopic,
  getRandomQuestions,
  getQuestionsFiltered,
  getQuestion,
  submitAnswer,
  getQuestionsForQuiz,
  bulkDeleteQuestions,
  bulkUpdateQuestions,
  getDailyQuestion,
};
